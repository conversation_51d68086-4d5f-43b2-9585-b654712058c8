openapi: 3.0.0
info:
  title: GraphNode Backend API - Health Check
  description: API endpoints for monitoring the health of the GraphNode Backend API and its dependencies
  version: 1.0.0
  contact:
    name: Kavia AI
servers:
  - url: http://localhost:8000
    description: Local development server
  - url: https://api.example.com
    description: Production server
tags:
  - name: health
    description: Health check endpoints for Kubernetes readiness and liveness probes
paths:
  /api/health:
    get:
      tags:
        - health
      summary: Health check endpoint for Kubernetes
      description: Comprehensive health check that tests all external dependencies
      operationId: healthCheck
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
              example:
                status: healthy
                timestamp: **********.989318
                services:
                  mongodb:
                    status: healthy
                    response_time_ms: 247.34
                    details: MongoDB connection successful
                  neo4j:
                    status: healthy
                    response_time_ms: 266.06
                    details: Neo4j connection successful
                  aws_bedrock:
                    status: healthy
                    response_time_ms: 23.25
                    details: AWS Bedrock connection successful
                  openai:
                    status: healthy
                    response_time_ms: 16.19
                    details: OpenAI API connection successful
                  vertex_ai:
                    status: skipped
                    response_time_ms: 0
                    details: Google Vertex AI libraries not installed
                avg_response_time_ms: 256.7
        '503':
          description: Service Unavailable
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
              example:
                status: unhealthy
                timestamp: **********.989318
                services:
                  mongodb:
                    status: unhealthy
                    response_time_ms: 0.0
                    error: Connection refused
                  neo4j:
                    status: healthy
                    response_time_ms: 266.06
                    details: Neo4j connection successful
                avg_response_time_ms: 133.03
  /api/health/readiness:
    get:
      tags:
        - health
      summary: Readiness probe for Kubernetes
      description: Lightweight check to determine if the application is ready to serve traffic
      operationId: readinessProbe
      responses:
        '200':
          description: Application is ready
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReadinessResponse'
              example:
                status: ready
                timestamp: **********.9818501
                services:
                  mongodb:
                    status: healthy
                    response_time_ms: 249.01
                    details: MongoDB connection successful
                  neo4j:
                    status: healthy
                    response_time_ms: 1447.16
                    details: Neo4j connection successful
        '503':
          description: Application is not ready
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReadinessResponse'
              example:
                status: not_ready
                timestamp: **********.113207
                services:
                  mongodb:
                    status: healthy
                    response_time_ms: 236.02
                    details: MongoDB connection successful
                  neo4j:
                    status: unhealthy
                    response_time_ms: 0.43
                    error: Connection refused
  /api/health/liveness:
    get:
      tags:
        - health
      summary: Liveness probe for Kubernetes
      description: Simple check to verify the application is running
      operationId: livenessProbe
      responses:
        '200':
          description: Application is alive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LivenessResponse'
              example:
                status: alive
                timestamp: **********.2022479
                version: "1.0.0"
components:
  schemas:
    ServiceStatus:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, unhealthy, skipped]
          description: Status of the service
        response_time_ms:
          type: number
          format: float
          description: Response time in milliseconds
        details:
          type: string
          description: Additional details about the service status
        error:
          type: string
          description: Error message if the service is unhealthy
      required:
        - status
        - response_time_ms
    HealthCheckResponse:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, unhealthy]
          description: Overall health status
        timestamp:
          type: number
          format: float
          description: Unix timestamp of the health check
        services:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ServiceStatus'
          description: Status of each service
        avg_response_time_ms:
          type: number
          format: float
          description: Average response time across all services in milliseconds
      required:
        - status
        - timestamp
        - services
    ReadinessResponse:
      type: object
      properties:
        status:
          type: string
          enum: [ready, not_ready]
          description: Readiness status
        timestamp:
          type: number
          format: float
          description: Unix timestamp of the readiness check
        services:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ServiceStatus'
          description: Status of critical services
      required:
        - status
        - timestamp
        - services
    LivenessResponse:
      type: object
      properties:
        status:
          type: string
          enum: [alive]
          description: Liveness status
        timestamp:
          type: number
          format: float
          description: Unix timestamp of the liveness check
        version:
          type: string
          description: API version
      required:
        - status
        - timestamp
