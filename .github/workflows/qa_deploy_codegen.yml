name: Codegenservice Deployment
on:
  push:
    branches:
      - qatest
  workflow_dispatch:

env:
  DUPLO_HOST: https://duplo.cloud.kavia.ai
  DUPLO_TOKEN: ${{ secrets.DUPLO_TOKEN }}
  DUPLO_TENANT: kavia-qa
  REPO_NAME: codegenservice

jobs:
  build:
    if: contains(github.event.head_commit.message, 'codegendeploy')
    runs-on: ubuntu-latest
    outputs:
      backend_image: ${{ steps.build-and-push-codegenservice.outputs.image }}:${{ github.sha }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Duplo Setup
      uses: duplocloud/actions@main

    - name: Build and Push codegenservice Docker Image
      uses: duplocloud/actions/build-image@main
      id: build-and-push-codegenservice
      with:
        push: true
        repo: ${{ env.REPO_NAME }}
        dockerfile: Dockerfile.dev
        platforms: linux/amd64
        cache: false
        build-args: |
          AWS_ACCESS_KEY_ID=${{ secrets.DUPLO_AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY=${{ secrets.DUPLO_AWS_SECRET_ACCESS_KEY }}
        tags: ${{ github.sha }}
  
  deploy:
    if: contains(github.event.head_commit.message, 'codegendeploy')
    runs-on: ubuntu-latest
    needs:
    - build
    steps: 
    - name: Checkout code
      uses: actions/checkout@v3
    - name: Duplo Setup
      uses: duplocloud/actions@main
      with:
        admin: true
    - name: Create Kubeconfig
      run: |
        aws eks update-kubeconfig \
        --name duploinfra-qa \
        --region us-west-2
        
#    - name: Configure AWS credentials
#      uses: aws-actions/configure-aws-credentials@v1
#      with:
#        aws-access-key-id: ${{ secrets.DUPLO_AWS_ACCESS_KEY_ID }}
#        aws-secret-access-key: ${{ secrets.DUPLO_AWS_SECRET_ACCESS_KEY }}
#        aws-region: us-west-2
        
#    - name: Login to Amazon ECR
#      id: login-ecr
#      uses: aws-actions/amazon-ecr-login@v1

    - name: Deploy to QA environment
      env:
        IMAGE: "${{ needs.build.outputs.backend_image }}"
      run: |
        echo "Image being used: $IMAGE"
        GIT_SHA="${{ github.sha }}"
        echo $GIT_SHA
        GIT_SHA="${{ github.sha }}"
        > /tmp/test_podname.txt
        kubectl get pods -l service=codegen -n duploservices-kavia-qa --no-headers | while read -r POD_NAME READY STATUS _; do
          echo "Processing pod: $POD_NAME with status: $STATUS"
          if [[ "$STATUS" != "Running" ]]; then
            echo "Skipping non-running pod: $POD_NAME"
            continue
          fi
          BASE_NAME=$(echo "$POD_NAME" | sed -E 's/-[a-z0-9]+-[a-z0-9]+$//')
          echo "Checking for screen session in pod: $POD_NAME..."
          if kubectl exec "$POD_NAME" -c "$BASE_NAME" -n duploservices-kavia-qa -- screen -ls 2>/dev/null | grep -q 'Detached\|Attached'; then
            echo "Screen session found in $POD_NAME"
            echo "$POD_NAME" >> /tmp/test_podname.txt
            continue
          else
            echo "No screen session in $POD_NAME, proceeding to update"
          fi
          echo "Updating deployment: $BASE_NAME"
          kubectl set image deployment/$BASE_NAME $BASE_NAME=$IMAGE -n duploservices-kavia-qa
        done

    - name: update image-id for codegen in duploctl
      env:
        IMAGE: "${{ needs.build.outputs.backend_image }}"
      run: |
        echo "Image being used: $IMAGE"
        GIT_SHA="${{ github.sha }}"
        echo $GIT_SHA
        NEW_IMAGE_TAG=${IMAGE##*:}
        echo $NEW_IMAGE_TAG
        echo "finding config map"
        duploctl configmap find codegenservicedeploymentqa -o yaml > /tmp/test7.yaml
        cat /tmp/test7.yaml
        echo "defining image prefix"
        IMAGE_PREFIX="127214169382.dkr.ecr.us-west-2.amazonaws.com/codegenservice"
        echo "using sed"
        sed -i -E "s|(${IMAGE_PREFIX}):[a-zA-Z0-9._-]+|\1:${NEW_IMAGE_TAG}|g" /tmp/test7.yaml
        echo "opening final file"
        cat /tmp/test7.yaml
        echo "updating existing config map"
        duploctl configmap update codegenservicedeploymentqa -f /tmp/test7.yaml

  notify:
    name: Slack Notification
    runs-on: ubuntu-latest
    needs: deploy
    if: always()    # run even if deploy fails or is canceled
    steps:
      - name: Slack Deployment Succeeded
        if: ${{ needs.deploy.result == 'success' }}
        run: |
          curl -X POST \
            -H 'Content-type: application/json' \
            --data "{\"text\":\"✅ *Code Generation Deployment complete* for branch \`${{ github.ref_name }}\` in *${{ github.repository }}*.\"}" \
            ${{ secrets.SLACK_HOOK_QA_DEPLOY }}

      - name: Slack Deployment Failed
        if: ${{ needs.deploy.result == 'failure' }}
        run: |
          curl -X POST \
            -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 *Code Generation Deployment failed* for branch \`${{ github.ref_name }}\` in *${{ github.repository }}*.\nView logs: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}\"}" \
            ${{ secrets.SLACK_HOOK_QA_DEPLOY }}
