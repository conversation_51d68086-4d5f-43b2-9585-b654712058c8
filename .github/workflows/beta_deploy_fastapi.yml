name: Deploy to AWS

on:
  push:
    branches:
      - beta

env:
  DUPLO_TOKEN: ${{ secrets.DUPLO_TOKEN }}
  DUPLO_HOST: https://duplo.cloud.kavia.ai
  DUPLO_TENANT: kavia-beta
  REPO_NAME: fastapi-backend
  SERVICE_NAME: kavia-backend
  CELERY_SERVICE_NAME: kavia-celery-worker-beta-svc
  QUERY_SERVICE_NAME: code-query-fast-api-sever

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    outputs:
      backend_image: ${{ steps.build-and-push-backend.outputs.image }}:${{ github.sha }}
    steps:
    - uses: actions/checkout@v2
      with:
        fetch-depth: 0

    - name: Set branch
      id: set_branch
      run: |
        if [[ ${{ github.ref }} == 'refs/heads/duplo-main' ]]; then
          echo "branch=duplo-main" >> $GITHUB_OUTPUT
        fi

    - name: Duplo Setup
      uses: duplocloud/actions@main

    - name: Set environment variables
      run: |
        if [[ ${{ github.ref }} == 'refs/heads/duplo-main' ]]; then
          echo "IMAGE_NAME=fastapi-backend" >> $GITHUB_ENV
          echo "CLUSTER_NAME=kaviaAI" >> $GITHUB_ENV
          echo "SERVICE_NAME=kavia-backend" >> $GITHUB_ENV
          echo "CELERY_SERVICE_NAME=kavia-celery-worker-beta-svc" >> $GITHUB_ENV
          echo "QUERY_SERVICE_NAME=code-query-fast-api-sever" >> $GITHUB_ENV
        fi

    - name: Print SSH key (debug)
      run: |
        echo "::debug::SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}"

    - name: Build and Push Docker Image
      uses: duplocloud/actions/build-image@main
      id: build-and-push-backend
      with:
        repo: ${{ env.REPO_NAME }}
        platforms: linux/amd64
        push: true
        cache: false
        build-args: |
          AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION=us-east-1
          SSH_KEY="${{ secrets.SSH_PRIVATE_KEY }}"
          DD_GIT_REPOSITORY_URL=$(git config --get remote.origin.url)
          DD_GIT_COMMIT_SHA=$(git rev-parse HEAD)
        tags: ${{ github.sha }}

  deploy:
    runs-on: ubuntu-latest
    needs:
      - build
    environment: development
    steps:
    - name: Duplo Setup
      uses: duplocloud/actions/setup@v0.0.5

    - name: Update Backend Service
      uses: duplocloud/actions/update-image@v0.0.5
      with:
        name: ${{ env.SERVICE_NAME }}
        image: ${{ needs.build.outputs.backend_image }}

    - name: Update celery worker Service
      uses: duplocloud/actions/update-image@v0.0.5
      with:
        name: ${{ env.CELERY_SERVICE_NAME }}
        image: ${{ needs.build.outputs.backend_image }}

    - name: Update Query Service
      uses: duplocloud/actions/update-image@v0.0.5
      with:
        name: ${{ env.QUERY_SERVICE_NAME }}
        image: ${{ needs.build.outputs.backend_image }}

  notify:
    name: Slack Notification
    runs-on: ubuntu-latest
    needs: deploy
    if: always()    # run even if deploy fails or is canceled
    steps:
      - name: Slack Deployment Succeeded
        if: ${{ needs.deploy.result == 'success' }}
        run: |
          curl -X POST \
            -H 'Content-type: application/json' \
            --data "{\"text\":\"✅ *FastAPI Deployment complete* for branch \`${{ github.ref_name }}\` in *${{ github.repository }}*.\"}" \
            ${{ secrets.SLACK_HOOK_BETA_DEPLOY }}

      - name: Slack Deployment Failed
        if: ${{ needs.deploy.result == 'failure' }}
        run: |
          curl -X POST \
            -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 *FastAPI Deployment failed* for branch \`${{ github.ref_name }}\` in *${{ github.repository }}*.\nView logs: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}\"}" \
            ${{ secrets.SLACK_HOOK_BETA_DEPLOY }}
