# Repository Management Internal Logic Analysis: GraphNode Backend API

## Overview

This analysis focuses on the internal code logic for repository management within the GraphNode Backend API, specifically covering how repositories are created for tenants, database interactions, session branch management, and knowledge storage mechanisms.

## Tenant-Based Repository Architecture

### Tenant Database Isolation

The system implements multi-tenant architecture with database isolation:

```python
# From app/repository/mongodb/tenant.py
def get_tenant_based_db_name(name: str) -> str:
    """Get tenant-specific database name"""
    tenant_id = get_tenant_id()

    if name == settings.STAGE:
        return f'{name}_{tenant_id}'
    elif name != KAVIA_ROOT_DB_NAME:
        return f'{settings.STAGE}_{name}_{tenant_id}'
    return name
```

### B2C Collection Naming Strategy

For B2C clients, collections are prefixed with user IDs:

```python
# From app/repository/mongodb/base_repository.py
def _get_b2c_collection_name(self, collection_name: str, db_name: str) -> str:
    if tenant_id == settings.KAVIA_B2C_CLIENT_ID and db_name != KAVIA_ROOT_DB_NAME:
        user_id = get_user_id()
        if user_id:
            if not collection_name.startswith(f"{user_id}_"):
                modified_collection_name = f"{user_id}_{collection_name}"
                return modified_collection_name
    return collection_name
```

## Repository Creation for Tenants

### Neo4j Repository Metadata Storage

Repository metadata is stored in Neo4j nodes with JSON-serialized repository details:

```python
# From app/classes/NodeDB.py
async def create_node(self, node_types, properties, parent_id=None):
    if 'repository_details' in properties:
        repo_details = properties['repository_details'].copy()
        properties['repository_details'] = json.dumps(properties['repository_details'])
```

### MongoDB Project Repositories Collection

The `project_repositories` collection stores detailed repository information:

```python
# From app/utils/respository_utils.py
dummy_repository = {
    "service": "github",
    "repo_id": repo_id,
    "repository_name": repo_name,
    "associated": associated,
    "git_url": f"https://github.com/{repo_name}.git",
    "repositoryStatus": "initialized",
    "clone_url_ssh": f"**************:{repo_name}.git",
    "repo_type": repo_type,
    "branches": [
        {
            "name": branch_name,
            "latest_commit_hash": None,
            "builds": {
                "build_id": build_id,
                "build_session_id": build_session_id,
                "path": f"{data_dir}/{build_id}/{repo_name.split('/')[-1]}",
                "kg_creation_status": 1,
                "build_info": {
                    "start_time": generate_timestamp(),
                    "end_time": None,
                    "last_updated": None,
                    "duration_seconds": None
                },
                "last_updated": generate_timestamp(),
                "user_id": user_id,
                "error": None
            }
        }
    ],
    "selected_branch": branch_name
}
```

### Repository Assignment Process

1. **Project Node Update**: Repository metadata is stored in Neo4j project nodes
2. **MongoDB Document Creation**: Detailed repository data stored in `project_repositories` collection
3. **Container Association**: Repositories are linked to specific container IDs

```python
# From app/routes/repository_route.py
project_repositories[str(container_id)] = repository_metadata
await db.update_node_by_id(project_id, {"repositories": json.dumps(project_repositories)})
```

## Repository Cloning Logic

### Clone Path Structure

Repositories are cloned to tenant-specific paths with build isolation:

```python
# From app/utils/respository_utils.py
"path": f"{data_dir}/{build_id}/{repo_name.split('/')[-1]}"
```

### Authentication and Cloning Process

The system supports multiple SCM services with token-based authentication:

```python
# From app/utils/respository_utils.py
def clone_repository(repository_metadata: Dict, tenant_id: str, task_id: str = None) -> str:
    if service == 'github':
        clone_url = repository_metadata.get('cloneUrlHttp')
        auth_url = clone_url.replace('https://', f'https://oauth2:{access_token}@')

        # Resume from specific branch if needed
        if os.getenv("resume"):
            resume_branch = f"cga-{task_id}"
            repo = Repo.clone_from(auth_url, clone_path, branch=resume_branch)
        else:
            repo = Repo.clone_from(auth_url, clone_path)

        # Configure git settings
        repo.git.config("core.filemode", "false")
        setup_git_credentials(clone_path, access_token, clone_url)
```

### Background Cloning

For performance, repositories are cloned in background threads:

```python
# From app/routes/repository_route.py
if os.getenv("task_id"):
    clone_thread = threading.Thread(
        target=_run_clone_in_background,
        args=(repository_metadata_copy, tenant_id, task_id, repository_name),
        daemon=True
    )
    clone_thread.start()
```

## Session Branch Creation Logic

### Kavia-Main Branch Creation

The system automatically creates a `kavia-main` branch as the default branch:

```python
# From app/utils/respository_utils.py
def create_kavia_main_branch(github_repo):
    try:
        main_branch = github_repo.get_branch("main")
        github_repo.create_git_ref(ref="refs/heads/kavia-main", sha=main_branch.commit.sha)
        github_repo.edit(default_branch="kavia-main")
    except Exception as e:
        print(f"Warning: Could not set kavia-main as default branch: {str(e)}")
```

### Session Branch Management

The `create_session_branches` function handles complex branch operations:

```python
# From app/utils/respository_utils.py
def create_session_branches(repo: Repo, task_id: str = None) -> Dict[str, str]:
    """
    Create and manage session branches for repository operations.

    Creates kavia-main branch from current branch if it doesn't exist,
    and optionally creates cga-{task_id} branch from kavia-main.
    """
    result = {
        "kavia_main_created": False,
        "task_branch_created": False,
        "current_branch": None,
        "errors": []
    }

    # Create kavia-main if it doesn't exist
    if "kavia-main" not in [branch.name for branch in repo.branches]:
        try:
            repo.git.checkout('-b', 'kavia-main')
            result["kavia_main_created"] = True
        except Exception as e:
            result["errors"].append(f"Failed to create kavia-main: {str(e)}")

    # Create task-specific branch if task_id provided
    if task_id:
        task_branch_name = f"cga-{task_id}"
        try:
            repo.git.checkout('kavia-main')
            repo.git.checkout('-b', task_branch_name)
            result["task_branch_created"] = True
        except Exception as e:
            result["errors"].append(f"Failed to create task branch: {str(e)}")
```

## Code Generation Integration

### Session Branch Creation in Code Generation

After repository cloning, session branches are automatically created:

```python
# From app/core/code_generation.py
if task_id:
    try:
        repo = Repo(repo_path)
        branch_result = create_session_branches(repo, task_id)
        send_agent_message_custom(
            ws_client=ws_client,
            task_id=task_id,
            message=f'Session branches created for {repository_metadata["repositoryName"]}: {branch_result}',
            db=db
        )
```

### Resume Functionality

The system supports resuming from specific branches:

```python
# From app/utils/respository_utils.py
if os.getenv("resume"):
    resume_branch = f"cga-{task_id}"
    repo = Repo.clone_from(auth_url, clone_path, branch=resume_branch)
```

### Code Maintenance vs Code Generation

Different workflows are triggered based on the agent type:

```python
# From app/core/code_generation.py
# Handle session branch creation after cloning
if task_id:
    try:
        repo = Repo(repo_path)
        branch_result = create_session_branches(repo, task_id)
        # Different handling for CodeMaintenance vs CodeGeneration
```

## Knowledge Storage and Git Integration

### Knowledge Base Repository Integration

The knowledge system integrates directly with git repositories:

```python
# From app/utils/kg_build/knowledge.py
class KnowledgeCodeBase:
    def __init__(self, base_path, name=None, service="github"):
        self.base_path = base_path
        self.service = service
        self.repo = None
        if self.service != "localFiles":
            try:
                self.repo = Repo(self.base_path, search_parent_directories=False)
            except Exception:
                self.repo = None
```

### Git Revision Tracking

Knowledge items track git revisions for version control:

```python
# From app/utils/kg_build/knowledge.py
def _get_git_revision(self, filename):
    git_hash = ""
    if self.repo:
        try:
            git_hash = self.repo.git.log('-n', '1', '--format=%H', '--', filename).strip()
        except Exception as e:
            self.knowledge.logger.error(f"Exception getting git commit hash for {filename}: {str(e)}")
    return git_hash
```

### Knowledge Persistence with Git Integration

Knowledge is persisted with git revision information:

```python
# From app/utils/kg_build/knowledge.py
def _persist_knowledge(self, file_info):
    if state == _STATE_COMPLETE:
        file_info['git_revision'] = code_base._get_git_revision(filename)
        revision_history.append({file_info['knowledge_revision']: file_info['git_revision']})
        file_info['revision_history'] = revision_history
```

## Repository Storage Locations

### Local vs EFS Storage

Repositories are stored locally within containers, not on EFS:

```python
# From app/utils/respository_utils.py
# Repositories are cloned to local paths within containers
clone_path = f"/data/{tenant_id}/{build_id}/{repo_name}"
```

### Data Directory Structure

The system uses a hierarchical directory structure:

```
/data/
├── {tenant_id}/
│   ├── {build_id}/
│   │   ├── {repository_name}/
│   │   │   ├── .git/
│   │   │   ├── source_code/
│   │   │   └── .knowledge/
│   │   └── logs/
│   └── knowledge_cache/
```

### Knowledge Storage Integration

Knowledge is stored alongside the git repository:

```python
# From app/utils/kg_build/knowledge.py
folder = os.path.join(base_path, '.knowledge')
# Knowledge files are stored in .knowledge directory within repo
```

### Build Path Management

Each build gets a unique path for isolation:

```python
# From app/utils/respository_utils.py
"builds": {
    "build_id": build_id,
    "build_session_id": build_session_id,
    "path": f"{data_dir}/{build_id}/{repo_name.split('/')[-1]}",
    "kg_creation_status": 1,
    # ... other build metadata
}
```

## Database Field Mappings

### Neo4j Repository Fields

Repository metadata in Neo4j project nodes:

```python
# From app/classes/NodeDB.py
properties = {
    "repositories": json.dumps({
        str(container_id): {
            "repositoryName": repo_name,
            "cloneUrlHttp": clone_url,
            "repositoryId": repo_id,
            "organization": org_name,
            "service": "github"
        }
    })
}
```

### MongoDB Collection Structure

The `project_repositories` collection contains:

- **project_id**: Links to Neo4j project node
- **repositories**: Array of repository objects
- **created_at**: Timestamp of creation
- **tenant_id**: Implicit through collection naming

### Repository Status Tracking

Repository status is tracked through multiple fields:

```python
# From app/utils/respository_utils.py
"repositoryStatus": "initialized",  # Status in Neo4j
"kg_creation_status": 1,            # Knowledge graph status
"build_info": {                     # Build tracking
    "start_time": timestamp,
    "end_time": None,
    "last_updated": None,
    "duration_seconds": None
}
```

## Git Repository Management Per Project

### Project-Container-Repository Relationship

Each project can have multiple containers, each with its own repository:

```python
# From app/routes/repository_route.py
project_repositories = json.loads(project_details.get("repositories", "{}"))
project_repositories[str(container_id)] = repository_metadata
```

### Repository Workspace Management

Each repository gets isolated workspace:

1. **Tenant Isolation**: `/data/{tenant_id}/`
2. **Build Isolation**: `/{build_id}/`
3. **Repository Isolation**: `/{repository_name}/`

### Knowledge Graph Integration

Repository code is indexed into knowledge graphs:

```python
# From app/utils/kg_build/knowledge.py
# Knowledge is extracted from git repositories and stored
# with git revision tracking for version control
```

## Conclusion

The repository management system implements sophisticated multi-tenant isolation with:

- **Tenant-based database naming** for MongoDB collections
- **Container-specific repository assignment** in Neo4j
- **Session branch management** for code generation tasks
- **Local storage** within containers (not EFS)
- **Git-integrated knowledge storage** with revision tracking
- **Build-isolated workspaces** for parallel operations

The system effectively manages the complexity of multi-tenant repository operations while maintaining data isolation and enabling sophisticated code generation workflows.
