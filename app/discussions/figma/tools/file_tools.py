import os
from functools import wraps
from enum import Enum
from app.discussions.figma.tools.editor import apply_edit_block
import os
from pathlib import Path

class ExecutorResult(Enum):
    SUCCESS = 0
    ERROR = 1



class FileTools:
    def __init__(self, base_path: str):
        self.base_path = base_path
        self.discussion_id = None
        
        self.tool_schema_mapping = [
            {   
                "type": "function",
                "function": {
                    "name": "create_file",
                    "description": "Create a file",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "file_path": {"type": "string",
                                        "description": "The path to the file to create"
                                        },
                            "content": {"type": "string",
                                        "description": "The content to write to the file"
                                        }
                        },
                        "required": ["file_path", "content"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "read_file",
                    "description": "Read a file",
                    "parameters": {
                        "type": "object",
                        "properties": {"file_path": {"type": "string",
                                          "description": "The path to the file to read"
                                          }},
                        "required": ["file_path"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "delete_file",
                    "description": "Delete a file",
                    "parameters": {
                        "type": "object",
                        "properties": {"file_path": {"type": "string",
                                          "description": "The path to the file to delete"
                                          }},
                        "required": ["file_path"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "edit_block",
                    "description": "Edit a file using a unified diff-like format for precise, context-aware changes.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "changes": {"type": "string",
                                        "description": f""" String ins SEARCH/REPLACE format.
# *SEARCH/REPLACE block* Rules:
Every *SEARCH/REPLACE block* must use this format:
1. The *FULL* and absolute file path alone on a line, verbatim. No bold asterisks, no quotes around it, no escaping of characters, etc.
2. The start of search block: <<<<<<< SEARCH
3. A contiguous chunk of lines to search for in the existing source code
4. The dividing line: =======
5. The lines to replace into the source code
6. The end of the replace block: >>>>>>> REPLACE
Use absolute file path, as shown to you by the user.
Absolute path always starts with: {self.base_path}
If the SEARCH section doesn't exactly match existing file content, the edit will fail and damage the user's system. 
Consider the preloaded file content as a source of actual truth for current file content.
Every *SEARCH* section must *EXACTLY MATCH* the existing file content, character for character, including all comments, doc strings, etc.
The *SEARCH* section is always a subset, or multiple subsets of an existing file content. This *SEARCH* section MUST be is based on:
a.) The preloaded file content in the prompt, if any.
b.) The file content loaded using a previous *ContainerFileTools_read_files* tool call.
Never use the *SEARCH* section to search for a line that is not in the file content, or I will be fired.
If the file contains code or other data wrapped/escaped in json/xml/quotes or other containers, you need to propose edits to the literal contents of the file, including the container markup.
*SEARCH/REPLACE* blocks will replace *all* matching occurrences.
Include enough lines to make the SEARCH blocks uniquely match the lines to change.
IMPORTANT: Each SEARCH/REPLACE block must have exactly one "=======" separator line.
If you need to make multiple changes to a file, use multiple SEARCH/REPLACE blocks.
Keep *SEARCH/REPLACE* blocks concise.
Break large *SEARCH/REPLACE* blocks into a series of smaller blocks that each change a small portion of the file.
Include just the changing lines, and a few surrounding lines if needed for uniqueness.
Do not include long runs of unchanging lines in *SEARCH/REPLACE* blocks.
To move code within a file, use 2 *SEARCH/REPLACE* blocks: 1 to delete it from its current location, 1 to insert it in the new location.
Pay attention to which filenames the user wants you to edit, especially if they are asking you to create a new file.
If you want to put code in a new file, use a *SEARCH/REPLACE block* with:
- A new file path, including dir name if needed
- An empty `SEARCH` section
- The new file's contents in the `REPLACE` section
To rename files which have been added to the chat, use shell commands at the end of your response
                    """
                                        }
                        },
                        "required": ["changes"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "list_files",
                    "description": "List all files under the base path in a tree structure. Use this list_files function when the file structure is unknown.",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        ]
    def set_discussion_id(self,discussion_id):
        self.discussion_id = discussion_id
        
    def get_tool_schemas(self):
        return self.tool_schema_mapping

    def get_tool_executors(self):
        return self.file_tool_executor

    async def file_tool_executor(self, function_name, function_args):
        try:
            print(f"DEBUG: Starting execution of {function_name}")
            print(f"DEBUG: Arguments received: {function_args}")

            method = getattr(self, function_name, None)
            print(f"DEBUG: Found method: {method}")

            if callable(method):
                try:
                    print(f"DEBUG: Attempting to execute {function_name}")
                    result = method(**function_args)
                    print(f"DEBUG: Execution successful, result: {result}")
                    return (ExecutorResult.SUCCESS.value, result)
                except Exception as e:
                    print(f"DEBUG: Error executing method: {str(e)}")
                    return (ExecutorResult.ERROR.value, str(e))
            else:
                print(f"DEBUG: Method {function_name} not callable or not found")
                return (ExecutorResult.ERROR.value, f"Method {function_name} not found")
        except Exception as e:
            print(f"DEBUG: Top-level error: {str(e)}")
            return (ExecutorResult.ERROR.value, str(e))

    def validate_path_decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Get file_path from either args or kwargs
            file_path = kwargs.get('file_path') if 'file_path' in kwargs else args[0]
            if not file_path.startswith(self.base_path):
                raise ValueError(f"File path must start with {self.base_path}")
            return func(self, *args, **kwargs)
        return wrapper

    @validate_path_decorator
    def create_file(self, file_path, content):
        print(f"Creating file at {file_path} with content: {content}")
        with open(file_path, 'w') as f:
            f.write(content)
        return f"File created successfully at {file_path}"

    @validate_path_decorator
    def read_file(self, file_path):
        with open(file_path, 'r') as f:
            return f.read()

    @validate_path_decorator
    def delete_file(self, file_path):
        os.remove(file_path)
        return f"File deleted successfully at {file_path}"
    
    def list_files(self):
        """
        Walk through self.base_path and return a tree-like string of all files.
        """
        tree_str = ''
        
        full_path = f"{self.base_path}/{self.discussion_id}"
        for root, dirs, files in os.walk(full_path):
            level = root.replace(full_path, '').count(os.sep)
            indent = ' ' * 4 * level
            tree_str += f"{indent}{os.path.basename(root)}/\n"
            subindent = ' ' * 4 * (level + 1)
            for f in files:
                tree_str += f"{subindent}{f}\n"
        return tree_str

    def edit_block(self, changes):
        """
        Edit a file using the editor's apply_edit_block functionality.
        
        Args:
            changes (str): Edit block content in SEARCH/REPLACE format
            
        Returns:
            dict: Status and results of the operation
        """

        # Get the file path from the first line
        file_path = changes.partition('\n')[0]
        basename = os.path.basename(file_path)

        # Apply the edit using the editor
        success, message, warning, file_summary = apply_edit_block(changes, None)


        if success:

            # Get updated content from file summary
            updated_content = file_summary.get(basename, '')


            response = {
                "status": "SUCCESS",
                "message": message
            }

            if warning:
                response["warning"] = warning

            if file_summary:
                response["updated_file_content"] = file_summary

            return response
        else:
            raise Exception(message)


