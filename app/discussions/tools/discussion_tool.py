"""
Enhanced logging for discussion_tool.py that creates structured, indexable logs for Datadog
"""

from datetime import datetime
from app.connection.establish_db_connection import get_node_db
from llm_wrapper.core.llm_interface import LLMInterface
from app.utils.logs_utils import get_path
from llm_wrapper.utils.base_tools import BaseTool
from app.core.function_schema_generator import get_function_schema
from app.connection.establish_db_connection import get_mongo_db
import json
import uuid
from app.utils.datetime_utils import generate_timestamp
from app.utils.mermaid_validator import validate_mermaid_code, fix_mermaid_with_llm
import anthropic
from app.core.Settings import settings
from app.core.data_model_helper import data_model_helper
from app.telemetry.logger_config import get_logger
import traceback

# Initialize logger with consistent naming
logger = get_logger(__name__)

class DiscussionTools(BaseTool):
    
    def __init__(self, base_path, logger, llm=None, user_id=None, discussion=None):
        super().__init__(logger)
        self.user_id = user_id
        self.node_db = get_node_db()
        self.discussion = discussion
        self.tool_id = str(uuid.uuid4())  # Unique ID for this tool instance
        self.logger = get_logger(__name__)
        
        # Log tool initialization with context
        self.logger.info(
            "Initializing DiscussionTools",
            extra={
                'method': 'init',
                'tool_id': self.tool_id,
                'user_id': self.user_id,
                'discussion_type': getattr(self.discussion, 'discussion_type', None),
                'discussion_id': getattr(self.discussion, 'discussion_id', None),
                'node_type': getattr(self.discussion, 'node_type', None),
                'has_function_schema': hasattr(self.discussion, 'function_schema_type')
            }
        )

        self.function_schemas = []
        self.function_mapping = {}
        if not self.discussion.discussion_type == 'Conversation':
            self.function_schemas = [get_function_schema(self.discussion.function_schema_type)]

            self.function_mapping.update({
                "capture_discussion_output": self.capture_discussion_output
            })
            
            self.logger.info(
                "Registered capture_discussion_output function",
                extra={
                    'method': 'init',
                    'tool_id': self.tool_id,
                    'discussion_type': self.discussion.discussion_type,
                    'function_schema_type': self.discussion.function_schema_type,
                    'registered_function': 'capture_discussion_output'
                }
            )

        if self.discussion.function_schema_type in ['Epic','RequirementRoot','ArchitecturalRequirement','Conversation']:
            function_schema = [
                {
                    "type": "function",
                    "function": {
                        "name": "get_nodes",
                        "description": "Get the contents of the specified nodes",
                        "parameters": {
                            "type": "object",
                            "strict": True,
                            "properties": {
                                "node_ids": {
                                    "type": "array",
                                    "items": {"type": "integer"},
                                    "description": "list of node_id values"
                                }
                            },
                            "required": ["node_ids"]
                        }
                    }
                },
                {
                    "type": "function",
                    "function": {
                        "name": "find_relevant_nodes",
                        "description": "Find nodes relevant to the specified search terms. The search is an OR-search. Returns relevant node_id values listed " \
                                       "from most relevant to least relevant. The node_types argument is an optional list of node types that can be used to " \
                                        "limit results to nodes of the listed types.  For example, if you are only intersted in Epic nodes, pass node_types as ['Epic'].",
                        "parameters": {
                            "type": "object",
                            "strict": True,
                            "properties": {
                                "parent_node": {
                                    "type": "integer",
                                    "description": "parent node id"
                                },
                                "search_terms": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "List of search terms"
                                },
                                "node_types": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "list of node types"
                                }
                            },
                            "required": ["parent_node","search_terms"]
                        }
                    }
                }
            ]
            self.function_schemas.extend(function_schema)
            self.function_mapping.update({
                "get_nodes": self.get_nodes,
                "find_relevant_nodes": self.find_relevant_nodes
            })
            
            self.logger.info(
                "Registered knowledge graph functions",
                extra={
                    'method': 'init',
                    'tool_id': self.tool_id,
                    'discussion_type': self.discussion.discussion_type,
                    'function_schema_type': self.discussion.function_schema_type,
                    'registered_functions': ['get_nodes', 'find_relevant_nodes'],
                    'schema_count': len(self.function_schemas)
                }
            )

        if self.discussion.function_schema_type in ['Conversation']:
            function_schema = [
                {
                    "type": "function",
                    "function": {
                        "name": "find_nodes_by_types",
                        "description": "Find nodes of specified types",
                        "parameters": {
                            "type": "object",
                            "strict": True,
                            "properties": {
                                "parent_node": {
                                    "type": "integer",
                                    "description": "parent node id"
                                },
                                "node_types": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "list of node types"
                                }
                            },
                            "required": ["parent_node","node_types"]
                        }
                    }
                },
            ]
            self.function_schemas.extend(function_schema)
            self.function_mapping.update({
                "find_nodes_by_types": self.find_nodes_by_types
            })
            
            self.logger.info(
                "Registered conversation-specific functions",
                extra={
                    'method': 'init',
                    'tool_id': self.tool_id,
                    'discussion_type': self.discussion.discussion_type,
                    'function_schema_type': self.discussion.function_schema_type,
                    'registered_functions': ['find_nodes_by_types'],
                    'schema_count': len(self.function_schemas)
                }
            )
        
        # Initialize LLM interface with strict session ID enforcement
        try:
            # Use the discussion's strict session ID resolver
            session_id_for_llm = self.discussion.get_strict_session_id("discussion_tools")

            self.llm = LLMInterface(
                str(get_path()),
                'discussion_tools',
                self.discussion.current_user,
                self.discussion.node_id,
                'discussion_'+self.discussion.discussion_type,
                mongo_handler=get_mongo_db(),
                session_id=session_id_for_llm
            )
            
            self.client = anthropic.Anthropic(
                api_key=settings.ANTHROPIC_API_KEY
            )
            
            self.logger.info(
                "Initialized LLM interface",
                extra={
                    'method': 'init',
                    'tool_id': self.tool_id,
                    'model': getattr(self.llm, 'model', 'default'),
                    'path': str(get_path()),
                    'has_client': self.client is not None
                }
            )
        except Exception as e:
            self.logger.error(
                f"Error initializing LLM interface: {str(e)}",
                extra={
                    'method': 'init',
                    'tool_id': self.tool_id,
                    'error_type': 'initialization',
                    'error_category': e.__class__.__name__,
                    'stack_trace': traceback.format_exc()
                }
            )
            raise

    # Helper method to standardize log context
    def get_log_context(self, method, **kwargs):
        """Create standardized context dictionary for logging"""
        context = {
            'tool_id': self.tool_id,
            'method': method,
            'discussion_type': getattr(self.discussion, 'discussion_type', None),
            'discussion_id': getattr(self.discussion, 'discussion_id', None),
            'node_type': getattr(self.discussion, 'node_type', None),
            'node_id': getattr(self.discussion, 'node_id', None),
            'user_id': self.user_id
        }
        
        # Add any additional keyword arguments
        context.update(kwargs)
        
        return context

    def get_display_type(self, nodetype, nodefield):
        """
        Get the display type for a given node type and field from the data model.
        Args:
            nodetype (str): The type of node (e.g., 'Container', 'Interface')
            nodefield (str): The field name to get display type for
        Returns:
            str: The display type for the field, or 'text' if not specified
        """
        try:
            # Get the node definition from the data model
            node_def = data_model_helper.data_model['model'].get(nodetype, {})
            # Get the UI metadata for the node type
            ui_metadata = node_def.get('ui_metadata', {})
            # Get the field's UI metadata
            field_metadata = ui_metadata.get(nodefield, {})
            # Return the display type, defaulting to 'text' if not specified
            return field_metadata.get('display_type', 'text')
        except Exception as e:
            # Log any errors and return default display type
            print(f"Error getting display type for {nodetype}.{nodefield}: {str(e)}")
            return 'text'
        
    def process_function_args(self, args):
        """Process function arguments to handle stringified arrays or objects."""
        import json
        import re
        
        # Create a copy to avoid modifying the original
        processed = args.copy() if isinstance(args, dict) else args
        
        # Handle dict case
        if isinstance(processed, dict):
            for key, value in processed.items():
                if isinstance(value, str):
                    # Clean the string value before trying to parse it
                    if value.strip().startswith('[') and value.strip().endswith(']'):
                        try:
                            # First attempt direct parsing
                            processed[key] = json.loads(value)
                        except json.JSONDecodeError:
                            try:
                                # If direct parsing fails, try to clean up common JSON errors
                                # Remove leading commas before array items
                                cleaned_value = re.sub(r',\s*{', '{', value)
                                # Remove trailing commas at the end of arrays
                                cleaned_value = re.sub(r',\s*]', ']', cleaned_value)
                                processed[key] = json.loads(cleaned_value)
                            except json.JSONDecodeError:
                                # If still failing, log and keep as string
                                print(f"Failed to parse JSON string for key {key}: {value[:100]}...")
                    elif value.strip().startswith('{') and value.strip().endswith('}'):
                        try:
                            processed[key] = json.loads(value)
                        except json.JSONDecodeError:
                            try:
                                # Clean up common JSON errors
                                cleaned_value = re.sub(r',\s*}', '}', value)
                                processed[key] = json.loads(cleaned_value)
                            except json.JSONDecodeError:
                                print(f"Failed to parse JSON object for key {key}: {value[:100]}...")
        
        # Handle array case
        elif isinstance(processed, str) and processed.strip().startswith('[') and processed.strip().endswith(']'):
            try:
                processed = json.loads(processed)
            except json.JSONDecodeError:
                try:
                    # Try to clean up common JSON errors
                    cleaned_value = re.sub(r',\s*{', '{', processed)
                    cleaned_value = re.sub(r',\s*]', ']', cleaned_value)
                    processed = json.loads(cleaned_value)
                except:
                    # Keep as string if unable to parse
                    pass
        
        return processed
        
    async def capture_discussion_output(self, modified_node,modified_child_nodes=None,modified_relationships=None,new_child_nodes=None,modified_sibling_nodes=None,
                                        modified_other_nodes=None,new_relationships=None,new_relationship_types=None, reason_for_this_call=None, delete_node_check=None):
        operation_id = str(uuid.uuid4())
        logger.info(
            "Starting capture_discussion_output",
            extra=self.get_log_context(
                method='capture_discussion_output',
                operation_id=operation_id,
                has_modified_node=bool(modified_node),
                has_modified_child_nodes=bool(modified_child_nodes),
                has_modified_relationships=bool(modified_relationships),
                has_new_child_nodes=bool(new_child_nodes),
                has_modified_sibling_nodes=bool(modified_sibling_nodes),
                has_modified_other_nodes=bool(modified_other_nodes),
                has_new_relationships=bool(new_relationships),
                has_new_relationship_types=bool(new_relationship_types),
                has_delete_node_check=bool(delete_node_check),
                reason=reason_for_this_call
            )
        )
        try:
            new_child_nodes = self.process_function_args(new_child_nodes)
            new_relationships = self.process_function_args(new_relationships)

            self.discussion.modifications = {
                'modified_node': modified_node ,
                'new_child_nodes': new_child_nodes,
                'modified_children': modified_child_nodes,
                'modified_siblings': modified_sibling_nodes,
                'modified_similar_nodes': modified_other_nodes,
                'new_relationships': new_relationships,
                'modified_relationships': modified_relationships,
                'new_relationship_types': new_relationship_types,
                'delete_node_check':delete_node_check,
            }

            self.logger.info(
                "Built modifications structure",
                extra=self.get_log_context(
                    method='capture_discussion_output',
                    operation_id=operation_id,
                    stage='build_mods',
                    has_modified_node=bool(self.discussion.modifications.get('modified_node')),
                    has_new_child_nodes=bool(self.discussion.modifications.get('new_child_nodes')),
                    has_modified_children=bool(self.discussion.modifications.get('modified_children')),
                    has_modified_siblings=bool(self.discussion.modifications.get('modified_siblings')),
                    has_modified_similar=bool(self.discussion.modifications.get('modified_similar_nodes')),
                    has_new_relationships=bool(self.discussion.modifications.get('new_relationships')),
                    has_modified_relationships=bool(self.discussion.modifications.get('modified_relationships')),
                    has_new_relationship_types=bool(self.discussion.modifications.get('new_relationship_types')),
                    has_delete_node_check=bool(self.discussion.modifications.get('delete_node_check'))
                )
            )

            # ===== START OF MERMAID VALIDATION CODE =====
            # Validate mermaid diagrams in modified_node
            if modified_node and isinstance(modified_node, dict):
                try:
                    # Import validation utilities
                    from app.utils.mermaid_validator import validate_mermaid_code, fix_mermaid_with_llm
                    from app.utils.mermaid_validator import get_display_type, is_likely_mermaid
                    from app.core.data_model_helper import data_model_helper
                    
                    # Validate mermaid diagrams in modified_node
                    for field_name, field_value in list(modified_node.items()):
                        # Skip non-string values
                        if not field_value or not isinstance(field_value, str) or field_value.strip() == 'NA':
                            continue
                        
                        # Check if this is a mermaid field
                        display_type = get_display_type(self.discussion.node_type, field_name, data_model_helper)
                        if display_type == 'mermaid_chart':
                            self.logger.info(f"Validating mermaid diagram in {field_name}")
                            
                            # Validate the diagram
                            is_valid, error_msg = validate_mermaid_code(field_value)
                            
                            if not is_valid:
                                self.logger.info(f"Invalid mermaid diagram in {field_name}: {error_msg}")
                                
                                # Try to fix the diagram using LLM
                                fixed_diagram, is_fixed = await fix_mermaid_with_llm(
                                    self.discussion.llm,  # Use the LLM interface from the discussion
                                    field_value,
                                    error_msg
                                )
                                
                                if is_fixed:
                                    self.logger.info(f"Successfully fixed mermaid diagram in {field_name}")
                                    modified_node[field_name] = fixed_diagram
                                else:
                                    self.logger.warning(f"Could not fix mermaid diagram in {field_name}")
                    
                    # Also validate mermaid diagrams in new child nodes
                    if new_child_nodes and isinstance(new_child_nodes, list):
                        for i, child_node in enumerate(new_child_nodes):
                            if not isinstance(child_node, dict):
                                continue
                            
                            child_type = child_node.get('Type')
                            if not child_type:
                                continue
                            
                            for field_name, field_value in list(child_node.items()):
                                # Skip non-string values
                                if not field_value or not isinstance(field_value, str) or field_value.strip() == 'NA':
                                    continue
                                
                                # Check if this is a mermaid field
                                display_type = get_display_type(child_type, field_name, data_model_helper)
                                if display_type == 'mermaid_chart':
                                    self.logger.info(f"Validating mermaid diagram in child node {i}, field {field_name}")
                                    
                                    # Validate the diagram
                                    is_valid, error_msg = validate_mermaid_code(field_value)
                                    
                                    if not is_valid:
                                        self.logger.info(f"Invalid mermaid diagram in child node {i}, field {field_name}: {error_msg}")
                                        
                                        # Try to fix the diagram using LLM
                                        fixed_diagram, is_fixed = await fix_mermaid_with_llm(
                                            self.discussion.llm,
                                            field_value,
                                            error_msg
                                        )
                                        
                                        if is_fixed:
                                            self.logger.info(f"Successfully fixed mermaid diagram in child node {i}, field {field_name}")
                                            child_node[field_name] = fixed_diagram
                                        else:
                                            self.logger.warning(f"Could not fix mermaid diagram in child node {i}, field {field_name}")
                
                except Exception as e:
                    # Log the error but don't interrupt the flow
                    self.logger.error(f"Error validating mermaid diagrams: {str(e)}")
                    import traceback
                    self.logger.error(traceback.format_exc())
            # ===== END OF MERMAID VALIDATION CODE =====

            self.discussion.get_modifications_from_llm_output()
            self.discussion.modifications['created_at'] = generate_timestamp()

            self.discussion.modifications_history.append(self.discussion.modifications)
            self.logger.info(
                "Updating discussion node with modifications",
                extra=self.get_log_context(
                    method='capture_discussion_output',
                    operation_id=operation_id,
                    stage='update_node',
                    discussion_id=self.discussion.discussion_id,
                    history_count=len(self.discussion.modifications_history)
                )
            )
            await self.discussion.update_discussion_node(self.discussion.discussion_id, modifications=self.discussion.modifications,
                                            modifications_history=self.discussion.modifications_history)
            self.logger.info(
                "Successfully captured discussion output",
                extra=self.get_log_context(
                    method='capture_discussion_output',
                    operation_id=operation_id,
                    stage='complete',
                    discussion_id=self.discussion.discussion_id,
                    node_type=self.discussion.node_type,
                    modifications_count=len(self.discussion.modifications_history)
                )
            )
            modifications = {
                "modifications": self.discussion.modifications_history
            }
            self.discussion.retrieved_info['latest_modifications'] = self.discussion.modifications
            self.discussion.discussion_so_far.append({'role': 'assistant', 'created_at': generate_timestamp(), 'content': (json.dumps(modifications))})
            return {'content': modifications, 'skip_last_call': True}
        except Exception as e :
            error_msg = f"Error in capture_discussion_output : {str(e)}"
            stack_trace = traceback.format_exc()
            self.logger.error(
                error_msg,
                extra=self.get_log_context(
                    method='capture_discussion_output',
                    operation_id=operation_id,
                    stage='error',
                    discussion_id=getattr(self.discussion, 'discussion_id', None),
                    node_type=getattr(self.discussion, 'node_type', None),
                    error_type='capture_error',
                    error_category=e.__class__.__name__,
                    stack_trace=stack_trace
                )
            )
            return
                
    async def get_modifications_from_discussion(self, function_args):
        self.discussion.modifications = self.get_modifications_from_llm_output(self.discussion.node_type, self.discussion.root_node_type,
                                                                    self.discussion.discussion_type, function_args)

        self.discussion.modifications['created_at'] = generate_timestamp()

        self.discussion.modifications_history.append(self.discussion.modifications)

        await self.discussion.update_discussion_node(self.discussion.discussion_id, modifications=self.discussion.modifications,
                                          modifications_history=self.discussion.modifications_history)

        return self.discussion.modifications  # Return the current modifications, not the history
    
    def get_modifications_from_llm_output(self, node_type, root_node_type, discussion_type, function_args):
        config = self.current_configuration
        modified_node = function_args.get('modified_node', {})
        
        self.modifications = {
            'modified_node': modified_node ,
            'new_child_nodes': modified_node.get('new_child_nodes', []) ,
            'modified_children': modified_node.get('modified_child_nodes', []) ,
            'modified_siblings': modified_node.get('modified_sibling_nodes', []) ,
            'modified_similar_nodes': modified_node.get('modified_other_nodes', []) ,
            'new_relationships': modified_node.get('new_relationships', []) ,
            'modified_relationships': modified_node.get('modified_relationships', []),
        }
        
        for field in config.get('additional_fields', []):
            self.modifications[field.lower().replace('-', '_')] = modified_node.get(field)

        return self.modifications

    async def find_relevant_nodes(self, parent_node, search_terms, node_types = None):
        import asyncio
        from app.connection.establish_db_connection import get_vector_db
        from app.connection.llm_init import get_llm_interface
        llm_interface = get_llm_interface()
        vector_db = get_vector_db()
        db = get_node_db()
        min_items= 3
        max_items= 10
        root_node = await db.get_root_node(parent_node)
        if root_node:
            root_id = root_node['id']
        else:
            root_id = parent_node
        relevant_nodes = []
        embedding_search = await asyncio.to_thread(llm_interface.generate_embedding, {'content': search_terms})
        if not node_types:
            if self.discussion.node_type == 'RequirementRoot':
                node_types = ['Epic']
            elif self.discussion.node_type == 'Epic':
                node_types = ['Epic','UserStory']
            elif self.discussion.node_type == 'ArchitecturalRequirement':
                node_types = ['Epic','UserStory','FunctionalRequirement','NonFunctionalRequirement']
            else:
                node_types = ['Epic','UserStory','FunctionalRequirement','NonFunctionalRequirement']
        descendants = await db.get_descendant_nodes(root_id, node_types, None, 100)
        for node in descendants:
            embedding_node = await vector_db.get_node_embedding(node['id'])
            if not embedding_node:
                await vector_db.update_node_in_vector_db(node['id'], node, node.get("Type"))
                embedding_node = await vector_db.get_node_embedding(node['id'])
            if embedding_node:
                def cosine_similarity(vec1, vec2):
                    import numpy as np
                    vec1 = np.array(vec1)
                    vec2 = np.array(vec2)
                    dot_product = np.dot(vec1, vec2)
                    norm_vec1 = np.linalg.norm(vec1)
                    norm_vec2 = np.linalg.norm(vec2)
                    if norm_vec1 == 0 or norm_vec2 == 0:
                        return 0.0
                    return dot_product / (norm_vec1 * norm_vec2)
                similarity = abs(cosine_similarity(embedding_search, embedding_node))
                if not relevant_nodes:
                    relevant_nodes.append((similarity,node))
                else:
                    inserted = False
                    for i in range(0,len(relevant_nodes)):
                        if similarity > relevant_nodes[i][0]:
                            relevant_nodes.insert(i,(similarity,node))
                            inserted = True
                            break
                    if not inserted:
                        relevant_nodes.append((similarity,node))
                if len(relevant_nodes) > max_items:
                    relevant_nodes.pop()
        while len(relevant_nodes) > min_items:
            if relevant_nodes[-1][0] < 0.8:
                relevant_nodes.pop()
            else:
                break
        result = [t[1]['id'] for t in relevant_nodes]
        return result

    async def get_nodes(self, node_ids):
        status = "ERROR"
        values = []
        for node_id in node_ids:
            node = await self.discussion.db.get_node_by_id(node_id)
            if node:
                values.append(node)
        if values:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{values}"
        }
        return result

    async def find_nodes_by_types(self, parent_node:int, node_types:list):
        status = "ERROR"
        values = []
        db = get_node_db()
        root_node = await db.get_node_by_id(parent_node)
        if root_node['properties'].get('Type') == 'Epic' and 'UserStory' in node_types:
            root_id = parent_node
        else:
            root_node = await db.get_root_node(parent_node)
            if root_node:
                root_id = root_node['id']
            else:
                root_id = parent_node
        nodes = await db.get_descendant_nodes(root_id, node_types, None, 100)
        for node in nodes:
            node_id = node.get('id')
            node_type = node['properties'].get('Type','unknown')
            values.append( {'id': node_id, 'Type': node_type})
        if values:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{values}"
        }
        return result