from app.discussions.discussion_factory import DiscussionFactory
from app.celery_app import user_context
import logging

def setup_logger(name, log_file, level=logging.INFO):
    """Function to set up a logger for logging to a file."""
    logger = logging.getLogger(name)

    # Check if the logger already has handlers to prevent adding duplicate handlers
    if not logger.handlers:
        handler = logging.FileHandler(log_file)
        formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)

    logger.setLevel(level)
    logger.propagate = False
    return logger

update_logger_agent = setup_logger('update_logger_agent', 'logs/discussions_agents.log')

async def conduct_discussion(node_id, node_type, root_node_type, discussion_type, logging_context=None, semaphore=None, levels=0, supervisor=None):
    user_id = user_context.get()
    print("conduct_discussion_user", user_id)
    title = f"{discussion_type} discussion"
    description = f"This is an automated discussion for this {node_type}"
    print(node_id, node_type, root_node_type, discussion_type)
    discussion = await DiscussionFactory.create_discussion(
        discussion_type,
        node_type=node_type,
        node_id=node_id,
        discussion_node_id=None,
        title=title,
        description=description,
        logging_context=logging_context,
        semaphore=semaphore,
        levels=levels,
        invocation_type="autoconfig",
        supervisor=supervisor,
        current_user=user_id
    )

    steps = discussion.get_visible_steps()
    
    for step in steps:
        
        await discussion.execute_to_step(step['name'])

    return True

def create_root_node_properties(node_type, root_type, parent_node):
    properties = {
        'Title': f"{parent_node['properties']['Title']}",
        'Description': f"This is the root {node_type} for the {root_type} {parent_node['properties']['Title']}",
        'Details': 'No details provided',
    }
    if node_type == 'Architecture':
        properties['Title'] = f"System Architecture for {parent_node['properties']['Title']}"
    if node_type == 'WorkItem':
        properties['Dependencies_state'] = "configured"  # We don't need to track dependencies for the root work item
    return properties

