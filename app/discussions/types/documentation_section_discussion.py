from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
import json
import inspect
from app.discussions.tools.discussion_tool import DiscussionTools
from app.models.user_model import LLMModel
from app.utils.auth_utils import get_current_user
import os
from app.utils.prodefn.docs_tool import DocsTools
from app.utils.prodefn.docs_session_manager import get_or_create_session
from app.utils.file_utils.upload_utils import upload_and_process,get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id
from app.utils.node_version_manager import NodeVersionManager
from datetime import datetime

base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
documentation_mapping_path = os.path.join(base_dir, 'discussions', 'types', 'documentation_mapping.json')

class SectionManagementDiscussion(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description)
        self.template_name = "documentation_section_management.prompt"
        self.function_schema_type = "SubsectionManagementConfiguration"
        self.version_manager = NodeVersionManager()
        self.background_info = None

    async def retrieve_info(self):
        """Retrieve context information for documentation section management"""
        await super().retrieve_info()
        try:
            if "DocumentationRoot" in self.node['labels']:
                # Get existing section nodes directly instead of relying on Sub_Sections property
                existing_sections = await self.db.get_child_nodes(self.node_id, "Sub_Section")
                
                # Format the sections for display
                self.retrieved_info['current_sections'] = sorted([
                    {
                        'title': section['properties'].get('Title', ''),
                        'type': section['properties'].get('SectionType', ''),
                        'description': section['properties'].get('Description', ''),
                        'order': section['properties'].get('Order', 999),
                        'content': section['properties'].get('Content', ''),
                        'configuration_state': section['properties'].get('configuration_state', '')
                    }
                    for section in existing_sections
                ], key=lambda x: x['order'])

                self.retrieved_info['existing_section_nodes'] = existing_sections

                # Get documentation type specific configuration
                doc_type = self.node['properties'].get('DocumentationType')
                if doc_type:
                    with open(documentation_mapping_path , 'r') as f:
                        doc_mappings = json.load(f)
                        if doc_type in doc_mappings:
                            self.retrieved_info['documentation_config'] = doc_mappings[doc_type]

            self.update_logger.info(f"Retrieved sections: {self.retrieved_info.get('current_sections')}")

             # Get project node and details
            project_node = self.retrieved_info['root_node']
            self.project_id = project_node['id']

            existing_session = await get_or_create_session(self.current_user, self.project_id)
            self.doc_session_id = existing_session.get("session_id")

            print(self.doc_session_id)

            # Retrieve existing containers
            containers = await self.db.get_child_nodes(self.node_id, "Container")
            self.retrieved_info['containers'] = containers

            architecture_root = await self.db.get_child_nodes(self.retrieved_info['root_node']['id'], "ArchitectureRoot")
            if architecture_root:
                arch_req_node = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
                if arch_req_node:
                    # Get full requirement nodes
                    functional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "FunctionalRequirement")
                    nonfunctional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "NonFunctionalRequirement")
                    
                    architectural_reqs = {
                        'functional_requirements': functional_reqs,
                        'architectural_requirements': nonfunctional_reqs
                    }

            # Retrieve existing external systems and users
            system_context = self.retrieved_info['current_node']
            # Retrieve existing external systems and users
            system_context = self.retrieved_info['current_node']
            self.retrieved_info['external_systems'] = system_context['properties'].get('ExternalSystems', '')
            self.retrieved_info['users'] = system_context['properties'].get('Users', '')

            design_node = await self.db.get_node_by_id(self.node_id)
            algorithms = design_node['properties'].get('Algorithms', '')
            class_diagrams = await self.db.get_child_nodes(self.node_id, "ClassDiagram") or None
            state_diagrams = await self.db.get_child_nodes(self.node_id, "StateDiagram") or None
            sequence_diagrams = await self.db.get_child_nodes(self.node_id, "SequenceDiagram") or None
            test_cases = await self.db.get_child_nodes(self.node_id, "Test") or None

            # Retrieve existing containers
            containers = await self.db.get_child_nodes(system_context['id'], "Container")
            parent_node = await self.db.get_parent_node(self.node_id)
            components = await self.db.get_child_nodes(parent_node['id'], "Component")

            container_component_details = []
            container_component_details.append({
                'container': self.node_id,
                'components': components
            })

            # Structure current background info with complete node information
            current_background_info = {
                'project_context': {
                    'node_id': project_node['id'] if project_node else None,
                    'properties': project_node['properties'] if project_node else {}
                },
                'system_context': {
                    'users': self.retrieved_info['users'],
                    'external_systems': self.retrieved_info['external_systems'],
                    'container': parent_node,  # Already storing full container nodes
                    'container_component_details': container_component_details
                },
                'requirements_context': {
                    'functional_requirements': architectural_reqs['functional_requirements'],
                    'architectural_requirements': architectural_reqs['architectural_requirements'],
                    'parent_node': arch_req_node[0] if arch_req_node else None  # Store parent node for context
                },
                'design_context': {
                    'algorithms': algorithms,
                    'state_diagrams': state_diagrams,
                    'sequence_diagrams': sequence_diagrams,
                    'class_diagrams': class_diagrams,   
                    'test_cases': test_cases

                }
            }
            # Handle reconfiguration
            if self.is_reconfig():
                self.model_name = LLMModel.gpt_4_1.value
                stored_background_info = await self.version_manager._get_context(self.node_id)
                
                if stored_background_info:
                    self.retrieved_info.update({
                        'original_node': self.node,
                        'background_info': stored_background_info.get('background_info', current_background_info),
                        'user_interaction': {
                            'input': self.node['properties'].get('user_inputs', '[]')
                        },
                        'change_log': self.node['properties'].get('change_log', []),
                        'change_reason': self.node['properties'].get('change_reason', '')
                    })
                    # Add safety check
                    if not self.retrieved_info['background_info']:
                        self.retrieved_info['background_info'] = current_background_info
                else:
                    self.retrieved_info.update({
                        'original_node': self.node,
                        'background_info': current_background_info,
                        'user_interaction': {
                            'input': self.node['properties'].get('user_inputs', '[]')
                        },
                        'change_log': self.node['properties'].get('change_log', []),
                        'change_reason': self.node['properties'].get('change_reason', '')
                    })
                
                self.retrieved_info['new_background'] = current_background_info
            else:
                # For initial configuration
                self.retrieved_info['background_info'] = current_background_info
                self.background_info = current_background_info  # Set class variable
                self.retrieved_info['new_background'] = current_background_info

            return self.retrieved_info
        except Exception as e:
            self.update_logger.error(f"Error in retrieve_info: {str(e)}")
            raise

    async def get_modifications_from_llm_output(self):
        """Process the LLM output into modifications"""
        try:
            modified_node = self.modifications.get('modified_node', {})
            
            # Get current sections
            current_sections = json.loads(self.node['properties'].get('Sub_Sections', '[]'))
            current_section_titles = {s['title'] for s in current_sections}
            
            # Process modifications
            if 'Sub_Sections' in modified_node:
                new_sections = modified_node['Sub_Sections']
                if isinstance(new_sections, str):
                    new_sections = json.loads(new_sections)
                
                # Find truly new sections
                new_section_titles = {s['title'] for s in new_sections}
                actually_new_sections = [
                    s for s in new_sections 
                    if s['title'] not in current_section_titles
                ]
                
                # Create child nodes for new sections
                if actually_new_sections:
                    self.modifications['new_child_nodes'] = [
                        {
                            'Title': section['title'],
                            'Type': 'Sub_Section',
                            'SectionType': section['type'],
                            'Description': section['description'],
                            'Order': section['order'],
                            'Content': '',
                            'configuration_state': 'not_configured'
                        }
                        for section in actually_new_sections
                    ]
                
                # Update the Sub_Sections property with the complete list
                modified_node['Sub_Sections'] = json.dumps(new_sections)
            
            self.modifications['modified_node'] = modified_node
            return self.modifications

        except Exception as e:
            self.update_logger.error(f"Error in get_modifications_from_llm_output: {str(e)}")
            raise

    async def merge_captured_items(self):
        try:
            # Extract the modifications
            modified_node = self.modifications.get('modified_node', {})
            new_child_nodes = self.modifications.get('new_child_nodes', [])
            
            # Update the current node if needed
            if modified_node:
                await self.db.update_node_by_id(self.node_id, modified_node)
                
            # Create new section nodes with proper type and structure
            if new_child_nodes:
                formatted_nodes = []
                for node in new_child_nodes:
                    formatted_node = {
                        'Title': node.get('Title'),
                        'Description': node.get('Description'),
                        'Content': node.get('Content', ''),
                        'SectionType': node.get('Type', 'standard'),
                        'Order': node.get('Order', 0),
                        'Version': node.get('Version', '1.0'),
                        'configuration_state': 'not_configured'
                    }
                    formatted_nodes.append(formatted_node)
                    
                # Create the nodes with explicit type
                created_nodes = await self.db.create_nodes(
                    ['Sub_Section'], 
                    formatted_nodes, 
                    self.node_id
                )
                
                self.modifications['created_nodes'] = created_nodes

            return True
            
        except Exception as e:
            self.update_logger.error(f"Error in merge_captured_items: {str(e)}")
            raise

    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        return config_state == 'configured'

# Register the discussion type
DiscussionFactory.register('section_management', SectionManagementDiscussion, "DocumentationRoot")