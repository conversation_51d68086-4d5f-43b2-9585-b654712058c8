from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from app.utils.kg_inspect.kg_tool import KgTools
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.kg_inspect.knowledge import Knowledge

class SystemContextExtraction(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None, project_id=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="SystemContext")
        self.template_name = "system_context_extraction.prompt"
        self.function_schema_type = "SystemContextConfiguration"
        self.model_name = 'claude-3.5'
        self.project_id = project_id
        self.kg_tools = None
        self.discussion_tools = None

    async def initialize(self):
        """Initialize discussion with required tools and schemas"""
        await super().async_initialize()
        
        # Get project node and verify
        project_node = await self.db.get_node_by_id(self.project_id)
        if not project_node:
            raise ValueError(f"Project with ID {self.project_id} not found")
            
        # Initialize KgTools with project context
        self.kg_tools = KgTools(
            base_path=self.base_path,
            logger=self.logger,
            session_id=self.session_id
        )

        # Initialize standard DiscussionTools
        self.discussion_tools = DiscussionTools(
            base_path=self.base_path,
            logger=self.logger,
            user_id=self.session_id,
            discussion=self
        )

        # Get system context node if it exists
        system_contexts = await self.db.get_child_nodes(self.project_id, "SystemContext")
        if system_contexts:
            self.node_id = system_contexts[0]['id']

        self.function_schemas = self.kg_tools.function_schemas + self.discussion_tools.function_schemas

    async def retrieve_info(self):
        """Retrieve existing project and knowledge information"""
        await super().retrieve_info()
        
        # Get project details
        project_node = await self.db.get_node_by_id(self.project_id)
        self.retrieved_info['project_details'] = {
            'title': project_node['properties'].get('Title', ''),
            'description': project_node['properties'].get('Description', ''),
            'architecture_pattern': project_node['properties'].get('ArchitecturePattern', '')
        }

        # Get knowledge instance based on project ID
        try:
            knowledge = Knowledge.getKnowledge(id=self.project_id)
            if knowledge:
                # Add any relevant knowledge base information
                self.retrieved_info['source_languages'] = knowledge.getKeyValue('source-languages')
                self.retrieved_info['search_terms'] = knowledge.getKeyValue('search-terms')
        except Exception as e:
            self.logger.error(f"Error retrieving knowledge for project {self.project_id}: {e}")

        return self.retrieved_info

    def get_modifications_from_llm_output(self):
        """Process LLM output to extract modifications"""
        llm_output = self.modifications.get('modified_node', {})
        
        self.modifications.update({
            'external_systems': llm_output.get('ExternalSystems', []),
            'users': llm_output.get('Users', []),
            'child_node_types': ['Container']
        })
        
        return self.modifications

    async def merge_captured_items(self):
        """Merge captured items including interface creation"""
        await super().merge_captured_items()
        
        # Handle container creation
        created_nodes = self.modifications.get('created_nodes', [])
        for node in created_nodes:
            if "Container" in node.get('labels', []):
                await self.db.create_relationship(
                    self.node_id,
                    node['id'],
                    "CONTAINS",
                    {"description": f"System Context contains {node['properties'].get('Title', 'Container')}"}
                )

        # Handle interface creation
        interfaces = self.modifications.get('new_relationships', [])
        if interfaces:
            await self.create_interface_nodes(interfaces)

    async def create_interface_nodes(self, interfaces):
        """Create interface nodes from discovered interfaces"""
        for interface in interfaces:
            source_id = self.get_node_id(interface['source'])
            target_id = self.get_node_id(interface['target'])
            
            if source_id and target_id:
                interface_props = {
                    "Title": interface['name'],
                    "Description": interface['description'],
                    "Type": "Interface"
                }
                interface_node = await self.db.create_node(
                    ["Interface"],
                    interface_props,
                    target_id
                )
                
                await self.db.create_relationship(
                    source_id,
                    target_id,
                    "INTERFACES_WITH",
                    {
                        "name": interface['name'],
                        "type": interface['type'],
                        "description": interface['description']
                    }
                )

    def get_node_id(self, node_reference):
        """Get actual node ID from reference"""
        if '-' in node_reference:
            prefix, num = node_reference.split('-', 1)
            if prefix == 'NEW':
                for node in self.modifications.get('created_nodes', []):
                    if node['properties'].get('ID') == node_reference:
                        return node['id']
            elif prefix == 'EXISTING':
                try:
                    return int(num)
                except ValueError:
                    pass
        return None

# Register with factory
DiscussionFactory.register('extraction', SystemContextExtraction, "SystemContext")