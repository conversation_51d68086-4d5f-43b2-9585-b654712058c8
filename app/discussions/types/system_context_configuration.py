# system_context_configuration.py

from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
import json
from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import json
from app.core.function_schema_generator import get_function_schema
# system_context_configuration.py
from app.core.Settings import settings
from datetime import datetime
import os
from app.connection.establish_db_connection import get_mongo_db
from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
import json
from app.llm import LLMInterface
from app.models.user_model import LLMModel
from app.utils.kg_inspect.kg_tool import KgTools
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.kg_inspect.knowledge import Knowledge
from app.utils.prodefn.docs_tool import DocsTools
import boto3
from app.utils.file_utils.upload_utils import upload_and_process,get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id
from app.core.Settings import settings
from app.utils.logs_utils import get_path  # Import settings
import asyncio
import inspect
from app.utils.prodefn.docs_session_manager import get_or_create_session
from app.utils.node_version_manager import NodeVersionManager


class SystemContextConfiguration(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="SystemContext")
        self.template_name = None
        self.template_name = None
        self.function_schema_type = "SystemContextConfiguration"
        self.model_name = 'gpt-4o'
        self.project_id = None
        self.doc_tools = None
        self.discussion_tools = None
        self.function_mapping = {}
        self.function_executor = None
        self.function_schemas = []
        self.schemas_initialized = False
        self.project_knowledge = False
        self.s3_client = boto3.client('s3',
                          aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                          aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                          region_name=settings.AWS_REGION
                          )
        self.version_manager = NodeVersionManager()
        self.background_info = None

    async def retrieve_info(self):
        await super().retrieve_info()
        
        # Get project node and architecture pattern
        project_node = self.retrieved_info['root_node']
        architecture_pattern = project_node['properties'].get('ArchitecturePattern', 'adaptive')
        
        # Set appropriate template based on architecture pattern
        if architecture_pattern.lower() in ['monolithic-application', 'monolithic-service']:
            self.template_name = "monolithic_system_context_configuration.prompt"
            self.update_logger.info(f"Using monolithic system context template for {architecture_pattern}")
        elif architecture_pattern.lower() == 'multi-container-service':
            self.template_name = "multicontainer_service_configuration.prompt"
            self.update_logger.info("Using multi-container service system context template")
        else:
            self.template_name = "system_context_configuration.prompt"
            self.update_logger.info("Using standard system context template")
        
        self.retrieved_info['architecture_pattern'] = architecture_pattern

        
       # Get project node and architecture pattern
        project_node = self.retrieved_info['root_node']
        self.retrieved_info['project_details'] = {
            'description': project_node['properties'].get('Description', ''),
            'scope': project_node['properties'].get('Scope', ''),
            'architecture_strategy': project_node['properties'].get('ArchitectureStrategy', ''),
            'additional_details': project_node['properties'].get('AdditionalDetails', {}),
            'team_composition': project_node['properties'].get('TeamComposition', '')
        }
        
        self.retrieved_info['architecture_pattern'] = architecture_pattern

        # Retrieve architectural requirements - store full nodes
        architectural_reqs = {
            'functional_requirements': [],
            'architectural_requirements': []
        }

        # Retrieve architectural requirements
        architecture_root = await self.db.get_child_nodes(self.retrieved_info['root_node']['id'], "ArchitectureRoot")
        if architecture_root:
            arch_req_nodes = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
            if arch_req_nodes:
                # self.retrieved_info['functional_requirements'] = arch_req_nodes[0]['properties'].get('functional_requirements', '')
                # self.retrieved_info['architectural_requirements'] = arch_req_nodes[0]['properties'].get('architectural_requirements', '')
                # Get full requirement nodes
                functional_reqs = await self.db.get_child_nodes(arch_req_nodes[0]['id'], "FunctionalRequirement")
                nonfunctional_reqs = await self.db.get_child_nodes(arch_req_nodes[0]['id'], "NonFunctionalRequirement")
                
                architectural_reqs = {
                    'functional_requirements': functional_reqs,
                    'architectural_requirements': nonfunctional_reqs
                }

        # Store in retrieved_info for template use
        self.retrieved_info.update(architectural_reqs)

        # Retrieve existing external systems and users
        system_context = self.retrieved_info['current_node']
        self.retrieved_info['external_systems'] = system_context['properties'].get('ExternalSystems', '')
        self.retrieved_info['users'] = system_context['properties'].get('Users', '')
        
        # Retrieve existing containers
        containers = await self.db.get_child_nodes(self.node_id, "Container")
        self.retrieved_info['containers'] = containers

        # Retrieve existing INTERACTS_WITH relationships
        interactions = await self.db.get_relationships_involving_node(self.node_id, "INTERACTS_WITH")
        self.retrieved_info['existing_interactions'] = interactions

        # Retrieve existing CONTAINS relationships
        contains_relationships = await self.db.get_relationships_involving_node(self.node_id, "CONTAINS")
        self.retrieved_info['existing_contains'] = contains_relationships

        # Structure current background info with complete node information
        current_background_info = {
            'project_context': {
                'node_id': project_node['id'] if project_node else None,
                'properties': project_node['properties'] if project_node else {}
            },
            'system_context': {
                'users': self.retrieved_info['users'],
                'external_systems': self.retrieved_info['external_systems'],
                'containers': containers  # Already storing full container nodes
            },
            'requirements_context': {
                'functional_requirements': architectural_reqs['functional_requirements'],
                'architectural_requirements': architectural_reqs['architectural_requirements'],
                'parent_node': arch_req_nodes[0] if arch_req_nodes else None  # Store parent node for context
            }
        }

        # Handle reconfiguration
        if self.is_reconfig():
            # self.model_name = LLMModel.gpt_4_1.value
            stored_background_info = await self.version_manager._get_context(self.node_id)
            
            if stored_background_info:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': stored_background_info.get('background_info', current_background_info),
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
                # Add safety check
                if not self.retrieved_info['background_info']:
                    self.retrieved_info['background_info'] = current_background_info
            else:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': current_background_info,
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
            
            self.retrieved_info['new_background'] = current_background_info
        else:
            # For initial configuration
            self.retrieved_info['background_info'] = current_background_info
            self.background_info = current_background_info  # Set class variable
            self.retrieved_info['new_background'] = current_background_info

        return self.retrieved_info

    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        return config_state == 'configured'
    
    def get_modifications_from_llm_output(self):
        self.modifications['child_node_types'] = ['Container']
        
        # Extract external systems and users from LLM output
        llm_output = self.modifications.get('modified_node', {})
        self.modifications['external_systems'] = llm_output.get('ExternalSystems', [])
        self.modifications['users'] = llm_output.get('Users', [])
        
        # Extract interface relationships
        self.modifications['new_relationships'] = self.modifications.get('new_relationships', [])
        
        return self.modifications

    async def merge_captured_items(self):
        """
        Merges captured items handling all architectural patterns:
        - monolithic-application: Single container without interfaces
        - monolithic-service: Single container with exposed interfaces
        - multi-container: Multiple containers with interfaces between them
        """
         # Get background info by calling retrieve_info again
        await self.retrieve_info()
        # Save version info
        save_data = {
            'node_id': self.node_id,
            'properties': self.modifications['modified_node'],
            'background_info': self.retrieved_info['new_background'],
            'user_interaction': {
                'timestamp': datetime.now().isoformat(),
                'action': 'reconfig',
                'input': self.modifications['modified_node'].get('user_inputs', ''),
                'reason': self.modifications['modified_node'].get('change_reason', '')
            }
        }
        await self.version_manager.save_node_info(save_data)
        
        if not self.modifications.get('modified_node'):
            self.update_logger.warning("No modifications to merge")
            return
         # Get background info by calling retrieve_info again
        await self.retrieve_info()

        try:
            if self.is_reconfig():
                if self.modifications['modified_node'].get('changes_needed', False):
                    await super().merge_captured_items()
                    #Hack - Discussion class instance changes...FIX ME

                    # Save version info
                    save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['new_background'],
                        'user_interaction': {
                            'timestamp': datetime.now().isoformat(),
                            'action': 'reconfig',
                            'input': self.modifications['modified_node'].get('user_inputs', ''),
                            'reason': self.modifications['modified_node'].get('change_reason', '')
                        }
                    }
                    await self.version_manager.save_node_info(save_data)
                    created_nodes = self.modifications.get('created_nodes', [])
                    
                    # Get architecture pattern
                    architecture_pattern = self.retrieved_info.get('architecture_pattern', '').lower()
                    is_monolithic_service = architecture_pattern == 'monolithic-service'
                    is_monolithic_application = architecture_pattern == 'monolithic-application'
                    
                    # Handle CONTAINS relationships for created child nodes
                    for node in created_nodes:
                        if "Container" in node.get('labels', []):
                            await self.db.create_relationship(
                                self.node_id,
                                node['id'],
                                "CONTAINS",
                                {"description": f"System Context contains {node['properties'].get('Title', 'Container')}"}
                            )

                    # Get interfaces from modifications
                    interfaces = self.modifications.get('new_relationships', [])
                    if not interfaces:
                        self.update_logger.info("No interfaces to process")
                        return

                    if is_monolithic_service:
                        await self.handle_monolithic_service_interfaces()
                    elif is_monolithic_application:
                        # No interfaces to process for monolithic application
                        self.update_logger.info("Monolithic application pattern - no interfaces to process")
                    else:
                        # For multi-container pattern, handle interfaces between containers
                        await self.handle_multi_container_interfaces(created_nodes, interfaces)

                    # Clean up temporary IDs
                    await self.cleanup_temporary_ids(created_nodes)

                    

            else:
                await super().merge_captured_items()
                #Hack - Discussion class instance changes...FIX ME

                # Save version info
                save_data = {
                    'node_id': self.node_id,
                    'properties': self.modifications['modified_node'],
                    'background_info': self.retrieved_info['new_background'],
                    'user_interaction': {
                        'timestamp': datetime.now().isoformat(),
                        'action': 'initial_config',
                        'input': self.modifications['modified_node'].get('user_inputs', ''),
                        'reason': self.modifications['modified_node'].get('change_reason', '')
                    }
                }
                await self.version_manager.save_node_info(save_data)

                created_nodes = self.modifications.get('created_nodes', [])
                
                # Get architecture pattern
                architecture_pattern = self.retrieved_info.get('architecture_pattern', '').lower()
                is_monolithic_service = architecture_pattern == 'monolithic-service'
                is_monolithic_application = architecture_pattern == 'monolithic-application'
                
                # Handle CONTAINS relationships for created child nodes
                for node in created_nodes:
                    if "Container" in node.get('labels', []):
                        await self.db.create_relationship(
                            self.node_id,
                            node['id'],
                            "CONTAINS",
                            {"description": f"System Context contains {node['properties'].get('Title', 'Container')}"}
                        )

                # Get interfaces from modifications
                interfaces = self.modifications.get('new_relationships', [])
                if not interfaces:
                    self.update_logger.info("No interfaces to process")
                    return

                if is_monolithic_service:
                    await self.handle_monolithic_service_interfaces()
                elif is_monolithic_application:
                    # No interfaces to process for monolithic application
                    self.update_logger.info("Monolithic application pattern - no interfaces to process")
                else:
                    # For multi-container pattern, handle interfaces between containers
                    await self.handle_multi_container_interfaces(created_nodes, interfaces)

                # Clean up temporary IDs
                await self.cleanup_temporary_ids(created_nodes)

                

        except Exception as e:
            self.update_logger.error(f"Error in merge_captured_items: {str(e)}")
            raise

    async def handle_monolithic_service_interfaces(self):
        """Handle interface creation for monolithic service pattern"""
        try:
            # Get all child containers
            containers = await self.db.get_child_nodes(self.node_id, "Container")
            
            # Get the internal container - we know there's exactly one for monolithic
            internal_container = next(
                container for container in containers 
                if container['properties'].get('ContainerType', 'internal') == 'internal'
            )
            
            # Create interfaces for the internal container
            await self.create_monolithic_interfaces(
                internal_container['id'], 
                self.modifications.get('interfaces', [])
            )

        except Exception as e:
            self.update_logger.error(f"Error handling monolithic service interfaces: {str(e)}")
            raise


    async def create_monolithic_interfaces(self, container_id, interfaces):
        """Create interface node for monolithic container"""
        try:
            # Create a single interface node for the container
            interface_node = await self.db.create_node(
                ["Interface"],
                {
                    "Title": "Public API Interfaces",
                    "Description": "External APIs exposed by the monolithic system"
                },
                container_id
            )
            
            # Transform interfaces into the expected format
            incoming_interfaces = []
            for interface in interfaces:
                if isinstance(interface, dict):
                    incoming_interface = {
                        'name': interface.get('name', 'Unnamed Interface'),
                        'description': interface.get('description', 'Description Not Available'),
                        'source_component_id': container_id,  # Set container as source
                        'interfaceType': interface.get('interfaceType','Unknown')
                    }
                    incoming_interfaces.append(incoming_interface)
            
            # Update the interface node with all interfaces
            if incoming_interfaces:
                await self.db.update_node_by_id(
                    interface_node['id'],
                    {"incoming_interfaces": json.dumps(incoming_interfaces)}
                )
                
            self.update_logger.info(f"Created interface node with {len(incoming_interfaces)} interfaces for monolithic container")
                
        except Exception as e:
            self.update_logger.error(f"Error creating monolithic interfaces: {str(e)}")
            raise
    
    def get_node_id(self, node_reference, created_nodes):
        """Helper method to resolve node references"""
        if not isinstance(node_reference, str):
            self.update_logger.warning(f"Invalid node reference format: {node_reference}")
            return None
        if '-' in node_reference:
            prefix, num = node_reference.split('-', 1)
            if prefix == 'NEW':
                # Look up in recently created nodes
                for node in created_nodes:
                    if node['properties'].get('ID') == node_reference:
                        return node['id']
            elif prefix == 'EXISTING':
                try:
                    return int(num)
                except ValueError:
                    self.update_logger.warning(f"Invalid existing node reference: {node_reference}")
                    return None
        return None

    async def handle_multi_container_interfaces(self, created_nodes, interfaces):
        """
        Handles interface creation and relationships for multi-container architecture.
        
        Args:
            created_nodes (list): List of newly created container nodes
            interfaces (list): List of interface definitions
        """
        try:
            # Get architecture pattern
            architecture_pattern = self.retrieved_info.get('architecture_pattern', '').lower()
            
            # Special handling for multi-container-service
            if architecture_pattern == 'multi-container-service':
                # Get all internal containers
                containers = await self.db.get_child_nodes(self.node_id, "Container")
                internal_containers = [container for container in containers 
                                     if container['properties'].get('ContainerType') == 'internal']
                
                # Process provider interfaces for internal containers
                provider_interfaces = self.modifications.get('interfaces', [])
                if provider_interfaces:
                    for container in internal_containers:
                        await self.create_multi_container_service_interfaces(container['id'], provider_interfaces)
                
                # Continue with normal multi-container interface processing for container-to-container interfaces
            
            # Track containers and their interfaces
            containers_to_update = set()
            interface_mappings = {}

            # First pass: Collect all valid relationships
            for interface in interfaces:
                if not isinstance(interface, dict):
                    self.update_logger.warning(f"Invalid interface format: {interface}")
                    continue
                
                if 'source' not in interface or 'target' not in interface:
                    self.update_logger.warning(f"Missing source or target in interface: {interface}")
                    continue

                source_id = self.get_node_id(interface['source'], created_nodes)
                target_id = self.get_node_id(interface['target'], created_nodes)

                if not source_id or not target_id:
                    self.update_logger.warning(
                        f"Could not resolve source ({interface.get('source')}) or target ({interface.get('target')})"
                    )
                    continue

                # Add valid interface to mappings
                containers_to_update.add(target_id)
                if target_id not in interface_mappings:
                    interface_mappings[target_id] = []
                interface_mappings[target_id].append({
                    'source_id': source_id,
                    'interface': interface
                })

            # Second pass: Process each container's interfaces
            for container_id in containers_to_update:
                await self.create_or_update_container_interfaces(
                    container_id, 
                    interface_mappings[container_id]
                )

            # Third pass: Create the actual interface relationships in database
            for interface in interfaces:
                source_id = self.get_node_id(interface['source'], created_nodes)
                target_id = self.get_node_id(interface['target'], created_nodes)
                
                if source_id and target_id:
                    await self.db.create_relationship(
                        source_id,
                        target_id,
                        "INTERFACES_WITH",
                        {
                            'name': interface.get('name', 'Unnamed Interface'),
                            'description': interface.get('description', 'Description Not available'),
                            'interfaceType': interface.get('interfaceType','Unknown')
                        }
                    )
                    self.update_logger.info(
                        f"Created interface relationship: {interface.get('name')} "
                        f"from {source_id} to {target_id}"
                    )

        except Exception as e:
            self.update_logger.error(f"Error handling multi-container interfaces: {str(e)}")
            raise

    async def create_or_update_container_interfaces(self, container_id, interface_infos):
        """Helper method to create or update container interfaces"""
        try:
            # Get or create interface node
            interface_nodes = await self.db.get_child_nodes(container_id, "Interface")
            if not interface_nodes:
                container = await self.db.get_node_by_id(container_id)
                interface_node = await self.db.create_node(
                    ["Interface"],
                    {
                        "Title": f"Interfaces for {container['properties'].get('Title', 'Container')}",
                        "incoming_interfaces": "[]"
                    },
                    container_id
                )
            else:
                interface_node = interface_nodes[0]

            # Get existing interfaces
            try:
                existing_interfaces = json.loads(
                    interface_node['properties'].get('incoming_interfaces', '[]')
                )
            except json.JSONDecodeError:
                self.update_logger.warning(f"Invalid JSON in incoming_interfaces for container {container_id}")
                existing_interfaces = []

            # Add new interfaces
            for interface_info in interface_infos:
                source_id = interface_info['source_id']
                interface = interface_info['interface']
                
                new_interface = {
                    'name': interface.get('name', 'Unnamed Interface'),
                    'type': interface.get('type', 'REST API'),
                    'description': interface.get('description', 'Not Available'),
                    'source_component_id': source_id,
                    'interfaceType': interface.get('interfaceType','Unknown')
                }
                existing_interfaces.append(new_interface)

            # Update interface node
            await self.db.update_node_by_id(
                interface_node['id'],
                {"incoming_interfaces": json.dumps(existing_interfaces)}
            )

        except Exception as e:
            self.update_logger.error(f"Error creating/updating interfaces for container {container_id}: {str(e)}")
            raise

    async def cleanup_temporary_ids(self, created_nodes):
        """
        Removes temporary ID properties from created nodes after all relationships are established.
        
        Args:
            created_nodes (list): List of newly created nodes to clean up
        """
        try:
            for node in created_nodes:
                try:
                    # Using Neo4j REMOVE clause to delete the ID property
                    query = """
                    MATCH (n)
                    WHERE ID(n) = $node_id
                    REMOVE n.ID
                    """
                    await self.db.async_run(query, node_id=node['id'])
                    self.update_logger.info(f"Removed temporary ID field from node {node['id']}")
                    
                except Exception as node_error:
                    # Log error but continue with other nodes
                    self.update_logger.warning(
                        f"Failed to remove temporary ID from node {node['id']}: {str(node_error)}"
                    )
                    continue
                    
        except Exception as e:
            self.update_logger.error(f"Error in cleanup_temporary_ids: {str(e)}")
            # Don't raise the exception as this is a cleanup operation
            # We don't want to fail the entire operation if cleanup has issues

    async def create_multi_container_service_interfaces(self, container_id, interfaces):
        """Create interface node for each container in multi-container-service architecture"""
        try:
            # Create a single interface node for the container
            container = await self.db.get_node_by_id(container_id)
            container_title = container['properties'].get('Title', 'Internal Container')
            
            interface_node = await self.db.create_node(
                ["Interface"],
                {
                    "Title": f"Provider Interfaces for {container_title}",
                    "Description": f"Public APIs exposed by the {container_title}"
                },
                container_id
            )
            
            # Transform interfaces into the expected format
            incoming_interfaces = []
            
            # Filter interfaces for this specific container
            container_interfaces = [iface for iface in interfaces if iface.get('container_id') == container_id]
            
            for interface in container_interfaces:
                if isinstance(interface, dict):
                    incoming_interface = {
                        'name': interface.get('name', 'Unnamed Interface'),
                        'description': interface.get('description', 'Description Not Available'),
                        'source_component_id': container_id,  # Set container as source
                        'interfaceType': interface.get('interfaceType', 'Unknown'),
                        'type': interface.get('type', 'REST'),
                        'technology': interface.get('technology', 'HTTP/JSON')
                    }
                    incoming_interfaces.append(incoming_interface)
            
            # Update the interface node with all interfaces
            if incoming_interfaces:
                await self.db.update_node_by_id(
                    interface_node['id'],
                    {"incoming_interfaces": json.dumps(incoming_interfaces)}
                )
                
            self.update_logger.info(f"Created interface node with {len(incoming_interfaces)} interfaces for service container {container_title}")
                
        except Exception as e:
            self.update_logger.error(f"Error creating service container interfaces: {str(e)}")
            raise

DiscussionFactory.register('configuration', SystemContextConfiguration, "SystemContext")