import json
from jinja2 import Environment, FileSystemLoader
from app.connection.establish_db_connection import get_node_db
from app.connection.llm_init import get_llm_interface
from app.models.user_model import LLMModel
from app.utils.logs_utils import get_path
from llm_wrapper.core.llm_interface import LLMInterface
from app.telemetry.logger_config import get_logger
from app.connection.establish_db_connection import get_mongo_db
import os
class APIGenerator:
    def __init__(self):
        self.db = get_node_db()
        self.llm = LLMInterface(get_path(), 'interface_api_generator', mongo_handler = get_mongo_db())
        self.logger = get_logger(__name__)
        base_path = os.path.dirname(os.path.abspath(__file__))
        template_path = os.path.join(base_path, 'discussions' 'prompt_templates')
        self.env = Environment(loader=FileSystemLoader(template_path))

    async def generate_api_for_component(self, component_id):
        try:
            # Fetch component details
            component = await self.db.get_node_by_id(component_id)
            if not component:
                self.logger.error(f"Component with ID {component_id} not found")
                return None

            # Fetch Interface node
            interface_nodes = await self.db.get_child_nodes(component_id, "Interface")
            if not interface_nodes:
                self.logger.info(f"No Interface node found for component {component_id}")
                return None
            interface_node = interface_nodes[0]

            # Compile interface specifications and consumer details
            interfaces = []
            consumers = []
            for key, value in interface_node['properties'].items():
                if key.startswith('interface_'):
                    interface_data = json.loads(value)
                    interfaces.append(interface_data)
                    consumer = await self.db.get_node_by_id(interface_data['source_component_id'])
                    if consumer:
                        consumers.append(consumer)

            # Prepare data for LLM
            llm_input = {
                "provider": component,
                "consumers": consumers,
                "interfaces": interfaces
            }

            # Generate LLM prompt
            prompt = self.generate_api_design_prompt(llm_input)

            # Get LLM response
            llm_response = await self.llm.llm_interaction_wrapper(
                messages=[],
                user_prompt=prompt,
                system_prompt="You are an expert API designer. Design an API based on the provided component and interface information.",
                response_format={'type': 'json_object'},
                model=LLMModel.gpt_4_1.value,
                stream=False
            )

            # Process LLM response
            api_design = self.process_api_design_response(llm_response)

            # Store API design
            api_node = await self.db.create_node(["APIDesign"], api_design, component_id)

            # Update related nodes
            await self.update_nodes_with_api_reference(component_id, api_node['id'])

            return api_node

        except Exception as e:
            self.logger.error(f"Error generating API for component {component_id}: {str(e)}")
            return None

    def generate_api_design_prompt(self, input_data):
        template = self.env.get_template('interface_api_design.prompt')
        return template.render(input_data)

    def process_api_design_response(self, llm_response):
        try:
            if isinstance(llm_response, dict) and 'content' in llm_response:
                content = llm_response['content']
            elif isinstance(llm_response, str):
                content = llm_response
            else:
                self.logger.error(f"Unexpected LLM response type: {type(llm_response)}")
                return {}

            api_design = json.loads(content)
            return {
                "Title": api_design.get("api_name", "Generated API"),
                "Description": api_design.get("description", ""),
                "Endpoints": json.dumps(api_design.get("endpoints", [])),
                "AuthenticationMethod": api_design.get("authentication_method", ""),
                "Version": "1.0"
            }
        except json.JSONDecodeError:
            self.logger.error(f"Failed to parse LLM response: {content}")
            return {}

    async def update_nodes_with_api_reference(self, component_id, api_node_id):
        # Update component node
        await self.db.update_node_by_id(component_id, {"APIDesignId": api_node_id})

        # Update Interface node
        interface_nodes = await self.db.get_child_nodes(component_id, "Interface")
        if interface_nodes:
            await self.db.update_node_by_id(interface_nodes[0]['id'], {"APIDesignId": api_node_id})

        # Update consumer nodes
        interface_node = interface_nodes[0]
        for key, value in interface_node['properties'].items():
            if key.startswith('interface_'):
                interface_data = json.loads(value)
                consumer_id = interface_data['source_component_id']
                await self.db.update_node_by_id(consumer_id, {"ConsumedAPIId": api_node_id})

    async def consolidate_interfaces(self, component_id):
        # This method will be implemented later for interface consolidation
        pass