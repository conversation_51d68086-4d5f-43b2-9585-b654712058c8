from app.core.function_schema_generator import get_function_schema
from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import json

class Designapidocs(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Design")
        self.template_name = "design_apidocs.prompt"
        self.function_schema_type = "DesignAPIDocs"

    async def async_initialize(self):
        # Initialization specific to this configuration
        await super().async_initialize()
        # Additional async setup specific to this class can be added here



    async def retrieve_info(self):
        await super().retrieve_info()
        architecture_root = self.retrieved_info['nodes_in_path'][0]['id']
        architecture_node = self.retrieved_info['nodes_in_path'][-1]['id']
        arch_info = await self.get_architectural_node_info(architecture_root, architecture_node)
        self.retrieved_info.update(arch_info)
        return self.retrieved_info
        
    def get_modifications_from_llm_output(self):

        #super().get_modifications_from_llm_output(node_type, root_node_type, discussion_type, llm_output)

        #child_nodes = llm_output.get("APIDocs", [])
        self.modifications['child_node_types'] = ["APIDoc"]
        #print(llm_output)
        return self.modifications
    



# Registering the subclass in the factory
DiscussionFactory.register('apidocs', Designapidocs, "Design")