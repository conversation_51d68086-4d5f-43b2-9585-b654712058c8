from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import json
from app.core.function_schema_generator import get_function_schema
from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import json
from app.core.function_schema_generator import get_function_schema
# system_context_configuration.py
from app.models.user_model import LLMModel
from app.utils.node_version_manager import NodeVersionManager

from datetime import datetime
import os
from app.connection.establish_db_connection import get_mongo_db
from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
import json
from app.llm import LLMInterface
from app.utils.kg_inspect.kg_tool import KgTools
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.kg_inspect.knowledge import Knowledge
from app.utils.prodefn.docs_tool import DocsTools

from app.core.Settings import settings
from app.utils.logs_utils import get_path  # Import settings
import asyncio
import inspect
from app.utils.prodefn.docs_session_manager import get_or_create_session
from app.utils.datetime_utils import generate_timestamp
class WorkItemConfiguration(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="WorkItem")
        self.template_name = "workitem_configuration.prompt"
        self.function_schema_type = "WorkItemConfiguration"
        self.project_id = None
        self.doc_tools = None
        self.discussion_tools = None
        self.function_mapping = {}
        self.function_executor = None
        self.function_schemas = []
        self.schemas_initialized = False
        self.version_manager = NodeVersionManager()
        self.background_info = None

    async def async_initialize(self):
        await super().async_initialize()
        self.work_item_specific_setup = await self.setup_work_item()

    async def retrieve_info(self):
        await super().retrieve_info()
        
         # Get project node and details
        project_node = self.retrieved_info['root_node']
        self.project_id = project_node['id']

        existing_session = await get_or_create_session(self.current_user, self.project_id)
        self.doc_session_id = existing_session.get("session_id")

        # Get background info from Neo4j
        parent_node = await self.db.get_parent_node(self.node_id)
        # Structure current background info
        current_background_info = {
            'project_context': {
                'node_id': parent_node['id'] if parent_node else None,
                'properties': parent_node['properties'] if parent_node else {}
            }
        }

         # Store background info in class for use across discussions
        #self.background_info = current_background_info
        # self.update_logger.info(f"retrieve_info called on instance {self.instance_id}")
        # For reconfig, get stored context from MongoDB
        if self.is_reconfig():
            # self.model_name = LLMModel.gpt_4_1.value
            stored_background_info = await self.version_manager._get_context(self.node_id)
            
            if stored_background_info:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': stored_background_info.get('background_info', current_background_info),
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
                # Add safety check
                if not self.retrieved_info['background_info']:
                    self.retrieved_info['background_info'] = current_background_info
            else:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': current_background_info,
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
            
            self.retrieved_info['new_background'] = current_background_info
        else:
            # For initial configuration
            self.retrieved_info['background_info'] = current_background_info
            self.background_info = current_background_info  # Set class variable
            self.retrieved_info['new_background'] = current_background_info

        return self.retrieved_info


    async def setup_work_item(self):
        return "Setup specific to WorkItem completed"

    def get_modifications_from_llm_output(self):
        self.modifications['child_node_types'] = ['WorkItem']

    async def merge_captured_items(self):
        if not self.modifications.get('modified_node'):
            self.update_logger.warning("No modifications to merge")
            return
        await self.retrieve_info()
        user_inputs = self.modifications['modified_node'].get('user_inputs', '')
        change_reason = self.modifications['modified_node'].get('change_reason', '')
        
        try:
            if self.is_reconfig():
                
                if self.modifications['modified_node'].get('changes_needed', False):
                    result = await super().merge_captured_items()

                    # Handle new dependencies
                    new_relationships = self.modifications.get('new_relationships', [])
                    if new_relationships:
                        for relationship in new_relationships:
                            if relationship.get('type') == 'dependsOn':
                                await self.create_dependency_relationship(relationship)
                    else:
                        self.update_logger.info("No new relationships to process")
                    
                    # Save initial configuration
                    save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['new_background'],
                        'user_interaction': {
                            'timestamp': generate_timestamp(),
                            'action': 'reconfig',
                            'input': self.modifications['modified_node'].get('user_inputs', ''),
                            'reason': self.modifications['modified_node'].get('change_reason', '')
                        }
                    }

                    await self.version_manager.save_node_info(save_data)
                    return result
            else:
                result = await super().merge_captured_items()

                # Handle new dependencies
                new_relationships = self.modifications.get('new_relationships', [])
                if new_relationships:
                    for relationship in new_relationships:
                        if relationship.get('type') == 'dependsOn':
                            await self.create_dependency_relationship(relationship)
                else:
                    self.update_logger.info("No new relationships to process")
                
                # Save initial configuration
                save_data = {
                    'node_id': self.node_id,
                    'properties': self.modifications['modified_node'],
                    'background_info': self.retrieved_info['new_background'],
                    'user_interaction': {
                        'timestamp': generate_timestamp(),
                        'action': 'initial_config',
                        'input': self.modifications['modified_node'].get('user_inputs', ''),
                        'reason': self.modifications['modified_node'].get('change_reason', '')
                    }
                }

                await self.version_manager.save_node_info(save_data)
                return result
                
        except Exception as e:
            self.update_logger.error(f"Error saving changes to node {self.node_id}: {str(e)}")
            raise

    async def create_dependency_relationship(self, relationship):
        source_title = relationship.get('source')
        target_title = relationship.get('target')

        if not source_title or not target_title:
            self.update_logger.warning(f"Invalid dependency relationship: {relationship}")
            return

        source_node = await self.db.get_node_by_title('WorkItem', source_title)
        target_node = await self.db.get_node_by_title('WorkItem', target_title)

        if not source_node or not target_node:
            self.update_logger.warning(f"Could not find nodes for dependency relationship: {relationship}")
            return

        relationship_properties = {
            'Type': 'dependsOn',
            'Description': relationship.get('description', 'Dependency relationship')
        }

        await self.db.create_relationship(
            source_node['id'],
            target_node['id'],
            'DEPENDS_ON',
            relationship_properties
        )
        self.update_logger.info(f"Created DEPENDS_ON relationship from '{source_title}' to '{target_title}'")

    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        return config_state == 'configured'
# Registering the subclass in the factory
DiscussionFactory.register('configuration', WorkItemConfiguration, "WorkItem")