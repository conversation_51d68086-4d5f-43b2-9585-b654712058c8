from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.auth_utils import get_current_user
import os
from app.utils.prodefn.docs_tool import DocsTools
from app.utils.prodefn.docs_session_manager import get_or_create_session
import json 
import inspect
from app.utils.file_utils.upload_utils import upload_and_process,get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id

class ComponentConfiguration(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Component")
        self.template_name = "component_configuration.prompt"
        self.function_schema_type = "ComponentConfiguration"
        self.project_id = None
        self.doc_tools = None
        self.discussion_tools = None
        self.function_mapping = {}
        self.function_executor = None
        self.function_schemas = []
        self.schemas_initialized = False
        self.project_knowledge = False

        
    async def retrieve_info(self):
        await super().retrieve_info()
        
        # Retrieve container information
        container = await self.db.get_parent_node(self.node_id)
        self.retrieved_info['container'] = container
        if container:
            self.retrieved_info['container'] = {
                'id': container['id'],
                'properties': container['properties'],
                'repository_strategy': container['properties'].get('RepositoryStrategy'),
                'repository_path': container['properties'].get('RepositoryPath'),
                'container_path': container['properties'].get('ContainerPath')
            }
        # Retrieve other components in the container
        other_components = await self.db.get_child_nodes(container['id'], "Component")
        self.retrieved_info['other_components'] = [c for c in other_components if c['id'] != self.node_id]
        
        # Retrieve existing relationships
        uses_relationships = await self.db.get_relationships_involving_node(self.node_id, "USES")
        implements_relationships = await self.db.get_relationships_involving_node(self.node_id, "IMPLEMENTS")
        extends_relationships = await self.db.get_relationships_involving_node(self.node_id, "EXTENDS")
        
        self.retrieved_info['existing_relationships'] = {
            'USES': uses_relationships,
            'IMPLEMENTS': implements_relationships,
            'EXTENDS': extends_relationships
        }

        # Retrieve existing classes and interfaces
        existing_classes = await self.db.get_child_nodes(self.node_id, "Class")
        existing_interfaces = await self.db.get_child_nodes(self.node_id, "Interface")
        self.retrieved_info['existing_classes'] = existing_classes
        self.retrieved_info['existing_interfaces'] = existing_interfaces

         # Get project node and details
        project_node = self.retrieved_info['root_node']
        self.project_id = project_node['id']

        existing_session = await get_or_create_session(self.current_user, self.project_id)
        self.doc_session_id = existing_session.get("session_id")

        print(self.doc_session_id)

        return self.retrieved_info

    def get_modifications_from_llm_output(self):
        self.modifications['child_node_types'] = ['Class', 'Interface']
        return self.modifications

    async def merge_captured_items(self):
        await super().merge_captured_items()
        
        created_nodes = self.modifications.get('created_nodes', [])
        
        for node in created_nodes:
            node_type = node['labels'][0]  # Assuming the first label is the node type
            if node_type in ['Class', 'Interface']:
                await self.handle_node_relationships(node)

        relationships = self.modifications.get('new_relationships', [])
        if relationships:
            # Handle component relationships
            for relationship in relationships:
                self.update_logger.info(f"Processing relationship: {relationship}")

                if relationship.get('type') == 'USES':
                    target_title = relationship.get('target')
                    if target_title:
                        target_component = await self.db.get_node_by_property("Component", "Title", target_title)
                        if target_component:
                            await self.db.create_relationship(
                                self.node_id,
                                target_component['id'],
                                "USES",
                                {
                                    "description": relationship.get('description', ''),
                                    "technology": relationship.get('technology', ''),
                                    "target": target_title  # Explicitly include the target in the relationship properties
                                }
                            )
                        else:
                            self.update_logger.warning(f"Target component '{target_title}' not found")
                    else:
                        self.update_logger.warning(f"No target specified in relationship: {relationship}")

    async def handle_node_relationships(self, node):
        node_id = node['id']
        node_info = node['properties']

        if 'implements' in node_info:
            for interface_name in node_info['implements']:
                interface_node = await self.db.get_node_by_property('Interface', 'Title', interface_name)
                if interface_node:
                    await self.db.create_relationship(node_id, interface_node['id'], "IMPLEMENTS")

        if 'extends' in node_info:
            parent_node = await self.db.get_node_by_property(['Class', 'Interface'], 'Title', node_info['extends'])
            if parent_node:
                await self.db.create_relationship(node_id, parent_node['id'], "EXTENDS")

        if 'uses' in node_info:
            for used_name in node_info['uses']:
                used_node = await self.db.get_node_by_property(['Class', 'Interface'], 'Title', used_name)
                if used_node:
                    await self.db.create_relationship(node_id, used_node['id'], "USES")


DiscussionFactory.register('configuration', ComponentConfiguration, "Component")