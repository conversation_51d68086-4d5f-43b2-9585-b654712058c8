from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
import json
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.auth_utils import get_current_user
import os
from app.utils.prodefn.docs_tool import DocsTools
import inspect
from app.utils.prodefn.docs_session_manager import get_or_create_session
from app.utils.file_utils.upload_utils import upload_and_process,get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id
from app.utils.node_version_manager import NodeVersionManager
from app.utils.datetime_utils import generate_timestamp
class InterfaceDesignDetails(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Interface")
        self.template_name = "interface_design_details.prompt"
        self.function_schema_type = "InterfaceDesignDetails"
        self.project_id = None
        self.doc_tools = None
        self.discussion_tools = None
        self.function_mapping = {}
        self.function_executor = None
        self.function_schemas = []
        self.schemas_initialized = False
        self.project_knowledge = False
        self.version_manager = NodeVersionManager()
        self.background_info = None

        
    async def retrieve_info(self):
        await super().retrieve_info()

        # Get project node and architecture pattern
        project_node = self.retrieved_info['root_node']

        # Get incoming interfaces from the Interface node
        self.retrieved_info['incoming_interfaces'] = json.loads(
            self.retrieved_info['current_node']['properties'].get('incoming_interfaces', '[]')
        )

        # Get the parent architecture node
        parent_node = await self.db.get_parent_node(self.node_id)
        self.retrieved_info['parent_architecture_node'] = parent_node

        # Retrieve existing containers
        containers = await self.db.get_child_nodes(self.node_id, "Container")
        self.retrieved_info['containers'] = containers

        # Get parent component information
        parent_component = await self.db.get_parent_node(self.node_id)
        if parent_component:
            self.retrieved_info['parent_component'] = {
                'id': parent_component['id'],
                'title': parent_component['properties'].get('Title', ''),
                'description': parent_component['properties'].get('Description', ''),
                'technology': parent_component['properties'].get('Technology', '')
            }

            # Get design details
            design_nodes = await self.db.get_child_nodes(parent_component['id'], "Design")
            if design_nodes:
                self.retrieved_info['design_details'] = {
                    'id': design_nodes[0]['id'],
                    'behavior': design_nodes[0]['properties'].get('BehaviorDescription', ''),
                    'component_interactions': design_nodes[0]['properties'].get('ComponentInteractionsDescription', '')
                }

        # Get interface incoming details
        incoming_interfaces = json.loads(
            self.retrieved_info['current_node']['properties'].get('incoming_interfaces', '[]')
        )
        enriched_interfaces = []
        for interface in incoming_interfaces:
            source_id = interface.get('source_component_id')
            if source_id:
                source_component = await self.db.get_node_by_id(source_id)
                if source_component:
                    # Get design details for source component
                    source_design = await self.db.get_child_nodes(source_id, "Design")
                    interface['source_details'] = {
                        'component': source_component,
                        'design': source_design[0] if source_design else None
                    }
            enriched_interfaces.append(interface)
        
        self.retrieved_info['incoming_interfaces'] = enriched_interfaces

        architecture_root = await self.db.get_child_nodes(self.retrieved_info['root_node']['id'], "ArchitectureRoot")
        if architecture_root:
            arch_req_node = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
            if arch_req_node:
                # Get full requirement nodes
                functional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "FunctionalRequirement")
                nonfunctional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "NonFunctionalRequirement")
                
                architectural_reqs = {
                    'functional_requirements': functional_reqs,
                    'architectural_requirements': nonfunctional_reqs
                }

        # Structure current background info with complete node information
        current_background_info = {
            'project_context': {
                'node_id': project_node['id'] if project_node else None,
                'properties': project_node['properties'] if project_node else {}
            },
            'interface_context': {
                'component': self.retrieved_info.get('parent_component', {}),
                'design_details': self.retrieved_info.get('design_details', {}),
                'interfaces': self.retrieved_info.get('incoming_interfaces', [])
            },
            'requirements_context': {
                'functional_requirements': architectural_reqs['functional_requirements'],
                'architectural_requirements': architectural_reqs['architectural_requirements'],
                'parent_node': arch_req_node[0] if arch_req_node else None  # Store parent node for context
            }
        }

        # Handle reconfiguration
        if self.is_reconfig():
            stored_background_info = await self.version_manager._get_context(self.node_id)
            
            if stored_background_info:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': stored_background_info.get('background_info', current_background_info),
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
                # Add safety check
                if not self.retrieved_info['background_info']:
                    self.retrieved_info['background_info'] = current_background_info
            else:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': current_background_info,
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
            
            self.retrieved_info['new_background'] = current_background_info
        else:
            # For initial configuration
            self.retrieved_info['background_info'] = current_background_info
            self.background_info = current_background_info  # Set class variable
            self.retrieved_info['new_background'] = current_background_info

        return self.retrieved_info
    
    async def merge_captured_items(self):
        if not self.modifications.get('modified_node'):
            self.update_logger.warning("No modifications to merge")
            return
         # Get background info by calling retrieve_info again
        await self.retrieve_info()

        try:
            if self.is_reconfig():
                if self.modifications['modified_node'].get('changes_needed', False):
                    await super().merge_captured_items()
                    # Save version info
                    save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['new_background'],
                        'user_interaction': {
                            'timestamp': generate_timestamp(),
                            'action': 'reconfig',
                            'input': self.modifications['modified_node'].get('user_inputs', ''),
                            'reason': self.modifications['modified_node'].get('change_reason', '')
                        }
                    }
                    await self.version_manager.save_node_info(save_data)

            else:
                await super().merge_captured_items()
                # Save version info
                save_data = {
                    'node_id': self.node_id,
                    'properties': self.modifications['modified_node'],
                    'background_info': self.retrieved_info['new_background'],
                    'user_interaction': {
                        'timestamp': generate_timestamp(),
                        'action': 'reconfig',
                        'input': self.modifications['modified_node'].get('user_inputs', ''),
                        'reason': self.modifications['modified_node'].get('change_reason', '')
                    }
                }
                await self.version_manager.save_node_info(save_data)

        except Exception as e:
            self.update_logger.error(f"Error saving changes to node {self.node_id}: {str(e)}")
            raise

    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        return config_state == 'configured'
# Registering the subclass in the factory
DiscussionFactory.register('design_details', InterfaceDesignDetails, "Interface")