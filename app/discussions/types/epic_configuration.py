from app.core.function_schema_generator import get_function_schema
from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import json
from app.discussions.tools.discussion_tool import DiscussionTools
from app.models.user_model import LLMModel
from app.utils.auth_utils import get_current_user
import os
from app.utils.prodefn.docs_tool import DocsTools
import inspect
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.auth_utils import get_current_user
import os
from app.utils.prodefn.docs_tool import DocsTools
from app.utils.prodefn.docs_session_manager import get_or_create_session
from app.utils.file_utils.upload_utils import upload_and_process,get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id
from datetime import datetime
from app.utils.node_version_manager import NodeVersionManager
from app.utils.datetime_utils import generate_timestamp
class EpicConfiguration(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Epic")
        self.template_name = "epic_configuration.prompt"
        self.function_schema_type = "EpicConfiguration"
        self.project_id = None
        self.doc_tools = None
        self.discussion_tools = None
        self.function_mapping = {}
        self.function_executor = None
        self.function_schemas = []
        self.schemas_initialized = False
        self.project_knowledge = False
        self.version_manager = NodeVersionManager()
        self.background_info = None

    async def async_initialize(self):
        # Initialization specific to WorkItem configuration
        await super().async_initialize()
        # Additional async setup specific to this class can be added here

    async def retrieve_info(self):
        await super().retrieve_info()
        
         # Get project node and details
        project_node = self.retrieved_info['root_node']
        self.project_id = project_node['id']

        existing_session = await get_or_create_session(self.current_user, self.project_id)
        self.doc_session_id = existing_session.get("session_id")

        # Get configuration state
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        self.retrieved_info['config_state'] = config_state

        # Get background info from Neo4j
        parent_node = await self.db.get_parent_node(self.node_id)
        
        # Get epics and their user stories
        epics = await self.db.get_child_nodes(self.node_id, "Epic")
        epic_details = []
        for epic in epics:
            user_stories = await self.db.get_child_nodes(epic['id'], "UserStory")
            epic_details.append({
                'epic': epic,
                'user_stories': user_stories
            })
        
        # Structure current background info
        current_background_info = {
            'project_context': {
                'node_id': parent_node['id'] if parent_node else None,
                'properties': parent_node['properties'] if parent_node else {}
            },
            'requirement_context': epic_details
        }

         # Store background info in class for use across discussions
        #self.background_info = current_background_info
        # self.update_logger.info(f"retrieve_info called on instance {self.instance_id}")
        # For reconfig, get stored context from MongoDB
        if self.is_reconfig():
            # self.model_name = LLMModel.gpt_4_1.value
            # Get MongoDB instance from version_manager
            stored_background_info = await self.version_manager._get_context(self.node_id)
            
            if stored_background_info:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': stored_background_info.get('background_info', current_background_info),
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    }
                })
                
                # Store metadata separately
                self.retrieved_info['change_log'] = self.node['properties'].get('change_log', [])
                self.retrieved_info['change_reason'] = self.node['properties'].get('change_reason', '')
            else:
                # If no stored background info, use current background info
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': current_background_info,
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
            
            self.retrieved_info['new_background'] = current_background_info #if something changed in project info 
        else:
            # For initial configuration
            self.retrieved_info['background_info'] = current_background_info
            
        self.update_logger.info(f"Retrieved info for node {self.node_id}: Background info and user interaction loaded")
        return self.retrieved_info
    
    async def merge_captured_items(self):
        # self.update_logger.info(f"merge_captured_items called on instance {self.instance_id}")
        if not self.modifications.get('modified_node'):
            self.update_logger.warning("No modifications to merge")
            return
         # Get background info by calling retrieve_info again
        await self.retrieve_info()
        user_inputs = self.modifications['modified_node'].get('user_inputs', '')
        change_reason = self.modifications['modified_node'].get('change_reason', '')
        try:
            if self.is_reconfig():
                if self.modifications['modified_node'].get('changes_needed', False):
                    await super().merge_captured_items()
                    # Get user inputs and change reason from LLM output
                   
                    
                    save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['background_info'],
                        'user_interaction': {
                            'timestamp': generate_timestamp(),
                            'action': 'reconfig',
                            'input': user_inputs,  # Use LLM output's user_inputs
                            'reason': change_reason  # Use LLM output's change_reason
                        }
                    }
                    await self.version_manager.save_node_info(save_data)
            else:
               
                await super().merge_captured_items()
                 # First create the node using super().merge_captured_items()
                save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['background_info'],
                        'user_interaction': {
                            'timestamp': generate_timestamp(),
                            'action': 'initial_config',
                            'input': user_inputs,  # Use LLM output's user_inputs
                            'reason': change_reason  # Use LLM output's change_reason
                        }
                    }
                await self.version_manager.save_node_info(save_data)
                
            self.update_logger.info(f"Successfully saved changes to node {self.node_id}")
                
        except Exception as e:
            self.update_logger.error(f"Error saving changes to node {self.node_id}: {str(e)}")
            raise

        
    def get_modifications_from_llm_output(self):
        # super().get_modifications_from_llm_output(node_type, root_node_type, discussion_type, llm_output)

        self.modifications['modified_node']['Type'] = 'Epic'
        # self.modifications['new_child_nodes'] = llm_output.get("UserStories")
        self.modifications['child_node_types'] = ['UserStory', 'Requirement']

        return self.modifications

    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        return config_state == 'configured'

# Registering the subclass in the factory
DiscussionFactory.register('configuration', EpicConfiguration, "Epic")