{% extends "base_discussion.prompt" %}

{% block task_description %}
To provide your response, please use the capture_discussion_output function, Now, for your specific task:

Your task is to  design component interactions for a software component. You should create sequence diagrams that cover interactions between this component and 
other components. Sequence diagrams should also cover interaction between this component and the external world. If there are internal states that
are affected by interactions with external components, you should also show those in state diagrams.


Use Mermaid syntax for all diagrams. Here are examples of the expected format:
State Diagram:
<Mermaid-State>
stateDiagram-v2
[*] --> Idle
Idle --> Processing: Receive Request
Processing --> Idle: Complete Request
Processing --> Error: Error Occurs
Error --> Idle: Reset
</Mermaid-State>
Sequence Diagram:
<Mermaid-Sequence>


sequenceDiagram
participant User
participant ThisComponent
participant ExternalAPI
participant Database
CopyUser->>ThisComponent: InitiateAction
ThisComponent->>ExternalAPI: RequestData
ExternalAPI-->>ThisComponent: ReturnData
ThisComponent->>Database: StoreResult
Database-->>ThisComponent: ConfirmStorage
ThisComponent-->>User: DisplayResult
</Mermaid-Sequence>
Ensure your diagrams are comprehensive, covering all major interactions and state changes.
{% endblock %}

{% block background_information %}

The component you are designing is part of a larger system. Here's the relevant information:

Current Component Node:
{{ details_for_discussion.get('current_node') | tojson(indent=2) }}
Root Product Node:
{{ details_for_discussion.get('root_node') | tojson(indent=2) }}
Component Hierarchy:
{{ details_for_discussion.get('parent_tree') | tojson(indent=2) }}
Defined Interfaces for the Current Component:
{{ details_for_discussion.get('direct_interfaces') | tojson(indent=2) }}

Use this information to inform your design decisions and ensure consistency with the overall system architecture.
{% endblock %}

{% block information_about_task %}
Information is stored in the system as nodes of a graph. Here is the node that stores the information for the current node: {{ details_for_discussion.get('current_node') | tojson(indent=2) }}
The current node is a part of a product with the following information: {{ details_for_discussion.get('root_node') | tojson(indent=2) }}
The current node is a part of the following tree of nodes of information: {{ details_for_discussion.get('parent_tree') | tojson(indent=2) }}
Here are all the interfaces that are defined for the current component: {{ details_for_discussion.get('direct_interfaces') | tojson(indent=2) }}
{% endblock %}


{% block output_format %}
Use the function capture_discussion_output to save the output.
{% endblock %}

{%block system_prompt %}
You are an expert software designer who has a deep understanding of software component design. You have several years of software development experience. 
You are engaging in a conversation with a software architect regarding the job of creating the detailed software design 
for a component. Output any comments to  the user in regular text format, 
Enclose any JSON formatted context inside <JSON> and </JSON> tags. 
{% endblock %}