{% extends "base_discussion.prompt" %}

{% block task_description %}
To provide your response, please use the capture_discussion_output function . Now, for your specific task:

Your task is to create design specification for the current component based on the information provided here, and your own knowledge.
The output should be the following elements of the detailed design for the current component, which could later be used by an AI agent to generate the source-code for the component:
    - Purpose and responsibilities of the component
    - Inputs and outputs
    - Functional requirements
    - Non-functional requirements (e.g., performance, scalability, security)
    - Dependencies on other components.
{% endblock %}

{% block information_about_task %}
Information is stored in the system as nodes of a graph. Here is the node that stores the information for the current node: {{ details_for_discussion.get('current_node') | tojson(indent=2) }}
The current node is a part of a product with the following information: {{ details_for_discussion.get('root_node') | tojson(indent=2) }}
The current node is a part of the following tree of nodes of information: {{ details_for_discussion.get('parent_tree') | tojson(indent=2) }}
Here are all the interfaces that are defined for the current component: {{ details_for_discussion.get('direct_interfaces') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
Here are some functional requirements for the whole system to take into account while you are designing this module: 
{{ details_for_discussion.get('functional_requirements')  }}
Here are some architectural requirements for the whole system to take into account while your are designing this module: 
{{ details_for_discussion.get('architectural_requirements') }}

Here are the high-level architecture decomposition of the whole system (which includes this module as well):
Top levels of architecture: {{ details_for_discussion.get('top_levels') | tojson(indent=2) }}
Relationship between other architectural components: {{ details_for_discussion.get('relationships') | tojson(indent=2) }}
{% endblock %}


{% block output_format %}
Use the function capture_discussion_output to save the output.
{% endblock %}

{%block system_prompt %}
You are an expert software designer who has a deep understanding of software component design. You have several years of software development experience. 
You are engaging in a conversation with a software architect regarding the job of creating the detailed software design 
for a component. Output any comments to  the user in regular text format, 
Enclose any JSON formatted context inside <JSON> and </JSON> tags. 
{% endblock %}