{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}
Create design behaviors for the software component. These behaviors should describe:
1. Functionality implementation using pseudo-code
2. Algorithmic logic for key operations
3. Input handling processes
4. Output generation methods
5. Internal processing mechanisms
6. State management logic for correct component functioning

Consider the component's role within the larger system and its defined interfaces when designing these behaviors.
{% endblock %}

{% block task_description_post_script %}
Ensure the design behaviors are comprehensive, clear, and align with best practices in software design and the component's specific requirements.
{% endblock %}

{% block autoconfig %}
Analyze the component's information and interfaces to autonomously generate appropriate design behaviors.
{% endblock %}

{% block auto_reconfig %}
Review existing design behaviors and update them based on changes in component functionality or system requirements.
{% endblock %}

{% block interactive_config %}
Guide the user through the process of creating design behaviors, focusing on critical functionalities and key algorithmic aspects of the component.
{% endblock %}

{% block interactive_reconfig %}
Collaborate with the user to review and enhance existing design behaviors, addressing any gaps or new requirements.
{% endblock %}

{% block information_about_task %}
Current node: {{ details_for_discussion.get('current_node') | tojson(indent=2) }}
Root node: {{ details_for_discussion.get('root_node') | tojson(indent=2) }}
Parent tree: {{ details_for_discussion.get('parent_tree') | tojson(indent=2) }}
Component interfaces: {{ details_for_discussion.get('direct_interfaces') | tojson(indent=2) }}
{% endblock %}