{% extends "base_discussion.prompt" %}

{% block autoconfig %}
{% block task_description_autoconfig %}
To provide your response, please use the capture_discussion_output, Now, for your specific task:

Your task is to add more details to the requirement text to cover all requirements 
to meet the product scope and to create a comprehensive list of Epics from that modified requirements for a product. 
The list of Epics should cover all requirements for the product.
 You will need to use the requirement text, the product title, product description, 
 product scope and your knowledge base to come up with the Epics that are relevant to the product.

When creating the epics consider below points.

Input Analysis
Carefully analyze the provided input:

If a clear product requirement or project idea is given, use it as the primary basis for Epic creation.
If the input is vague or incomplete, apply your expertise to infer and expand upon the likely requirements of a typical software project.
Consider the following aspects of the project:

Major features and functionalities
Architecture
Functional requirements
Non-functional requirements (e.g., performance, scalability, security)
Technical architecture
User experience and interface design
Development process and tooling
Quality assurance and testing
Deployment and operations
Compliance and regulatory requirements
Any other relevant aspects of software development life cycle

Other than the above, there can be below types of epics for different type of projects. You should chose the most suitable epics according to the project or product type.

-Epic Creation Guidelines-
Generate a set of Epics that comprehensively cover the project's needs. Each Epic should:

Be broad enough to encompass a significant portion of work, yet specific enough to be manageable.
Have a clear and concise title that reflects its purpose.
Include a brief description of the need it addresses.
List the key expectations or outcomes it needs to satisfy.

-Minimum Epic Set-
Ensure that your Epic set includes, at minimum, the following categories:

-Project Initialization-
Core Functionality Development
Infrastructure and Environment Setup
Testing and Quality Assurance
Security and Compliance
Deployment and Release
Expand on these categories as needed based on the specific project requirements.

-Non-Functional Requirements-
Include Epics that address non-functional requirements crucial for an end-to-end software project, such as:

Performance Optimization
Scalability and Load Handling
Security Implementation
Accessibility Compliance
Internationalization and Localization
Data Privacy and Protection
Monitoring and Logging
Disaster Recovery and Business Continuity

-Output Format-
Capture the output using the capture_discussion_output to save the data.

Additional Instructions
If the input is unclear or incomplete, state your assumptions and provide a rationale for the Epics you've created.
Ensure that the set of Epics covers both the explicit requirements and implicit needs of a robust software project.
Consider interdependencies between Epics and note any significant relationships or sequencing requirements.
If specific technologies or methodologies are mentioned in the input, tailor the Epics accordingly.
For highly specialized or domain-specific projects, include Epics that address unique industry requirements or standards.
Remember, your goal is to provide a comprehensive foundation for project planning that covers all crucial aspects of software development, even when given minimal input.

{% endblock %}
{% endblock %}

{% block reconfig %}
{% block task_description_reconfig %}
In the beginning of the response output - "RECONFIG DISCUSSION"
Your task is to assist in reconfiguring the Requirement Root and its associated Epics.
You will guide the user through reviewing the existing details, making updates as needed, and confirming the changes before saving them.
1. Collect the data from database and

{% if details_for_discussion.get('latest_modifications') %}
Please base your response and output primarily on these latest modifications:
{{ details_for_discussion.latest_modifications | tojson(indent=2) }}
Use this information as the most up-to-date context for the discussion. Any previous data should be considered outdated if it conflicts with these modifications.
{% else %}
Consider the following information for the initial discussion as input data: current node and child nodes
{{ details_for_discussion.get('current_node') }}
{{ details_for_discussion.get('child_nodes') }}
{% endif %}

... (rest of the reconfig block content)

{% endblock %}
{% endblock %}