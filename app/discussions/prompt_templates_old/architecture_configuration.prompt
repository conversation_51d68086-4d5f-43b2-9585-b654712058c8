{% extends "base_discussion.prompt" %}

{% block task_description %}
As an expert software architect, review the current architectural node and configure it according to the Architecture node description in the function schema. Decide on splitting into sub-components or marking as a leaf node, and create new components as necessary.
{% endblock %}

{% block information_about_task %}
Information about the task:
Current node: {{ details_for_discussion.get('current_node') | tojson(indent=2) }}
Root node: {{ details_for_discussion.get('root_node') | tojson(indent=2) }}
Parent tree: {{ details_for_discussion.get('parent_tree') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
Functional requirements: {{ details_for_discussion.get('functional_requirements') }}
Architectural requirements: {{ details_for_discussion.get('architectural_requirements') }}
Top levels of architecture: {{ details_for_discussion.get('top_levels') | tojson(indent=2) }}
Relationships between components: {{ details_for_discussion.get('relationships') | toj<PERSON>(indent=2) }}
{% endblock %}

{% block output_format %}
Use the capture_discussion_output function to save the details. Follow the ID naming conventions for new and existing architectural elements as specified in the Architecture node description.
{% endblock %}

{% block system_prompt %}
As an expert software architect, engage in architectural design for a new feature or product. Ensure your analysis is thorough and your design is optimized for quality attributes like performance, scalability, and maintainability. Address all key functional and non-functional requirements in your architectural design.
{% endblock %}