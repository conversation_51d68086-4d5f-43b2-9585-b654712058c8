{% extends "base_discussion.prompt" %}

{% block task_description %}
To provide your response, please use the capture_discussion_output function. Now, for your specific task:

Your task is to create a comprehensive API document for the current component of an AI-based software application development 
workflow manager. This document will serve as a blueprint for AI agents to generate the source code for the component. 
For each API endpoint, provide the following details:

<API-Doc>
Endpoint: [URL path]
Method: [HTTP method]
Description: [A concise description of the endpoint's purpose within the AI-based software application development workflow manager]
Use cases: [List of specific use cases for this endpoint in the context of workflow management]

Authentication: [Authentication requirements, if any]
Error responses: [Provide Possible Sample error codes and their meanings, in JSON format with key value]
</API-Doc>

Consider the following when designing the API:
1. RESTful principles and best practices
2. Consistency in naming conventions and response structures
3. Proper use of HTTP methods and status codes
4. Clear and concise descriptions for each endpoint
5. Comprehensive error handling and reporting
6. Scalability and future extensibility of the API
7. Security considerations, including authentication and authorization

Repeat this structure for each API endpoint related to the current component. Ensure that the API design follows RESTful principles and best practices for API development.
Include all the information for APIs in a single API document
{% endblock %}

{% block information_about_task %}
Information is stored in the system as nodes of a graph. Here is the node that stores the information for the current node: {{ details_for_discussion.get('current_node') | tojson(indent=2) }}
The current node is a part of a product with the following information: {{ details_for_discussion.get('root_node') | tojson(indent=2) }}
The current node is a part of the following tree of nodes of information: {{ details_for_discussion.get('parent_tree') | tojson(indent=2) }}
Here are all the interfaces that are defined for the current component: {{ details_for_discussion.get('direct_interfaces') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
{% endblock %}

{% block output_format %}
Please use the following JSON format for your JSON output:
<JSON>
{
  "API-Doc": [
    {
      "Endpoint": "/orders",
      "Method": "POST",
      "Description": "Place a new order in the Order Management Module.",
      "Use cases": [
        "Allow the Business Logic Layer (BLL) to place new orders.",
        "Enable users to execute trades in the stock market."
      ],
      "Request format": {
        "order": {
          "symbol": "string",
          "quantity": "integer",
          "price": "number",
          "order_type": "string"
        }
      },
      "Response format": {
        "order_id": "string",
        "status": "string",
        "timestamp": "string"
      },
      "Authentication": "OAuth token required.",
      "Error responses": {
        "400": "Bad Request - Invalid request format or parameters.",
        "401": "Unauthorized - Authentication credentials are missing or invalid.",
        "500": "Internal Server Error - An unexpected error occurred on the server."
      }
    },
    {
      "Endpoint": "/orders/{orderId}",
      "Method": "DELETE",
      "Description": "Cancel an existing order in the Order Management Module.",
      "Use cases": [
        "Allow the Business Logic Layer (BLL) to cancel existing orders.",
        "Enable users to cancel trades that have not yet been executed."
      ],
      "Request format": {},
      "Response format": {
        "order_id": "string",
        "status": "string",
        "timestamp": "string"
      },
      "Authentication": "OAuth token required.",
      "Error responses": {
        "400": "Bad Request - Invalid request format or parameters.",
        "401": "Unauthorized - Authentication credentials are missing or invalid.",
        "404": "Not Found - The specified order does not exist.",
        "500": "Internal Server Error - An unexpected error occurred on the server."
      }
    },
    {
      "Endpoint": "/orders/bulk-profit-booking",
      "Method": "POST",
      "Description": "Execute bulk profit booking orders in the Order Management Module.",
      "Use cases": [
        "Support the execution of bulk profit booking orders.",
        "Enable users to book profits on multiple trades simultaneously."
      ],
      "Request format": {
        "orders": [
          {
            "symbol": "string",
            "quantity": "integer",
            "price": "number",
            "order_type": "string"
          }
        ]
      },
      "Response format": {
        "orders": [
          {
            "order_id": "string",
            "status": "string",
            "timestamp": "string"
          }
        ]
      },
      "Authentication": "OAuth token required.",
      "Error responses": {
        "400": "Bad Request - Invalid request format or parameters.",
        "401": "Unauthorized - Authentication credentials are missing or invalid.",
        "500": "Internal Server Error - An unexpected error occurred on the server."
      }
    },
    {
      "Endpoint": "/orders/stop-loss",
      "Method": "POST",
      "Description": "Execute stop loss orders in the Order Management Module.",
      "Use cases": [
        "Support the quick execution of stop loss orders.",
        "Enable users to minimize losses in response to sudden market changes."
      ],
      "Request format": {
        "orders": [
          {
            "symbol": "string",
            "quantity": "integer",
            "price": "number",
            "order_type": "string"
          }
        ]
      },
      "Response format": {
        "orders": [
          {
            "order_id": "string",
            "status": "string",
            "timestamp": "string"
          }
        ]
      },
      "Authentication": "OAuth token required.",
      "Error responses": {
        "400": "Bad Request - Invalid request format or parameters.",
        "401": "Unauthorized - Authentication credentials are missing or invalid.",
        "500": "Internal Server Error - An unexpected error occurred on the server."
      }
    },
    {
      "Endpoint": "/orders/{orderId}/status",
      "Method": "GET",
      "Description": "Get the status of an order in the Order Management Module.",
      "Use cases": [
        "Provide real-time updates on the status of orders.",
        "Enable users to track the status of their trades."
      ],
      "Request format": {
        "orders": [
          {
            "symbol": "string",
            "quantity": "integer",
            "price": "number",
            "order_type": "string"
          }
        ]
      },
      "Response format": {
        "order_id": "string",
        "status": "string",
        "timestamp": "string"
      },
      "Authentication": "OAuth token required.",
      "Error responses": {
        "400": "Bad Request - Invalid request format or parameters.",
        "401": "Unauthorized - Authentication credentials are missing or invalid.",
        "404": "Not Found - The specified order does not exist.",
        "500": "Internal Server Error - An unexpected error occurred on the server."
      }
    }
  ]
}
</JSON>
{% endblock %}

{% block system_prompt %}
You are an expert software designer who has a deep understanding of software component design. You have several years of software development experience. 
You are engaging in a conversation with a software architect regarding the job of creating the detailed software design 
for a component. Output any comments to the user in regular text format, 
Enclose any JSON formatted context inside <JSON> and </JSON> tags. 
{% endblock %}

{% block additional_information %}
**Fix for Neo4j Property Storage**

To resolve the issue with the `apoc.create.node` procedure in Neo4j where a `HashMap` is being passed as a property value, you need to convert complex objects into JSON strings. Here's an example of how to adjust your Cypher query:

```cypher
WITH {
    Description: "Fetch real-time intraday trading recommendations.",
    Endpoint: "/recommendations",
    Response_format: apoc.convert.toJson({recommendations: [{symbol: "string", exit_criteria: "string", entry_criteria: "string", timestamp: "string"}]}),
    Authentication: "OAuth token required",
    Method: "GET",
    Use_cases: apoc.convert.toJson(["Retrieve current trading recommendations for intraday trading.", "Display trading recommendations on the user dashboard."]),
    Request_format: apoc.convert.toJson({})
} AS properties
CALL apoc.create.node(["Label"], properties) YIELD node
RETURN node;
{% endblock %}