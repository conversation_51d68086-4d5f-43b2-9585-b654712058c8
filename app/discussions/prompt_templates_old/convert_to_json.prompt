{# Check if the prompt type is "user" #}
{% if prompt_type == "user" %}
{% block user_prompt %}
{% block task_description %}
{% if root_node_type == 'Project' %}
Your job is to review the output of a discussion between a Product Manager and an AI agent about an Epic or UserStory, or a Requirement and to convert that to JSON format.
{% elif root_node_type == 'Project' %}
Your job is to review the output of a discussion between a Program Manager and an AI agent about Work Items for a project and converting that to JSON format.
{% else %}
{{ task_description }}
{% endif %}
{% endblock %}

{% block information_about_task %}
Please convert the following text (enclosed in <Text> and </Text> tags) that contains plain text and JSON into a JSON format:
<Text>{{ last_response_from_llm }}</Text>
{% endblock %}

{% block background_information %}
Here are some additional Background information about the current node:
Here is the list of sibling nodes: {{ details_for_discussion.get('sibling_nodes') | tojson }}
Here is the list of child nodes: {{ details_for_discussion.get('child_nodes') | tojson }}
Here is the list of other relevant nodes found through a vector similarity search: {{ details_for_discussion.get('other_relevant_nodes') | tojson }}

The background information above should be used primarily to make sure new nodes created are not duplicates of existing nodes or overlaps in functionality with existing nodes.
If you find any overlap in functionality you should do one of the following:
- Modify the existing node description to cover the overlapping functionality
- Modify the new child node to remove overlapping functionalities and make it distinct
- Do not create the new child node if there is significant overlap in functionality with existing nodes

The above set of instructions about what to do with the background tasks should be ignored if the discussion was about identifying dependencies, 
and in that case, the background_information should be used for idenifying dependencies for the current task.

If any other nodes provided in the background information needs to be updated, please update them with the new information.
{% endblock %}

{% block output_format %}
Please use the following JSON format for for your output:
{{output_format}}
JSON fields should contain just the content for that field and should not contain any other discussion or comments.
{% endblock %}
{% endblock %}
{% endif %}

{# Check if the prompt type is "system" #}
{% if prompt_type == "system" %}
{% block system_prompt %}
You are an agent that extracts correct information from text and formats the output in JSON format. You should not add any new information to the output, 
if that information is not already present in the input, especially within the <JSON> tag.
You are an expert in converting text into a JSON format that can be easily consumed by a requirement processing software.
There should not be any extra characters in the output outside of the JSON format.
{% endblock %}
{% endif %}