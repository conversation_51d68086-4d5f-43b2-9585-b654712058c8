{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}

{% if config_state != "configured" %}

Review the purpose and functionalities of the component, identify the key elements of the component in relation to overall architecture. Use your expertise and design interactions between these elements.
- Must create suitable sequence diagrams to show the interaction between the elements - create as child nodes.
- Must create appropriate state transition diagrams applicable for the component - create as child nodes
- Please generate Mermaid code without any syntax error

{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}

{% else %}
You are an expert system architect reviewing the design behaviour for potential reconfiguration.

Current Configuration Context:

1. Existing State:
   - Current configuration: {{ details_for_discussion.get('original_node') | tojson(indent=2) }}
   - Configuration state: {{ details_for_discussion.get('config_state') }}

2. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - System context: 
     - state_diagrams: {{ bg_info.get('design_context', {}).get('state_diagrams') | tojson(indent=2) }}
     - sequence_diagrams: {{ bg_info.get('design_context', {}).get('sequence_diagrams') | tojson(indent=2) }}
     - parent_component_node: {{ bg_info.get('system_context', {}).get('parent_component_node') | tojson(indent=2) }}
   

3. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated system context:
     - state_diagrams: {{ new_bg.get('design_context', {}).get('state_diagrams') | tojson(indent=2) }}
     - sequence_diagrams: {{ bg_info.get('design_context', {}).get('sequence_diagrams') | tojson(indent=2) }}
     - parent_component_node: {{ new_bg.get('system_context', {}).get('parent_component_node') | tojson(indent=2) }}
  

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

6. Current Component Configuration:
   Existing components: {{ details_for_discussion.get('parent_component') | tojson(indent=2) }}
   state_diagrams: {{ new_bg.get('design_context', {}).get('state_diagrams') | tojson(indent=2) }}
   sequence_diagrams: {{ bg_info.get('design_context', {}).get('sequence_diagrams') | tojson(indent=2) }}

Analysis Instructions:


1. Component Architecture Analysis:
   - Review the parent component against new requirements
   - Evaluate component responsibilities and boundaries
   - Assess need for new algorithm components or modifications

2. Required Changes:
   Modify Existing state diagrams and sequence diagrams, or add any required state diagrams and sequence diagrams following the below guidelines 

{% endif %}

Here are examples of the expected format, use the below for reference purpose only, create required diagrams based on the given component details.
State Diagram:
```
stateDiagram-v2
    [*] --> Idle: System Start
    Idle --> Validating: Order Received
    Validating --> Processing: Order Valid
    Validating --> Rejected: Invalid Order
    Processing --> Shipping: Payment Successful
    Processing --> Cancelled: Payment Failed
    Shipping --> Delivered: Order Shipped
    Delivered --> [*]: Order Complete
    Rejected --> Idle: Reset
    Cancelled --> Idle: Reset
```

Sequence Diagram:
```
sequenceDiagram
    actor User
    participant Client
    participant AuthService
    participant Database

    User->>Client: Enter Credentials
    Client->>AuthService: Submit Login Request
    AuthService->>Database: Verify Credentials
    alt Credentials Valid
        Database-->>AuthService: Credentials Confirmed
        AuthService-->>Client: Generate Auth Token
        Client-->>User: Login Successful
    else Credentials Invalid
        Database-->>AuthService: Invalid Credentials
        AuthService-->>Client: Authentication Failed
        Client-->>User: Show Error Message
    end
```
Change Needed: 
    - Set to True if changes are required.

Change Log :
    - capture history of changes.

{% endblock %}

{% block autoconfig %}
Create detailed sequence and state diagrams as new child nodes covering all necessary interactions for the component.
{% endblock %}

{% block auto_reconfig %}
Create or update the funtional and / or non-functional requirements following the above guidelines.
{% endblock %}


{% block node_details_interactive_reconfig %}
interaction diagrams
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
Analyze their feedback and current system state to derive optimal solutions that align with UML best practices and the evolving needs of workflow management
{% endblock %}

{% block information_about_task %}
Here is the architectural node for the component for which the detail design node is being configured:
{{details_for_discussion.get('architecture_node') | tojson(indent=2)}}
{{ details_for_discussion.get('existing_sequence_diagrams') | tojson(indent=2) }}
{{ details_for_discussion.get('existing_state_diagrams') | tojson(indent=2) }}
Current component interfaces:
{{ details_for_discussion.get('interface_details') | tojson(indent=2) }}
Use this information to ensure your interaction diagrams accurately represent the component's interactions within the system.
{% endblock %}
{% block background_information %}
None
{% endblock %}