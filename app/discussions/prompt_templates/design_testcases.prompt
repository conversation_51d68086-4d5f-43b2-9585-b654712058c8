{% extends "base_discussion.prompt" %}

You are an expert software quality assurance engineer.

{% block task_description_common_preface %}
{% if config_state != "configured" %}
Your task is to create a comprehensive test suite for the component. 
Retain the original title and description of the Design node - same as component title and description.
1. The test suite MUST include the following types of test cases, each test case shall be created as child of the Design node:
   a. Unit Tests
   b. Integration Tests
   c. Performance Tests
   d. Robustness Tests

2. List at least 3 specific test cases for each type, each with:
   a. A clear title   
   b. A detailed description of the test scenario
   c. Expected outputs or behavior\n\nEnsure that the test cases cover different aspects of the component's functionality and interfaces.
   d. child nodes needs to be in json format

{% else %}
You are an expert system architect reviewing the design test cases for potential reconfiguration.

Current Configuration Context:

1. Existing State:
   - Current configuration: {{ details_for_discussion.get('original_node') | tojson(indent=2) }}
   - Configuration state: {{ details_for_discussion.get('config_state') }}

2. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - System context: 
     - test_cases: {{ bg_info.get('design_context', {}).get('test_cases') | tojson(indent=2) }}
     - parent_component_node: {{ bg_info.get('system_context', {}).get('parent_component_node') | tojson(indent=2) }}
   

3. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated system context:
     - test_cases: {{ new_bg.get('design_context', {}).get('test_cases') | tojson(indent=2) }}
     - parent_component_node: {{ new_bg.get('system_context', {}).get('parent_component_node') | tojson(indent=2) }}
  

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

6. Current Component Configuration:
   Existing Containers: {{ details_for_discussion.get('parent_component') | tojson(indent=2) }}
   test_cases: {{ new_bg.get('design_context', {}).get('test_cases') | tojson(indent=2) }}

Analysis Instructions:


1. Component Architecture Analysis:
   - Review the parent component against new requirements
   - Evaluate component responsibilities and boundaries
   - Assess need for new algorithm components or modifications

2. Required Changes:
   Modify Existing test cases, or add any required test cases following the below guidelines 

{% endif %}


Change Needed: 
    - Set to True if changes are required.

Change Log :
    - capture history of changes.
    
{% endblock %}

{% block autoconfig %}
Create a comprehensive test suite for the component.
{% endblock %}

{% block auto_reconfig %}
Based on the current test suite context and component requirements, analyze if the test cases need updates to:

Better cover component functionality
Improve test coverage
Enhance integration testing
Update performance tests
Strengthen robustness testing

If updates are needed:

Propose specific modifications to test cases
Add new test scenarios if required
Update expected results
Document rationale in change_reason
Update change_log with modification history

If no updates needed:

Explain why current test suite remains valid
Set changes_needed to false
Document reasoning in change_reason
{% endblock %}


{% block node_details_interactive_reconfig %}
test suite (including all required test types and specific test cases)
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
Review and enhance the existing test cases based on the following information:

Child nodes: {{details_for_discussion.child_nodes | tojson(indent=2)}}

Interact with the user to update the test suite as per their needs. For each test type (Unit, Integration, Performance, and Robustness):

{% endblock %}

{% block information_about_task %}
Here is the architectural node for the component for which the detail design node is being configured:
{{details_for_discussion.get('architecture_node') | tojson(indent=2)}}

Component details:
Title: {{ details_for_discussion.current_node.Title }}
Description: {{ details_for_discussion.current_node.Description }}
Component interfaces:
{{ details_for_discussion.get('interface_details') | tojson(indent=2) }}
Existing test cases:
{{details_for_discussion.get('existing_unittest_cases') | tojson(indent=2)}}
{{details_for_discussion.get('existing_integrationtest_cases') | tojson(indent=2)}}
{{details_for_discussion.get('existing_performancetest_cases') | tojson(indent=2)}}
{{details_for_discussion.get('existing_robustnesstest_cases') | tojson(indent=2)}}
Use this information to ensure your test cases accurately cover the component's functionality and interactions.
{% endblock %}

{% block background_information %}
None
{% endblock %}