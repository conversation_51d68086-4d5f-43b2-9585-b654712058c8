{% extends "base_discussion.prompt" %}

You are an expert software architect.

{% block autoconfig %}
Your task is to start by determining the project title and basic description from the codebase:

1. First find key project identifiers using KnowledgeTools:
   - Look for identifiers in root configuration files (package.json, setup.py, etc.)
   - Examine main application entry points
   - Check documentation files (README, docs)

2. Focus on extracting:
   - Project Title: A clear, descriptive name for the project
   - Description: A concise explanation of what the project does

Only after establishing these basic identifiers should you proceed with deeper architectural analysis.
{% endblock %}


{% block task_description_post_script %}
Additional architectural considerations will be discussed after establishing basic project identity.

The basic project identity consists of:
- Title: Clear, descriptive project name
- Description: Concise explanation of project purpose and scope

Architectural details like patterns and strategy will be refined in subsequent steps.
{% endblock %}

{% block node_details_interactive_reconfig %}
project title and description, followed by detailed configuration
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
project identity and configuration based on codebase analysis.
{% endblock %}

{% block information_about_task %}
Using Knowledge tools to first establish project identity, then analyze detailed configuration
{% endblock %}