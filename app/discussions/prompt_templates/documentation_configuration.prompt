{% block task_description_common_preface %}
                  
Your task is to immediately provide a complete configuration proposal and invoke the functioncall for this {{ node_type }} :
                        1. FIRST: Analyze all available information, requirements, and context provided above.    
                        2. NEXT: Must stream chunks for all property with complete configuration proposal.
                        3. THEN: Call capture_discussion_output with your complete proposal.
               
                Important formatting guidelines while streaming the output.
                - DO NOT USE NUMBERED LISTS
                - Use bullet points (•) instead of numbered lists
                - Organize information using headings and subheadings with bold or italic text
                - Use clear section breaks instead of numbering
                - Separate different properties with blank lines for clarity    

{% if config_state != "configured" %}
As an expert software architect, configure the System Context based on the C4 model:

{% else %}

You are an expert system architect reviewing the System Context for potential reconfiguration.

Current Configuration Context:

1. Existing State:
   - Current configuration: {{ details_for_discussion.get('original_node') | tojson(indent=2) }}
   - Configuration state: {{ details_for_discussion.get('config_state') }}

2. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   - System context: 
     - Users: {{ bg_info.get('system_context', {}).get('users') | tojson(indent=2) }}
     - External Systems: {{ bg_info.get('system_context', {}).get('external_systems') | tojson(indent=2) }}
     - Containers: {{ bg_info.get('system_context', {}).get('containers') | tojson(indent=2) }}
   - Requirements context:
     {% set req_context = bg_info.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

3. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}
   - Updated system context:
     - Users: {{ new_bg.get('system_context', {}).get('users') | tojson(indent=2) }}
     - External Systems: {{ new_bg.get('system_context', {}).get('external_systems') | tojson(indent=2) }}
     - Containers: {{ new_bg.get('system_context', {}).get('containers') | tojson(indent=2) }}
   - Updated requirements context:
     {% set new_req_context = new_bg.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in new_req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in new_req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

6. Current Container Configuration:
   Existing Containers: {{ details_for_discussion.get('containers') | tojson(indent=2) }}
   Existing Interactions: {{ details_for_discussion.get('existing_interactions') | tojson(indent=2) }}

Analysis Instructions:

1. Requirements Impact Analysis:
   - Compare original vs new functional and non-functional requirements
   

2. System Context Changes:
   - Analyze changes in project scope and strategy
   - Review modifications in user interactions
   - Evaluate external system integration changes

3. Container Architecture Analysis:
   - Review existing containers against new requirements
   - Evaluate container responsibilities and boundaries
   - Assess need for new containers or modifications
   - Analyze interface adequacy for new requirements

4. Required Changes:
   Modify Existing system context, or add any required containers, corresponding interfaces following the below guidelines 

{% endif %}

As an expert technical documentation writer, your task is to create comprehensive, well-structured documentation that follows industry best practices and standards. You will:

IMPORTANT:
1. Analyze the provided context and requirements then using it create the documentation
2. When creating documentation make sure that it is clear, accurate, and maintainable - each and every heading, sub-heading, point should be described as much as possible using the given project infomation.   
3. Follow appropriate documentation standards for the given documentation type
4. Ensure consistency in terminology and formatting like indentations, point format, etc.
5. Include detailed markdown tables where its appropriate.
6. Do not provide any type of summarization content (like conclution, summary..)
7. Documentation should be descriptive and informative as much as possible
8. Please do not provide any prefix and suffix commentaries. Only the relevent content should be the output.

Change Needed: 
    - Set to True if changes are required.

Change Log :
    - capture history of changes.
    
{% endblock %}

{% block autoconfig %}
Create comprehensive documentation based on the provided context and requirements. For DocumentationRoot nodes, 
establish the overall structure and main content sections. For Sub_Section nodes, generate detailed content 
that aligns with the section's purpose and requirements.
 
Output should only contain full, production-ready documentation content following Markdown format. do not provide your comments or thoughts
{% endblock %}


{% block node_details_interactive_reconfig %}
Documentation
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
Documentation Sections 
{% endblock %}

{% if "DocumentationRoot" in details_for_discussion.current_node.labels  %}

in this scenario main focus is DocumentationRoot, Here are the details for that :

Documentation Type: {{ details_for_discussion.documentation_config.title_prefix }}
Parent Node Details:
- Title: {{ details_for_discussion.parent_node.properties.Title }}
- Description: {{ details_for_discussion.parent_node.properties.Description }}
{% for key, value in details_for_discussion.parent_node.properties.items() %}
{% if key not in ['Title', 'Description'] %}
- {{ key }}: {{ value }}
{% endif %}
{% endfor %}

Parent Node Context:
{% for key, nodes in details_for_discussion.node_context.grouped_children.items() %}
{{ key }}s:
{% for node in nodes %}
- {{ node.properties.Title }}: {{ node.properties.Description }}
{% endfor %}
{% endfor %}


This document will contain the following sections:
{% for section in details_for_discussion.documentation_config.sections %}
{{ loop.index }}. {{ section.title }} - {{ section.description }}
{% endfor %}


{% elif "Sub_Section" in details_for_discussion.current_node.labels %}

in this scenario main focus is Sub_Section, Here are the details for more specifications:

Section: {{ details_for_discussion.current_node.properties.Title }}
Type: {{ details_for_discussion.section_config.type }}
Description: {{ details_for_discussion.section_config.description }}

Parent Context:
{{ details_for_discussion.parent_context.properties | tojson(indent=2) }}

Related Information:
{% for key, nodes in details_for_discussion.parent_context.grouped_children.items() %}
{{ key }}s:
{% for node in nodes %}
- {{ node.properties.Title }}: {{ node.properties.Description }}
{% endfor %}
{% endfor %}

Analyze the above context and create a sub section content using suitable information.

please follow the IMPORTANT 8 guidelines mentioned above and all the other specifications, then provide the comprehensive content for the sub section,
- Avoid using any Additional details like Conclution, Timelines and Team Compositions etc...
- Make sure to Provide only valid Mermaid scripts without any syntax errors when providing any kind of chart / diagram content

It is very important that you provide only the final output. that means you should provide the content without any additional comments or remarks after or before the content - Just only the generated content.

{% endif %}

{%block system_prompt %}
You are an expert technical documentation writer with extensive experience creating various types of software documentation including PRDs, SADs, API documentation, and component documentation. its important that always the response should align with capture_discussion_output and in Markdown format.
{% endblock %}

{% block information_about_task %}

            Information is stored in the system as nodes of a graph. Here is the Node that stores the information for the current node:

            The current node is a part of a {{ root_node_type }} with the following information:
            {{ details_for_discussion.get('root_node') | tojson(indent=2) }}

            The current node is a part of the following tree of nodes of information:
            {{ details_for_discussion.get('parent_tree') | tojson(indent=2) }}

        {% endblock %}