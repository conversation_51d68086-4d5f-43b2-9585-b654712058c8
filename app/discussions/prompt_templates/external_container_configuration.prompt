{% extends "base_discussion.prompt" %}


{% block task_description_common_preface %}
As an expert software architect, analyze the provided API documentation and configure the External Container:



1. Container Properties:
   Title: Name of the external system/service
   Description: Purpose and role of the external system
   Selected_Tech_Stack: NA (Not applicable for external containers)
   UserInteractions: NA (Not applicable for external containers)
   ExternalSystemInteractions: NA (Not applicable for external containers)
   ImplementedRequirementIDs: NA (Not applicable for external containers)
   Repository_Name: NA (Not applicable for external containers)
   
2. Component Creation: NA (Not applicable for external containers)

3. C4 Diagram:

   If interface is created for the external container else NA

   - C4 System Context Diagram Example - use this for references purpose only - generate diagrams based on the details of the users, external system identified for the project appropriately.

IMPORTANT
The diagram syntax should be provided as raw Mermaid code without the keyword "mermaid" at the beginning and without any surrounding quotes. Start directly with 'graph TB'.
'

   {% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}

   graph TB
       subgraph ExternalSystem["External System Name"]
           ExtContainer["External Container<br/>[Container: External]"]
           InterfaceNode["API Interface<br/>[Interface]<br/><i>OpenAPI Specification</i>"]
           ExtContainer -->|"exposes"| InterfaceNode
       end

       %% Styling
       classDef container fill:#666666,stroke:#333333,color:#ffffff
       classDef interface fill:#85bbf0,stroke:#5d82a8,color:#000000
       
       class ExtContainer container
       class InterfaceNode interface
   
{% if project_knowledge %}

4. API Documentation Review:
   - Process provided API documentation using document tools
   - Identify endpoints, methods, and data structures
   - Capture authentication requirements
   - Document data formats
5. Interface Definition:
   - Create Interface node capturing:
    Name of the Interface
    Description of the Interface
    API specifications in OpenAPI format - capture OpenAPI specification wrapped in <JSON> tags
    - Available endpoints
    - Data contracts
    - Authentication details

{% endif %}


{% endblock %}

{% block autoconfig %}
Analyze the provided API documentation and configure the external container with appropriate interface specifications.
{% endblock %}


{% block information_about_task %}
{{ super() }}
API Documentation: {{ details_for_discussion.get('api_documentation') | tojson(indent=2) }}
Existing Interface: {{ details_for_discussion.get('existing_interface') | tojson(indent=2) }}
Consuming Containers: {{ details_for_discussion.get('consuming_containers') | tojson(indent=2) }}
{% endblock %}

{% block system_prompt %}
You are an expert in API integration. Your task is to analyze third-party API documentation and create appropriate interface configurations that capture the API specifications for system integration.
{% endblock %}