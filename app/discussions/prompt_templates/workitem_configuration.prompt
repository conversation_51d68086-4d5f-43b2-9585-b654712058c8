{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}

{% if config_state != "configured" %}
You are an expert technical project manager and strategic planner. 


{% else %}

You are an expert technical project manager and strategic planner reviewing a workitem node for potential reconfiguration.

Current Configuration Context:
1. Existing Node State:
   - Current configuration: {{ details_for_discussion.get('original_node') | tojson(indent=2) }}
   - Configuration state: {{ details_for_discussion.get('config_state') }}

2. Original Project and Requirements Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | toj<PERSON>(indent=2) }}

3. Current Project and Requirements Context:
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Project context: {{ new_bg.get('project_context', {}) | toj<PERSON>(indent=2) }}
4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log') | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason') }}

5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

Based on the current configuration and context:

1. Compare original and current contexts
2. Analyze if updates are needed based on:
   - Changes in project context
   - New user inputs
3. If changes are needed:
   - Create new workitems or modify the existing workitems as needed.
   - Capture the reason for the proposed modification.
   - IMPORTANT: Always set the "changes_needed" flag to true whenever you make any modifications, including adding new child nodes, modifying existing nodes, or updating any fields.
4. If no changes needed, update the reason in the change reason
   - Explain why current configuration remains valid
   - Reference specific workitems that support this

{% endif %}

In our system, WorkItems represent top levels of work items that has to be performed to complete this project. 

Work items will be performed by people or AI agents. 
 -  If there is an AI agent capable of handling the work item, please assign the work item to them.
 -  If not assign it to a human lead for that function, identified by the role name for that role
 
also For each work item,
-  Provide a descriptive title and elaborate description
-  Assign an appropriate priority and Estimated duration. 


Change Needed: 
    - Set to True if changes are required.
    
Change Log :
    - capture history of changes.

{% endblock %}

{% block autoconfig %}
    Your task is to create or update information for high-level WorkItems for the project. These work items should be created as child nodes for the root work item.
    Provide descriptive title to understand the purpose of workitem and elaborate description to explain the steps involved for execution.
    These typically include broad activities such as Market Research, Requirements Gathering, Architecture Design, Prototype development, code development, Test Strategy Development, etc.
    Please factor in the other major initiatives of a typical project and suggest suitably with title and detailed description.
{% endblock %}


{% block node_details_interactive_reconfig %}
Follow the above guidelines and propose the required changes based on your analysis.
Take feedback from the user on the proposal and consolidate. 
Make sure to capture all the changes in new child nodes or modified child nodes if the existing nodes do not include them

IMPORTANT: Always set the "changes_needed" flag to true in your output whenever you make ANY changes (adding nodes, modifying existing nodes, etc.)

{% endblock %}

{% block auto_reconfig %}
Create updated Requirement Root based on the above guidelines.Make sure to capture all the changes in new child nodes or modified child nodes if the existing nodes do not include them
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
ensuring all major aspects of project scope are adequately addressed
{% endblock %}