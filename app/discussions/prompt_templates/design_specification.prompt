{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}

{% if config_state != "configured" %}
Create a detailed design specification for the current component of the software application. The specification MUST include ALL of the following elements:

The specification should be comprehensive enough to enable AI agents to generate source code for the component.

Here is the architectural node for the component for which the detail design node is being configured:
{{details_for_discussion.get('architecture_node') | tojson(indent=2)}}


{% else %}
You are an expert system architect reviewing the design behaviour for potential reconfiguration.

Current Configuration Context:

1. Existing State:
   - Current configuration: {{ details_for_discussion.get('original_node') | tojson(indent=2) }}
   - Configuration state: {{ details_for_discussion.get('config_state') }}

2. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - System context: 
     - design_node: {{ bg_info.get('design_context', {}).get('design_node') | tojson(indent=2) }}
     - parent_component_node: {{ bg_info.get('system_context', {}).get('parent_component_node') | tojson(indent=2) }}
   

3. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated system context:
     - design_node: {{ new_bg.get('design_context', {}).get('design_node') | tojson(indent=2) }}
     - parent_component_node: {{ new_bg.get('system_context', {}).get('parent_component_node') | tojson(indent=2) }}
  

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

6. Current Component Configuration:
   Existing parent_component_node: {{ details_for_discussion.get('parent_component_node') | tojson(indent=2) }}
   Existing container_component_details: {{ details_for_discussion.get('container_component_details') | tojson(indent=2) }}
   Algorithms: {{ details_for_discussion.get('algorithms') | tojson(indent=2) }}

Analysis Instructions:


1. Component Architecture Analysis:
   - Review the parent component against new requirements
   - Evaluate component responsibilities and boundaries based on the detected changes
   - Assess need for design specification modifications

2. Required Changes:
   Modify Existing design specification, or add any required design specification elements following the below guidelines 

{% endif %}


Change Needed: 
    - Set to True if changes are required.

Change Log :
    - capture history of changes.
{% endblock %}

{% block task_description_post_script %}
IMPORTANT:
- Ensure ALL elements listed above are included in your response.
- Each element must be comprehensive and clear.
- The design specification must align with the component's role in the overall system architecture.
- If any information is missing or unclear, make reasonable assumptions and state them explicitly.
{% endblock %}

{% block autoconfig %}
Analyze the component's information and autonomously generate a detailed design specification. Ensure that ALL elements listed in the task description are fully addressed:
1. Component Overview
2. Inputs and Outputs
3. Functional Requirements
4. Non-functional Requirements
5. Dependencies
6. Data Management
7. Error Handling
8. Testing Considerations

Consider the component's role within the broader system and derive any additional details that would enhance its functionality and integration.
{% endblock %}

{% block auto_reconfig %}
Create updated Design Specification based on the above guidelines. Make sure to capture all the changes in new child nodes or modified child nodes if the exixting nodes do not include them

{% endblock %}


{% block interactive_reconfig %}
Follow the above guidelines and propose the required changes based on your analysis.
Take feedback from the user on the proposal and consolidate. Make sure to capture all the changes in new child nodes or modified child nodes if the exixting nodes do not include them
{% endblock %}

{% block node_details_interactive_reconfig %}
design specification (including all required elements)
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
design specification. Focus on analyzing the current system state to derive optimal solutions that align with best practices in software design and the evolving needs of the system. Ensure ALL elements of the design specification are addressed:
1. Component Overview
2. Inputs and Outputs
3. Functional Requirements
4. Non-functional Requirements
5. Dependencies
6. Data Management
7. Error Handling
8. Testing Considerations
{% endblock %}
{% block information_about_task %}
Parent Container USES Relationships: {{ details_for_discussion.get('component_uses_relationships') | tojson(indent=2) }}
Here is the architectural node for the component for which the detail design node is being configured:
{{details_for_discussion.get('architecture_node') | tojson(indent=2)}}

{{details_for_discussion.get('existing_specifications') | tojson(indent=2)}}
Current component interfaces: {{ details_for_discussion.get('interface_details') | tojson(indent=2) }}
{% endblock %}