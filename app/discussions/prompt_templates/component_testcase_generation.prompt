{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}
You are an expert in software testing and quality assurance. 
Your task is to generate comprehensive test cases for a component while considering its role in the overall system.

Follow these steps:
1. Analyze the System Context:
   - Review the system's overall architecture and design
   - Understand external systems and their interactions
   - Consider system-wide quality requirements and constraints
   - Identify cross-cutting concerns that affect multiple components

2. Review Component Context:
   - Understand the component's role in the system
   - Analyze its interfaces and relationships with other components
   - Consider its container context and container-level interfaces
   - Review its technology stack and non-functional requirements

3. Analyze Requirements and User Stories:
   - Review related user stories and their acceptance criteria
   - Understand the business value and user expectations
   - Consider epic-level requirements and project objectives
   - Identify cross-component requirements and dependencies

4. Generate Test Categories:
   - Create appropriate categories for functional and non-functional tests
   - Consider industry best practices and project-specific needs
   - Ensure categories cover both component-specific and system-wide aspects
   - Maintain clear separation between functional and non-functional categories

5. Review Existing Test Cases:
   - Analyze pre-filtered test cases for relevance
   - Identify potential reuse of existing test patterns
   - Consider test coverage gaps
   - Look for opportunities to extend existing test cases

6. Generate New Test Cases:
   - Create test cases that cover both component and system aspects
   - Include functional, non-functional, stress, stability, and interoperability tests
   - Ensure proper test level classification (Unit/Component/System)
   - Consider automation potential based on test characteristics

7. For each test case:
   - Assign appropriate tags from the common tag set
   - Include specific details relevant to the test type
   - Link to related user stories and requirements
   - Set automation potential based on test characteristics

8. Test Classification Guidelines:
   - Unit Tests: Focus on component's internal behavior and interfaces
   - Component Tests: Cover component's role in system workflows
   - System Tests: Address end-to-end scenarios and cross-component interactions
   - Consider container-level interfaces and external system interactions

9. Automation Considerations:
   - Evaluate test automation potential based on:
     - Execution frequency and stability
     - Complexity and maintainability
     - Cost-benefit ratio
     - Technical feasibility
   - Consider project's test automation framework
   - Focus on high-value, stable test cases for automation

10. Quality Assurance:
    - Ensure comprehensive coverage of requirements
    - Consider edge cases and error conditions
    - Include performance and security aspects
    - Maintain traceability to requirements

IMPORTANT: Focus on system-wide functionality and requirements rather than implementation details. Consider the component's role in the larger system context while maintaining proper test level classification.
{% endblock %}

{% block information_about_task %}
{{ super() }}
Project Details:
{{ details_for_discussion.get('project') | tojson(indent=2) }}

System Context:
{{ details_for_discussion.get('system_context') | tojson(indent=2) }}

Container Context:
{{ details_for_discussion.get('container') | tojson(indent=2) }}

Component Details:
{{ details_for_discussion.get('component') | tojson(indent=2) }}

Related User Stories:
{{ details_for_discussion.get('user_stories') | tojson(indent=2) }}

External Systems:
{{ details_for_discussion.get('external_systems') | tojson(indent=2) }}

Sibling Components:
{{ details_for_discussion.get('sibling_components') | tojson(indent=2) }}

Component Interfaces:
{{ details_for_discussion.get('interfaces') | tojson(indent=2) }}

Existing Test Categories:
{{ details_for_discussion.get('functional_categories') | tojson(indent=2) }}
{{ details_for_discussion.get('non_functional_categories') | tojson(indent=2) }}

Pre-filtered Existing Test Cases:
{{ details_for_discussion.get('relevant_existing_test_cases') | tojson(indent=2) }}

Common Tags:
{{ details_for_discussion.get('common_project_tags') | tojson(indent=2) }}
{% endblock %}

{% block autoconfig %}
Follow the guidelines provided above to create comprehensive test cases that consider both component-specific and system-wide aspects.
{% endblock %}

{% block interactive_config %}
Your task is to immediately propose a comprehensive set of test cases for this component following all the guidelines provided above.
Call capture_discussion_output with your complete proposal, then summarize the test suite you've created.
{% endblock %} 