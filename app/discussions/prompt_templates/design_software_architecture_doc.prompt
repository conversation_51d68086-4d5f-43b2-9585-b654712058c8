{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}
Create a comprehensive software architecture document for the given component of the software architecture..
Use the provided diagrams and documents to inform your design:
{{ details_for_discussion.get('diagrams_and_docs') | toj<PERSON>(indent=2) }}
{% endblock %}

{% block task_description_post_script %}
Ensure the architecture document is thorough, clear, and aligns with modern software development practices and the specific needs and requirements of the overall Architecture and software design.
{% endblock %}

{% block autoconfig %}
Develop a detailed software architecture document covering all aspects of the component and its place within the larger system.
Consider system scalability, security, and performance requirements.
Derive appropriate architectural patterns and technologies that would enhance the system's functionality.
{% endblock %}

{% block auto_reconfig %}
Update the existing software architecture document based on changes in requirements, system design, or workflow processes.
Analyze the current architecture and system needs to identify areas for improvement or expansion.
{% endblock %}


{% block information_about_task %}
Current component interfaces:
{{ details_for_discussion.get('direct_interfaces') | toj<PERSON>(indent=2) }}

Use this information to ensure your architecture document accurately represents the component's role and interactions within the system.
{% endblock %}

{% block output_format %}
Provide the software architecture document as a structured text in the 'api-doc-in-text' field of the APIDoc node.
Include sections for component overview, system architecture, data flow, technologies used, scalability, security, and performance considerations.
Use clear headings and subheadings for easy navigation.
{% endblock %}