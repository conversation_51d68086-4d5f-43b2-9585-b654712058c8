{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}

As an expert software architect, review the Project Details, parent Container, Functional and Non-Fucntional requirements and configure the given Architecture node:

{% if config_state != "configured" %}
As an expert software architect, review the Project Details, parent Container, Functional and Non-Fucntional requirements and configure the given Architecture node:

{% else %}
You are an expert system architect reviewing the Architecture for potential reconfiguration.

Current Configuration Context:

1. Existing State:
   - Current configuration: {{ details_for_discussion.get('original_node') | tojson(indent=2) }}
   - Configuration state: {{ details_for_discussion.get('config_state') }}

2. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   - System context: 
     - Components/Containers: {{ bg_info.get('system_context', {}).get('component_details') | tojson(indent=2) }}
   

3. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}
   - Updated system context:
     - Components/Containers: {{ new_bg.get('system_context', {}).get('component_details') | tojson(indent=2) }}
   

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

6. Current Container Configuration:
   Existing Containers: {{ details_for_discussion.get('component_details') | tojson(indent=2) }}
   Existing Interactions: {{ details_for_discussion.get('existing_interactions') | tojson(indent=2) }}

7. Current Component Configuration:
   Existing Containers: {{ details_for_discussion.get('component_details') | tojson(indent=2) }}
   Existing Interactions: {{ details_for_discussion.get('existing_interactions') | tojson(indent=2) }}

Analysis Instructions:

1. Container Architecture Analysis:
   - Review existing containers against new requirements
   - Evaluate container responsibilities and boundaries
   - Assess need for new containers or modifications
   - Analyze interface adequacy for new requirements

2. Component Architecture Analysis:
   - Review existing components against new requirements
   - Evaluate component responsibilities and boundaries
   - Assess need for new components or modifications
   - Analyze interface adequacy for new requirements

3. Required Changes:
   Conduct the architecture configuration based on the new requirements
   - IMPORTANT: Always set the "changes_needed" flag to true whenever you make any modifications, including adding new child nodes, modifying existing nodes, or updating any fields.

{% endif %}

1. IMPORTANT: Components are leaf nodes and should not be decomposed 
      - Set 'isArchitectureLeaf' to 'yes' always
      - No interfaces will be created at this level

{% set architecture_pattern = details_for_discussion.get('architecture_pattern', '').lower() %}

2. Component Description must include these sections clearly marked:
{% if architecture_pattern in ['monolithic-service','monolithic-application', 'multi-container-single-component'] %}
   a. Core Technologies:
      - Comprehensive technology stack to support entire container functionality
      - Primary programming language(s) and frameworks for all features
      - Database technologies for all data storage needs
      - Complete runtime environment requirements
      - Integration technologies for all external system interactions
      - UI technologies if user interfaces are required
   
   b. Build and Development Tools:
      - Complete build system setup
      - All required development tools
      - Testing frameworks for all layers
      - Deployment and CI/CD requirements

{% else %}
   a. Core Technologies:
      - Primary programming language(s) and versions
      - Key frameworks and libraries with versions
      - Database technologies if applicable
      - Runtime environment requirements
     
   b. Build and Development Tools:
      - Build system (e.g., Maven, Gradle, npm)
      - Required development tools
{% endif %}

3. External System Interactions :
{% if architecture_pattern in ['monolithic-service','monolithic-application']%}
   - Review parent container's USES relationships with external containers
   - Identify which external systems this component interacts with
   - Create USES Relationship with the external container - with Properties:

    type: Fixed value "USES" indicating external system dependency
    name: External service or API name that is being consumed
    description: Purpose and functionality being used from the external service
    source id: Node id of the current component consuming the service - should be an integer
    target id: Node id of the external container providing the service - should be an integer
    technology: Protocol or method used to integrate with the external service
{% else %}
NA (Not applicable for Multicontainer case)
{% endif%}
4. Map Implemented Requirements
      - Analyze the Component Purpose and Responsibilities 
      - Review the requirements given below and identify the requirements that this component implements 
      - Add only those requirement node IDs to the ImplementedRequirementIDs property, as an array of integers 
      - IMPORTANT: Verify that each requirement ID exists in the project before adding to ImplementedRequirementIDs
      - DO NOT add requirement IDs that don't exist in the project
      - Ensure the requirements are properly linked to the container 

         {{ details_for_discussion.get('functional_requirements', '') | tojson(indent=2) }}
         {{ details_for_discussion.get('architectural_requirements', '') | tojson(indent=2) }}

5. Root Folder:
   - Set Root_Folder name based on component functionality using kebab-case format
6. Component Diagram:
   - Create a C4 Component Diagram using Mermaid syntax that shows both component dependencies and external container relationships:
   Example Component Diagram showing external system interaction.
   - Use this for reference purpose only - create the diagram based on the component and its relevant details. 
{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}
   ```
   graph TB
      Component["Architecture Component"]
      ExtCont["External Container"]
      
      Component -->|"USES<br/>Service: External API<br/>Protocol: REST/HTTPS"| ExtCont
      
      classDef component fill:#85bbf0,stroke:#5d82a8,color:#000000
      classDef external fill:#666666,stroke:#333333,color:#ffffff
      
      class Component component
      class ExtCont external
   ```



Change Needed: 
    - Set to True if changes are required.

Change Log :
    - capture history of changes.
   
{% endblock %}
  
{% block autoconfig %}
Create the required details based on the guidelines provided above and save.
{% endblock %}


{% block node_details_interactive_reconfig %}
Review the above guidelines and propose the required changes based on your analysis.
Take feedback from the user on the proposal and consolidate. Make sure to capture all the changes in new child nodes or modified child nodes if the exixting nodes do not include them
{% endblock %}

{% block auto_reconfig %}
Create or update the funtional and / or non-functional requirements following the above guidelines.Make sure to capture all the changes in new child nodes or modified child nodes if the exixting nodes do not include them
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
architectural configuration as defined in the function schema. Propose improvements or additions based on the current state of the architecture, the component's or system's architectural guidelines in the llm_metadata, a comprehensive MermaidChart with correct syntax and industry best practices in software design.
{% endblock %}

{% block information_about_task %}
    {{ super() }}
    Here are all the interfaces that are already defined for the current component: {{ details_for_discussion.get('direct_interfaces') | tojson(indent=2) }}
    Parent Container USES Relationships: {{ details_for_discussion.get('parent_uses_relationships') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
    {{ super() }}
   Project Details :  {{ details_for_discussion.get('project_details') }} 
    Container Details: {{ details_for_discussion.get('container') }}
    Here are some functional requirements for the whole system to take into account while you are designing the architecture for this module: 
    {{ details_for_discussion.get('functional_requirements') | tojson(indent=2) }}
    Here are some non-functional requirements for the whole system to take into account while your are designing the architecture for this module: 
    {{ details_for_discussion.get('architectural_requirements') | tojson(indent=2) }}
     Here are top levels of the architecture tree: 
    {{ details_for_discussion.get('top_levels') | tojson(indent=2) }}

    Here all are the relationships between all the architectural components in the system: 
    {{ details_for_discussion.get('relationships') | tojson(indent=2) }}
   
{% endblock %}