UserStory:
Modified-Node: {Title: modified-user-story-title, Description: modified-user-story-description,Status:modified-user-story-status},
Tasks: [ {Title: title, Description: description, Type: 'Task',Dependencies:Dependencies,AssignedTo:AssignedTo, Priority:Priority,Status:Status}, {Title: Title, Description: Description, Type: 'Task',Dependencies:Dependencies,AssignedTo:AssignedTo, Priority:Priority,Status:Status}, ...]

Epic:
Modified-Node: {Title: modified-epic-title, Description: modified-epic-description, DefinitionOfDone: DefinitionOfDone,Priority:Priority, Assignee:Assignee, Status:Status}, {Title: Title, Description: Description, Type: 'Epic',DefinitionOfDone: DefinitionOfDone,Priority:Priority, Assignee:Assignee,Status:modified-epic-status}
UserStories: [ {Title: title, Description: Description, Type: UserStory,AcceptanceCriteria:AcceptanceCriteria,AssignedTo:AssignedTo,DueDate:DueDate,UserStoryType:UserStoryType, StoryPoints:StoryPoints, Status:Status}, {Title: Title, Description: Description, Type: UserStory,AcceptanceCriteria:AcceptanceCriteria,DueDate:DueDate,UserStoryType:UserStoryType, StoryPoints:StoryPoints, Status:Status}, ...]

Project:
Modified-Node: {Title: title, Description: modified-description, Scope: scope-description-for-the-product-in-plain-text }

RequirementRoot:
Modified-Node: {Title: title, Description: modified-description}, 
Epics: [ {Title: title, Description: description, Type: 'Epic',DefinitionOfDone: DefinitionOfDone,Priority:Priority, Assignee:Assignee, Status:status}, {Title: title, Description: description, Type: 'Epic',DefinitionOfDone: DefinitionOfDone,Priority:Priority, Assignee:Assignee, Status:Status}, ...]

WorkItem:
Modified-Node: {Title: title, Description: modified-description}, 
Sub-Tasks: [ {Title: title, Description: description, Type: 'WorkItem', Assignee: assignee}, {Title: title, Description: description, Type: 'WorkItem', Assignee: assignee}, ...]

dependencies:
Dependencies:[dependency-node-id-1, dependency-node-id-2, dependency-node-id-3, ... ]

Architecture_requirements:
functional_requirements: text-with-aggregrated-list-of-functional-requirements-in-plain-text
architectural_requirements: text-with-aggregrated-list-of-architectural-requirements-in-plain-text

Architecture: 
IsArchitecturalLeaf: "yes" or "no" 
Architectural_Elements: 
[
    {ID:id, Title:title, Type:type, Description:description, Functionality: functionality-covered-by-this-component, Tech_Stack_Choices: list-of-technology-stack-choices-for-this-component-in-plain-text, Recommended_Tech_Stack: recommended-tech-stack-for-this-component-in-plain-text},
    {ID:id, Title:title, Type:type, Description:description, Functionality: functionality-covered-by-this-component, Tech_Stack_Choices: list-of-technology-stack-choices-for-this-component-in-plain-text, Recommended_Tech_Stack: recommended-tech-stack-for-this-component-in-plain-text},
    ...
]
Interfaces:
[
    {"source": source-ID, "target": target-ID, "name": interface-name, "type": type-name, "description": short-text-description},
    {"source": source-ID, "target": target-ID, "name": interface-name, "type": type-name, "description": short-text-description},
    ...
]
Modified-Node: {Title:title, Description: detailed-desciption-of-the-architecture, MermaidChart: text-string-with-mermaid-chart}

ArchitectureDesignDetails:
Modified-Node: {Title:title, Design_Details: detailed-description-of-design-details-for-this-component-in-plain-text-that-can-be-used-for-further-discussions}

ArchitectureStatusCheck:
Next-Steps: "Continue Architectural Decomposition into sub-components" or "Start Detailed Design"

InterfaceDesignDetails:
Modified-Node: 
{
    Title:title, 
    FunctionalRequirements: detailed-functional-requirements-for-the-interface-in-plain-text,
    TechnicalRequirements: detailed-technical-requirements-for-the-interface-in-plain-text,
    DesignDetails: detailed-interface-definition-in-plain-text
    InterfaceDescription: full-interface-description-in-plain-text
}

DesignSpecification:
Modified-Node: 
{
    Title:title, 
    Description: detailed-description-of-design-for-the-component-in-plain-text,
    PurposeAndResponsibilies: detailed-purpose-and-responsibilites-of-the-component-in-plain-text,
    InputsAndOutputs: detailed-inputs-and-outputs-of-the-component-in-plain-text,
    FunctionalRequirements: detailed-functional-requirements-for-the-component-in-plain-text,
    NonFunctionalRequirements: detailed-non-functional-requirements-for-the-component-in-plain-text,
    Dependencies: dependencies-on-other-components-in-plain-text
}

InterfaceDefinition:
Modified-Node:
{Title: title, Description: desciption },
Methods: 
[
    {Title: method-name, Type: 'Method', Signature: method-signature-for-method-in-plain-text},
    {Title: method-name, Type: 'Method', Signature: method-signature-for-method--in-plain-text},
    ...
]
HttpRoutes:
[
    {Title: route-name, Type: 'Route', Route: route-in-plain-text},
    {Title: route-name, Type: 'Route', Route: route-in-plain-text},
    ...
]
DataContracts: 
[
    {Title: Data-contract-name, Type: 'DataContract', DataContract: Data-contract-for-data-element-in-plain-text},
    {Title: Data-contract-name, Type: 'DataContract',DataContract: Data-contract-for-data-element-in-plain-text},
    ...
]
CommunicationProtocols: 
[
    {Title: Protocol-name, Type: 'Protocol', ProtocolDescription: communication-protocol-for-the-interface-if-any-in-plain-text}
    {Title: Protocol-name, Type: 'Protocol', ProtocolDescription: communication-protocol-for-the-interface-if-any-in-plain-text}
    ...
]

DesignBehavior:
Modified-Node:
{Title: title, Description: desciption}
AlgorithmicDetails: 
[
    {Title: functionality1, Type: 'Algorithm', Details: algorithmic-logic-or-psuedo-codes-for-the-component-in-plain-text},
    {Title: functionality2, Type: 'Algorithm', Details: algorithmic-logic-or-psuedo-codes-for-the-component-in-plain-text},
    ...
]
StateManagementLogic: 
[ 
    {Title: state-variable-name, Type: 'StateLogic', Logic: state-management-logic-for-the-component-if-any-in-plain-text},
    {Title: state-variable-name, Type: 'StateLogic', Logic: state-management-logic-for-the-component-if-any-in-plain-text},
    ...
]

DesignComponentInteractions:
Modified-Node: {Title: title, Description: desciption}
SequenceDiagrams: 
[ 
    {Title: sequence-name, Type: 'Sequence', Description: desciption-in-plain-text, Diagram: sequence-diagrams-using-mermaid-chart-in-plain-text},
    {Title: sequence-name, Type: 'Sequence', Description: desciption-in-plain-text, Diagram: sequence-diagrams-using-mermaid-chart-in-plain-text},
    ...   
]
StateMachineDiagrams: 
[
    {Title: state-variable-name, Type: 'StateDiagram': Diagram: state-machine-diagrams-if-any-in-mermaid-chart},
    {Title: state-variable-name, Type: 'StateDiagram': Diagram: state-machine-diagrams-if-any-in-mermaid-chart},
    ...
]

DesignTestCases:
UnitTestCases:
[
    {Title: title-of-the-test-case, Type: 'UnitTest', Description: description-of-the-test-case, ExpectedResult: expected-result-of-the-test-case-in-plain-text},
    {Title: title-of-the-test-case, Type: 'UnitTest', Description: description-of-the-test-case, ExpectedResult: expected-result-of-the-test-case-in-plain-text},
    ...
]
IntegrationTestScenarios:
[
    {Title: title-of-the-test-case, Type: 'IntegrationTest', Description: description-of-the-test-case, ExpectedResult: expected-result-of-the-test-case-in-plain-text},
    {Title: title-of-the-test-case, Type: 'IntegrationTest', Description: description-of-the-test-case, ExpectedResult: expected-result-of-the-test-case-in-plain-text},
    ...
]
PerformanceTestCases:
[
    {Title: title-of-the-test-case, Type: 'PerformanceTest', Description: description-of-the-test-case, ExpectedResult: expected-result-of-the-test-case-in-plain-text},
    {Title: title-of-the-test-case, Type: 'PerformanceTest', Description: description-of-the-test-case, ExpectedResult: expected-result-of-the-test-case-in-plain-text},
    ...
]
FaultToleranceTestCases:
[
    {Title: title-of-the-test-case, Type: 'RobustnessTest', Description: description-of-the-test-case, ExpectedResult: expected-result-of-the-test-case-in-plain-text},
    {Title: title-of-the-test-case, Type: 'RobustnessTest', Description: description-of-the-test-case, ExpectedResult: expected-result-of-the-test-case-in-plain-text},
    ...
]

DesignClassDiagrams: 
Modified-Node: {Title: title, Description: desciption}
ClassDiagrams:
[
    {Title: state-variable-name, Type: 'ClassDiagram': Diagram: class-diagrams--in-mermaid-chart},
    {Title: state-variable-name, Type: 'ClassDiagram': Diagram: class-diagrams--in-mermaid-chart},
    ...
]

DesignAPIDocs: 
Modified-Node: {Title: title, Description: desciption}
APIDocs:
[
    {Title: state-variable-name, Type: 'APIDoc': api-doc-in-text}
]

DesignSADDocs: 
Modified-Node: {Title: title, Description: desciption}
SADDocs:
[
    {Title: state-variable-name, Type: 'SADDoc': software-architecture-doc-in-text}
]
