{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}
{% if config_state != "configured" %}

{% if project_knowledge %}
   - Try out these for `search_terms` and get_key_values `keys` initially ['ID', 'User Story', 'acceptance criteria', 'test cases'] so we can first check if the document already has the epics mentioned.
   - The User Story Description and Acceptance Criteria for the user story must match exactly the information specified in the provided document for the corresponding user story.
   - Strictly refer to the provided document at all times.
   
{% else %}
   - You are an expert agile requirements analyst. In our system, User Stories capture specific functionality from an end user's perspective, breaking down Epics into manageable implementation items with clear acceptance criteria.

{% endif %}

{% else %}

You are a Project Manager reviewing a User Story node for potential reconfiguration.

Current Configuration Context:
1. Existing Node State:
   - Current configuration: {{ details_for_discussion.get('original_node') | tojson(indent=2) }}
   - Configuration state: {{ details_for_discussion.get('config_state') }}

2. Original Project and Requirements Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   {% if bg_info.get('requirement_context') %}
   - Requirements:
     {% for epic_detail in bg_info.get('requirement_context', []) %}
     Epic: {{ epic_detail.epic.properties.Title }}
     Description: {{ epic_detail.epic.properties.Description }}
     User Stories:
     {% for story in epic_detail.user_stories %}
     - {{ story.properties.Title }}
     {% endfor %}
     {% endfor %}
   {% endif %}

3. Current Project and Requirements Context:
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}
   {% if new_bg.get('requirement_context') %}
   - Requirements:
     {% for epic_detail in new_bg.get('requirement_context', []) %}
     Epic: {{ epic_detail.epic.properties.Title }}
     Description: {{ epic_detail.epic.properties.Description }}
     User Stories:
     {% for story in epic_detail.user_stories %}
     - {{ story.properties.Title }}
     {% endfor %}
     {% endfor %}
   {% endif %}

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log') | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason') }}

5. User Interaction History:
   - Previous user inputs: {% if details_for_discussion and details_for_discussion.get('user_interaction') and details_for_discussion.get('user_interaction').get('input') is not none %}{{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}{% else %}None{% endif %}

Based on the current configuration and context:

1. Compare original and current contexts
2. Analyze if updates are needed based on:
   - Changes in project context
   - Changes in epics and user stories
   - New user inputs
3. If changes are needed:
   - Create new user stories or modify the existing user stories as needed.
   - Capture the reason for the proposed modification.
   - IMPORTANT: Always set the "changes_needed" flag to true whenever you make any modifications, including adding new child nodes, modifying existing nodes, or updating any fields.

4. If no changes needed, update the reason in the change reason
   - Explain why current configuration remains valid
   - Reference specific requirements that support this

{% endif %}


When configuring a User Story, you should focus on these key properties:
1. Title: Format as "As a [user role], I want [goal], so that [benefit]"
2. Description: Provide detailed context and purpose of the functionality
3. Priority: Set relative importance (High, Medium, Low) aligned with parent Epic
4. StoryPoints: Estimate effort using a predefined scale (e.g., Fibonacci sequence)
5. AcceptanceCriteria: Define elaborate, measurable conditions that must be met for satisfying the User Story.

After configuring the User Story, create essential Tasks (can be one or multiple) as child nodes. For each Task, specify:
- Title: Clear and descriptive name of the implementation task
- Description: Technical details and implementation steps
- Type: Always set as "Task"
- StoryPoints: Estimated effort using the same Fibonacci scale

Context Information:
- Project: {{ details_for_discussion.get('root_node') | tojson(indent=2) }}

- Parent Epic: {{ details_for_discussion.get('parent_node') | tojson(indent=2) }}


Change Needed: 
    - Set to False if no changes are required.

Change Log :
    - capture history of changes.

{% endblock %}

{% block autoconfig %}

{% if project_knowledge %}
   - The search_terms for this Userstory configuration must exactly match the list: [ 'User Story', 'User Stories', 'acceptance criteria', 'test cases'].
   - The keys used in get_key_values must be exactly: ['User Story', 'User Stories', 'acceptance criteria', 'test cases'].
   - The User Story Description and Acceptance Criteria for the user story must match exactly the information specified in the provided document for the corresponding user story.
   - Strictly refer to the provided document at all times; do not infer or assume any missing or unclear information.
   - If any data is missing or unclear, leave the corresponding fields blank or indicate as such; do not make assumptions or insert arbitrary data.
{% endif %}

For the User Story, populate all properties and its child task nodes based on the guidance above. 
{% endblock %}

{% block autoreconfig %}
For the User Story, populate all properties and its child task nodes based on the guidance above. 
{% endblock %}


{% block node_details_interactive_reconfig %}
Propose appropriate updates to the User Story based on the user's feedback.
Refine the proposal by incorporating additional user input and update.
{% endblock %}