{# 
Mermaid Diagram Requirements - Include only when needed
Usage: {% include 'includes/mermaid_guidance.jinja2' %}
#}

** MERMAID DIAGRAM REQUIREMENTS:**
* Follow official Mermaid grammar for the chosen diagram type only
* *Never combine different diagram syntaxes in one block*
* **Do not** wrap Mermaid code in Markdown fences (```mermaid or ```)
* **No HTML** (<br>, <div>, etc.). For line breaks inside labels use the literal string \n
* **No Markdown** formatting (**bold**, *italic*, etc.) inside the diagram text
* Keep all brackets [], braces {}, and parentheses (), plus quotes " " / ' ', perfectly balanced
* Use plain ASCII; avoid characters that are not part of Mermaid syntax
* **Class assignments**: Use identifiers without spaces: `class MyContainer container` not `class My Container container`
* **Standard quotes only**: Use `Node["text"]` not `Node[\"text\"]` - avoid unnecessary escaping

** DIAGRAM-SPECIFIC SYNTAX:**
* **Flowcharts/Graphs**: Use `-->` for arrows (double dash), not `->` or `--->`
* **Sequence Diagrams**: Use `->>` for messages, not `-->`
* **Class Diagrams**: Use proper relationship notation like `--|>`, `*--`, etc.
* **State Diagrams**: Use `-->` for transitions between states, NEVER `<--` (reverse arrows)
* **ER Diagrams**: Use proper relationship symbols like `||--o{`, `}o--||`

**CRITICAL MERMAID SYNTAX REQUIREMENTS:**

1. **Node Definition Syntax**: Always wrap multi-line node content in double quotes:
    - ✅ CORRECT: `TM["Team Members\n[Person]\nCreate and manage tasks\nCollaborate with team"]`
    - ❌ INCORRECT: `TM[Team Members\n[Person]\nCreate and manage tasks\nCollaborate with team]`

2. **Mandatory Quoting Rules**:
    - Use double quotes around ALL node content that contains:
        - Line breaks (\n)
        - Special characters
        - Multiple lines of text
        - Brackets within the content

3. **Proper Node Format**:
    ```
    NodeID["Node Title\n[Node Type]\nDescription line 1\nDescription line 2"]
    ```

4. **Relationship Labels**: Also quote relationship labels with special characters:
    - ✅ CORRECT: `-->|"Creates and manages\ntasks via UI"|`
    - ❌ INCORRECT: `-->|Creates and manages\ntasks via UI|`

5. **Example of Proper Syntax**:
    ```mermaid
    graph TB
        TM["Team Members\n[Person]\nCreate and manage tasks\nCollaborate with team"]
        PM["Project Managers\n[Person]\nOverview and reporting\nTeam management"]
        
        TM -->|"Creates and manages\ntasks via UI"| System
    ```

**VALIDATION CHECKLIST**:
Before generating any Mermaid diagram, ensure:
- [ ] All multi-line node definitions are wrapped in double quotes
- [ ] All relationship labels with line breaks are quoted
- [ ] No unescaped special characters in node content
- [ ] Proper escaping of quotes within quoted content (use single quotes inside double quotes)
