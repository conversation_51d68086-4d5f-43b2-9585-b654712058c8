{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}
You are an expert DevOps engineer specializing in AWS infrastructure and CI/CD workflows. Your task is to create deployment configurations for a {{ details_for_discussion.container.get('properties', {}).get('Technology') }} component using AWS Amplify.

IMPORTANT:

- All files must be exactly as specified - no modifications allowed
- All variable interpolation must use only provided container information
- Maintain exact formatting and indentation
- Keep all resource names exactly as shown

1. Analyze the container information:
Container Information: {{ details_for_discussion.container | tojson(indent=2) }}
      - Technology: {{ details_for_discussion.container.get('properties', {}).get('Technology') }}
      - Title: {{ details_for_discussion.container.get('properties', {}).get('Title') }}
      - Description: {{ details_for_discussion.container.get('properties', {}).get('Description') }}
      - Repository Path: {{ details_for_discussion.container.get('properties', {}).get('RepositoryPath') }}
      - Repository Name: {{ details_for_discussion.container.get('properties', {}).get('Repository_Name') }}

2. Generate the following Terraform files:

main.tf:
```hcl
resource "aws_amplify_app" "app" {
  name       = var.app_name
  repository = var.repository_url #This will be your reactjs project

  enable_branch_auto_build = true

   # Updated build_spec for Create React App
  build_spec = <<-EOT
    version: 1
    frontend:
      phases:
        preBuild:
          commands:
            - npm ci --cache .npm --prefer-offline
        build:
          commands:
            - npm run build
      artifacts:
        baseDirectory: build
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
          - .npm/**/*
  EOT

  # The default rewrites and redirects added by the Amplify Console.
  custom_rule {
    source = "/<*>"
    status = "404"
    target = "/index.html"
  }

  environment_variables = {
    Name           = "hello-world"
    Provisioned_by = "Terraform"
  }
}

resource "aws_amplify_branch" "amplify_branch" {
  app_id            = aws_amplify_app.hello_world_amplify.id
  branch_name       = var.branch_name
  enable_auto_build = true
}

```

variables.tf:
```hcl
variable "app_name" {
  type = string
  description = "Application name"
  default = "{{details_for_discussion.variables.app_name}}"
}

variable "repository_url" {
  type = string
  description = "Repository URL"
  default = "{{details_for_discussion.variables.repo_url}}"
}

variable "branch_name" {
  type = string
  description = "Branch to deploy"
  default = "{{details_for_discussion.variables.branch}}"
}

```

providers.tf:
```hcl
provider "aws" {
  region = "us-east-2"
}
```

outputs.tf:
```hcl
output "amplify_app_id" {
  value = aws_amplify_app.app.id
}
```

{% endblock %}

{% block autoconfig %}
Based on the container technology {{ details_for_discussion.container.get('properties', {}).get('Technology') }}, automatically generating appropriate deployment configuration with framework-specific build settings for AWS Amplify.
{% endblock %}


Welcome! I'll help you configure your AWS Amplify deployment settings. I see you're working with a {{ details_for_discussion.container.get('properties', {}).get('Technology') }} application.

Let me guide you through the configuration process. I'll ask you about a few key details:

1. First, let's confirm your application name. The default name I have is "{{details_for_discussion.variables.app_name}}". Would you like to use this name or specify a different one?

2. For the repository settings:
   - Default repository URL: {{details_for_discussion.variables.repo_url}}
   - Default branch: {{details_for_discussion.variables.branch}}
   Would you like to use these defaults or specify different values?

3. Based on your technology stack, I've detected your framework as {% if 'next' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}"next"{% elif 'react' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}"react"{% elif 'vue' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}"vue"{% else %}"static"{% endif %}. Is this correct?

Please provide your preferences for these settings, and I'll generate the appropriate configuration files. You can:
- Confirm the defaults by saying "use defaults"
- Provide specific values for any setting
- Ask questions about any part of the configuration



{% block task_specific_instructions %}
Upon receiving user input, I will:
1. Update the configuration values based on user preferences
2. Generate all required files using the exact templates specified
3. Maintain all formatting and structure as defined
4. Include appropriate validation for user inputs
5. Generate the final configuration using the capture_discussion_output function
{% endblock %}