{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}
{% if config_state != "configured" %}
{% if project_knowledge | default(false) %}
   - Try out these for `search_terms` and get_key_values `keys` initially ['Epic', 'ID', 'User Story', 'acceptance criteria', 'test cases'] so we can first check if the document already has the epics mentioned.
   - The Epic's Title (has to be exactly as in the document) and count (number of epics) has to match exactly as in the document.
   - You can create as many epics (No limitation with creating new child nodes), Do not miss out on any of the information/epic it has to be exact with the document information. 
   - Refer strictly to the provided document (If information is not available you can create about 4-7 epics based of the Document infomation ). 
{% else %}
   - Provide a clear title and description and create a set of appropriate epics(about 5-10 epics). These set of epics should represent the complete system features and major functional areas. 
{% endif %}

{% else %}
You are a Project Manager reviewing a RequirementRoot node for potential reconfiguration.
Also check for current project node and original project node changes and suggest. (Mainly Look for properties like Title, scope, description etc.).

Current Configuration Context:
1. Existing Node State:
   - Current configuration: {{ details_for_discussion.get('original_node') | tojson(indent=2) }}
   - Configuration state: {{ details_for_discussion.get('config_state') }}

2. Original Project and Requirements Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   {% if bg_info.get('requirement_context') %}
   - Requirements:
     {% for epic_detail in bg_info.get('requirement_context', []) %}
     Epic: {{ epic_detail.epic.properties.Title }}
     Description: {{ epic_detail.epic.properties.Description }}
     User Stories:
     {% for story in epic_detail.user_stories %}
     - {{ story.properties.Title }}
     {% endfor %}
     {% endfor %}
   {% endif %}

3. Current Project and Requirements Context:
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}
   {% if new_bg.get('requirement_context') %}
   - Requirements:
     {% for epic_detail in new_bg.get('requirement_context', []) %}
     Epic: {{ epic_detail.epic.properties.Title }}
     Description: {{ epic_detail.epic.properties.Description }}
     User Stories:
     {% for story in epic_detail.user_stories %}
     - {{ story.properties.Title }}
     {% endfor %}
     {% endfor %}
   {% endif %}

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log') | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason') }}

5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

Based on the current configuration and context:

1. Compare original and current contexts
2. Identify Thematic Shifts if any:
   - Specifically assess changes in the project's thematic focus, such as any shift from a specific niche.
3. Analyze if updates are needed based on:
   - Changes in project context
   - Changes in epics and user stories
   - New user inputs
4. If changes are needed:
   - Create new epics or modify the existing epics as needed.
   - Capture the reason for the proposed modification.
   - IMPORTANT: Always set the "changes_needed" flag to true whenever you make any modifications, including adding new child nodes, modifying existing nodes, or updating any fields.

5. If no changes needed, update the reason in the change reason
   - Explain why current configuration remains valid
   - Reference specific requirements that support this
6. propose the new changes if needed. 
{% endif %}

Epic Creation Guidelines: 
1. Epic Title Structure: [Primary Feature / Functionality] in few words - include the key sub-functionalities with regard to the main feature that is being developed - keep it elaborate in a sentence form. Epic titles should convey a high-level goal or objective, rather than specific tasks or phases.
2. Epic Descriptions: - High-level overview of the capability - Key functionality included - Main objectives and goals 
3. Epic Priority: - Assign priority as "High","Medium" or "Low" after assessing the criticality of the requirement.

Change Needed: 
    - Set to True if changes are required.

Change Log :
    - capture history of changes.
{% endblock %}
{% block autoconfig %}
{% if project_knowledge %}
   - The search_terms for this configuration can be: ['Epic', 'User Story', 'acceptance criteria', 'test cases'].
   - The keys for get_key_values `keys` can also be: ['Epic', 'User Story', 'acceptance criteria', 'test cases'].
   - The Epic's Title and count (number of epics) has to match exactly as in the document.
   - You can create as many epics (No limitation with creating new child nodes), Do not miss out on any of the information/epic it has to be exact with the document information. 
   - Refer strictly to the provided document (If information is not available you can create about 4-7 epics based of the information). 
{% else %}
   - Configure Requirement Root that captures all the high level functionalities of this project as epics. Ensure all attributes are filled with relevant and detailed information.
{% endif %}
{% endblock %}

{% block node_details_interactive_reconfig %}
Follow the above guidelines and propose the required changes based on your analysis.
Take feedback from the user on the proposal and consolidate. Make sure to capture all the changes in new child nodes or modified child nodes if the existing nodes do not include them
{% endblock %}
{% block auto_reconfig %}
Create updated Requirement Root based on the above guidelines. Make sure to capture all the changes in new child nodes or modified child nodes if the existing nodes do not include them
{% endblock %}

{% block information_about_task %}
Current node: {{ details_for_discussion.get('current_node') | tojson(indent=2) }}
Parent node: {{ details_for_discussion.get('root_node') | tojson(indent=2) }}
Configuration state: {{ details_for_discussion.get('config_state') }}
{% endblock %}
{% block node_details_interactive_reconfig_update_specifications %}
RequirementRoot configuration updates based on your analysis of the current state, context, and modification history.
{% endblock %}