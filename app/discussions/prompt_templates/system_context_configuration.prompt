{% extends "base_discussion.prompt" %}

You are an expert software architect.

{% block task_description_common_preface %}


{% if config_state != "configured" %}
As an expert software architect, configure the System Context based on the C4 model:

{% else %}

You are an expert system architect reviewing the System Context for potential reconfiguration.

1. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   - Requirements context:
     {% set req_context = bg_info.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. Current Context (Changes to Review):

   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}

   - Updated requirements context:
     {% set new_req_context = new_bg.get('requirements_context', {}) %}

   - Updated architecture requirements context:
     - Functional Requirements:
       {% for req in new_req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in new_req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}


Analysis Instructions:

1. Impact Analysis:
   - Compare original and new project context
   - Compare original and new functional/non-functional requirements
   
2. Container Architecture Analysis:
   - Review existing containers against new requirements
   - Evaluate container responsibilities and boundaries
   - Analyze interface adequacy for new requirements

4. Required Changes:
   - Modify Existing system context, add or modify or remove any containers,users and external_systems, interfaces as needed.
   - Follow the below guidelines as a reference while modifying the existing system_context.
   - IMPORTANT: Set the "changes_needed" flag to true if modifications are done else set to false.

{% endif %}

GENERIC GUIDELINES:

1. Review the Project Details, Functional and Non-Functional requirements and ensure they are addressed in the System Context:
   - Project Context: {{ details_for_discussion.get('project_details') | tojson(indent=2) }}
   - Requirements Context:
     - Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. Provide detailed description of the system's purpose and main functionalities.

3. Identify and describe the users of the system.

4. Identify and create external systems as containers - that interact with this system.
    For each external system identified:
   - Create a corresponding container representing the external system - as child node 
   - Set ContainerType as "external"
   - Provide a suitable name
   - Describe the external system's purpose and role - in description
   - Leave other properties as NA.

5. Container Creation:
   - Analyze system context responsibilities and create containers as child nodes considering:
      Architecture strategy and
      Fucntional and Non-Fucntional requirements
   - IMPORTANT make sure to create one or more Containers
   - For new Containers:
     * Assign IDs starting with 'NEW-ARCH-1', incrementing for each new Container
     * Example: NEW-ARCH-1, NEW-ARCH-2, etc.
   - Container Properties: Each Container must have:
     - Title: Clear, descriptive name
     - Description: Detailed purpose and responsibilities
     - Type: Container
    
6. Define Interfaces as relationships between Containers. Interfaces are software interfaces that the two Containers use to communicate with each other, like APIs, routes, message bus, etc:
    - Ensure appropriate interfaces are defined between the containers within this system context - an Interface should be defined as a new relationship between them.
    - For each interface, determine and provide:
     * Appropriate interface type based on:
       - Communication patterns required
       - Performance requirements
       - Reliability needs
       - Integration constraints
     * Clear descriptive name 
        -- Examples of good interface names
        * "SMS notification Interface for Weather Alerts"
        * "API for accessing User settings from database"
        * "Retrieve weather data from backend"
     * Detailed description

7. Generate a C4 System Context diagram using Mermaid syntax. 
   - C4 System Context Diagram Example - use this for references purpose only - generate diagrams based on the details of the users, external system identified for the project appropriately.

IMPORTANT
The diagram syntax should be provided as raw Mermaid code without the keyword "mermaid" at the beginning and without any surrounding quotes. Start directly with 'graph TB'.
'
{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}

graph TB
   %% Users/Actors
   subgraph Users[System Users]
       Customer["Online Customer<br/>[Person]<br/><i>A registered customer<br/>shopping online</i>"]
       CSRep["Customer Service Rep<br/>[Person]<br/><i>Handles customer<br/>inquiries & issues</i>"]
       Admin["System Administrator<br/>[Person]<br/><i>Manages system<br/>configuration & monitoring</i>"]
   end

   %% Main System
   subgraph ECommerceSystem[E-Commerce System]
       System["E-Commerce Platform<br/>[System]<br/><i>Provides online shopping,<br/>order management, and<br/>customer service capabilities</i>"]
   end

   %% External Systems
   subgraph ExternalSystems[External Systems]
       Payment["Payment Gateway<br/>[System_Ext]<br/><i>Processes credit card<br/>and digital payments</i>"]
       Shipping["Shipping Provider<br/>[System_Ext]<br/><i>Handles order fulfillment<br/>and delivery tracking</i>"]
       Email["Email Service<br/>[System_Ext]<br/><i>Sends notifications<br/>and updates</i>"]
       Analytics["Analytics Platform<br/>[System_Ext]<br/><i>Tracks user behavior<br/>and sales metrics</i>"]
   end

   %% Relationships with detailed descriptions
   Customer -->|"Browses products<br/>Places orders<br/>HTTPS"| System
   CSRep -->|"Manages orders<br/>Handles returns<br/>HTTPS"| System
   Admin -->|"Configures system<br/>Monitors performance<br/>HTTPS"| System
   
   System -->|"Processes payments<br/>REST/HTTPS"| Payment
   System -->|"Creates shipping labels<br/>Tracks deliveries<br/>REST/HTTPS"| Shipping
   System -->|"Sends order confirmations<br/>Marketing emails<br/>SMTP"| Email
   System -->|"Reports user activity<br/>Sales data<br/>REST/HTTPS"| Analytics

   %% Styling
   classDef system fill:#1168bd,stroke:#0b4884,color:#ffffff
   classDef person fill:#08427b,stroke:#052e56,color:#ffffff
   classDef external fill:#666666,stroke:#0b4884,color:#ffffff
   classDef boundary fill:none,stroke:#cccccc,stroke-dasharray:4

   class System system
   class Customer,CSRep,Admin person
   class Payment,Shipping,Email,Analytics external
   class Users,ExternalSystems,ECommerceSystem boundary
'
8. Generate a C4 Container diagram showing:
    - All service containers with their primary responsibilities
    - Interfaces between containers
    - External system interactions
    - User interactions
    - Data flows and protocols

    C4 Container Diagram Example - use for reference purpose only - generate diagrams only based on the Users, External Systems, Containers and their interfaces identified for this project.
'
graph TB
    %% External Users
    Users((Users))
    
    %% System Containers
    subgraph SystemBoundary[System Name]
        direction TB
        
        %% Containers
        Container1[Container 1\nDescription]
        Container2[Container 2\nDescription]
        Container3[Container 3\nDescription]
        
        %% Internal Container Relationships
        Container1 -->|"uses"| Container2
        Container2 -->|"uses"| Container3
    end
    
    %% External Systems
    ExternalSystem1[External System 1]
    ExternalSystem2[External System 2]
    
    %% External Relationships
    Users -->|"uses"| Container1
    Container3 -->|"uses"| ExternalSystem1
    Container2 -->|"uses"| ExternalSystem2
    
    %% Styling
    classDef user fill:#08427b,stroke:#052e56,color:#ffffff
    classDef container fill:#1168bd,stroke:#0b4884,color:#ffffff
    classDef external fill:#666666,stroke:#333333,color:#ffffff
    
    class Users user
    class Container1,Container2,Container3 container
    class ExternalSystem1,ExternalSystem2 external
'
In the final response make sure that the mermaid chart is in correct format without any unnecessary text in the end.


Change Needed: 
   - Set to False if changes are not required.


Change Log :
    - capture history of changes.
{% endblock %}

{% block autoconfig %}
Create a comprehensive C4 Model System Context based on the project's requirements, ensuring all functional and non-functional requirements are properly addressed in the system structure.
{% endblock %}


{% block node_details_interactive_reconfig %}
Follow the above guidelines and propose the required changes based on your analysis.
Take feedback from the user on the proposal and consolidate. Make sure to capture all the changes in new child nodes or modified child nodes if the exixting nodes do not include them
{% endblock %}
{% block auto_reconfig %}
Create updated system context based on the above guidelines. Make sure to capture all the changes in new child nodes or modified child nodes if the exixting nodes do not include them

{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
system context configuration, including its internal containers, adhering to C4 model principles. Suggest improvements based on software architecture best practices and C4 modeling guidelines.
{% endblock %}

{% block information_about_task %}
{{ super() }}
    Existing interactions: {{ details_for_discussion.get('existing_interactions') | tojson(indent=2) }}
      Existing Containers:
    {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
{{ super() }}
    Project Details :  {{ details_for_discussion.get('project_details') }} 
    Architectural Requirements: {{ details_for_discussion.get('architectural_requirements', '') }}
    Functional Requirements: {{ details_for_discussion.get('functional_requirements', '') }}
    Existing Containers: {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Other Containers in the system: 
    {{ details_for_discussion.get('other_containers') | tojson(indent=2) }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %}