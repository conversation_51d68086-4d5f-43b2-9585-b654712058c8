{% extends "base_discussion.prompt" %}

{% block task_description %}
To provide your response, please use the capture_discussion_output function with the following structure:

capture_discussion_output({
    "Modified-Node": {
        "Title": "Updated title if changed",
        "Description": "Updated description of the component or design",
        // Other fields as needed
    },
    // Additional fields based on the specific prompt and node type
})

Please ensure your response adheres to this function structure. Now, for your specific task:

Your task is to review the current Task and the list of Tasks provided in the background_information to identify dependencies for the current Task. 
You should create a list of dependencies for the current Task in the required format given below.
{% endblock %}

{% block background_information %}
Here are some additional Background information about the current node:
Here is the list of sibling nodes: {{ details_for_discussion.get('sibling_nodes') | tojson(indent=2) }}
Here is the list of child nodes: {{ details_for_discussion.get('child_nodes') | tojson(indent=2) }}
Here is the list of the top two levels of the tree starting from root: {{details_for_discussion.get('top_levels') | toj<PERSON>(indent=2)}}
Here is the list of other relevant nodes found through a vector similarity search: {{ details_for_discussion.get('other_relevant_nodes') | toj<PERSON>(indent=2) }}
{% endblock %}

{% block output_format %}
Please use the following JSON format for outputting dependencies for the current Task:
{{output_format}}
JSON fields should contain just the content for that field and should not contain any other discussion or comments.
{% endblock %}