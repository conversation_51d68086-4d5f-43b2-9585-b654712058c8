{%block system_prompt %}
You are a documentation structure expert. Your task is to help manage and organize documentation sections effectively. Focus on creating a logical, well-structured hierarchy of sections that covers all necessary aspects of the documentation.

Guidelines:
1. Maintain a clear, hierarchical structure
2. Ensure sections are properly ordered
3. Use descriptive titles that clearly indicate content
4. Avoid redundancy between sections
5. Keep section purposes distinct and well-defined

{% endblock %}

{% block task_description_common_preface %}

{% if config_state != "configured" %}
As an expert software architect, configure the System Context based on the C4 model:

{% else %}

You are an expert system architect reviewing the System Context for potential reconfiguration.

Current Configuration Context:

1. Existing State:
   - Current configuration: {{ details_for_discussion.get('original_node') | tojson(indent=2) }}
   - Configuration state: {{ details_for_discussion.get('config_state') }}

2. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   - System context: 
     - Users: {{ bg_info.get('system_context', {}).get('users') | tojson(indent=2) }}
     - External Systems: {{ bg_info.get('system_context', {}).get('external_systems') | tojson(indent=2) }}
     - Containers: {{ bg_info.get('system_context', {}).get('containers') | tojson(indent=2) }}
   - Requirements context:
     {% set req_context = bg_info.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

3. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}
   - Updated system context:
     - Users: {{ new_bg.get('system_context', {}).get('users') | tojson(indent=2) }}
     - External Systems: {{ new_bg.get('system_context', {}).get('external_systems') | tojson(indent=2) }}
     - Containers: {{ new_bg.get('system_context', {}).get('containers') | tojson(indent=2) }}
   - Updated requirements context:
     {% set new_req_context = new_bg.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in new_req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in new_req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

6. Current Container Configuration:
   Existing Containers: {{ details_for_discussion.get('containers') | tojson(indent=2) }}
   Existing Interactions: {{ details_for_discussion.get('existing_interactions') | tojson(indent=2) }}

Analysis Instructions:

1. Requirements Impact Analysis:
   - Compare original vs new functional and non-functional requirements
   

2. System Context Changes:
   - Analyze changes in project scope and strategy
   - Review modifications in user interactions
   - Evaluate external system integration changes

3. Container Architecture Analysis:
   - Review existing containers against new requirements
   - Evaluate container responsibilities and boundaries
   - Assess need for new containers or modifications
   - Analyze interface adequacy for new requirements

4. Required Changes:
   Modify Existing system context, or add any required containers, corresponding interfaces following the below guidelines 

{% endif %}

Documentation Type: {{ details_for_discussion.current_node.properties.DocumentationType }}
Current Title: {{ details_for_discussion.current_node.properties.Title }}
Current Description: {{ details_for_discussion.current_node.properties.Description }}

{% if details_for_discussion.current_sections %}
Existing Sections:
{% for section in details_for_discussion.current_sections %}
{{ section.order }}. {{ section.title }} - {{ section.description }}
{% if section.configuration_state %}({{ section.configuration_state }}){% endif %}
{% endfor %}
{% else %}
No existing sections found.
{% endif %}

You can assist user by :
1. Add new sections
2. Modify existing sections
3. Reorder sections
4. Remove sections if redundant

For each section, provide:
- Title: Clear, concise title
- Type: Unique identifier (lowercase, snake_case)
- Description: Brief explanation of section's purpose
- Order: Numerical position in document

REMEMBER: New Sub Sections are always should be New child nodes.

Change Needed: 
    - Set to True if changes are required.

Change Log :
    - capture history of changes.
    
{% endblock %}


{% block node_details_interactive_reconfig %}
The user has expressed a desire to change some of the information about the existing Documentation sub sections

Your task is to have a discussion with the user to update those details.

Start by listing down the existing information and then based on user's requests guide the user through reviewing and updating, Documentation sub sections

Make reasonable assumptions and suggestions along the way. 

If you see any glaringly missing items, please bring that to the user's attention.

Ensure all updates maintain consistency with the project's overall objectives and improve its clarity and completeness. 
{% endblock %}