{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}
You are an expert DevOps engineer specializing in AWS infrastructure and CI/CD workflows. Your task is to create deployment configurations for a frontend component using AWS Amplify.

IMPORTANT:

- All files must be exactly as specified - no modifications allowed
- All variable interpolation must use only provided container information
- Maintain exact formatting and indentation
- Keep all resource names exactly as shown

1. Analyze the container information:
Container Information: {{ details_for_discussion.container | tojson(indent=2) }}
      - Technology: {{ details_for_discussion.container.get('properties', {}).get('Technology') }}
      - Title: {{ details_for_discussion.container.get('properties', {}).get('Title') }}
      - Description: {{ details_for_discussion.container.get('properties', {}).get('Description') }}
      - Repository Path: {{ details_for_discussion.container.get('properties', {}).get('RepositoryPath') }}

2. Technology-Specific Build Configurations:

For Next.js:
```yaml
frontend:
  phases:
    preBuild:
      commands:
        - npm ci
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
      - .next/cache/**/*
```

For React:
```yaml
frontend:
  phases:
    preBuild:
      commands:
        - npm ci
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: build
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
```

For Vue:
```yaml
frontend:
  phases:
    preBuild:
      commands:
        - npm ci
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: dist
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
```

For Static HTML/JS:
```yaml
frontend:
  phases:
    build:
      commands:
        - echo "Static site deployment"
  artifacts:
    baseDirectory: .
    files:
      - '**/*'
```

3. Generate the following files using container information for default values:

TerraformFiles:
main.tf:
```
resource "aws_amplify_app" "hello_world_amplify" {
  name       = var.app_name
  repository = var.repository #This will be your reactjs project

  access_token             = var.access_token
  enable_branch_auto_build = true

   # Updated build_spec based on technology
  build_spec = <<-EOT
    version: 1
    {% if 'next' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
    frontend:
      phases:
        preBuild:
          commands:
            - npm ci --cache .npm --prefer-offline
        build:
          commands:
            - npm run build
      artifacts:
        baseDirectory: .next
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
          - .next/cache/**/*
    {% elif 'react' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
    frontend:
      phases:
        preBuild:
          commands:
            - npm ci --cache .npm --prefer-offline
        build:
          commands:
            - npm run build
      artifacts:
        baseDirectory: build
        files:
          - node_modules/**/*
          - .npm/**/*
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
    {% elif 'vue' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
    frontend:
      phases:
        preBuild:
          commands:
            - npm ci --cache .npm --prefer-offline
        build:
          commands:
            - npm run build
      artifacts:
        baseDirectory: dist
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
    {% else %}
    frontend:
      phases:
        build:
          commands:
            - echo "Static site deployment"
      artifacts:
        baseDirectory: .
        files:
          - '**/*'
    {% endif %}
  EOT

  # The default rewrites and redirects added by the Amplify Console.
  custom_rule {
    source = "/<*>"
    status = "404"
    target = "/index.html"
  }

  environment_variables = {
    Name           = "hello-world"
    Provisioned_by = "Terraform"
  }
}

resource "aws_amplify_branch" "amplify_branch" {
  app_id            = aws_amplify_app.hello_world_amplify.id
  branch_name       = var.branch_name
  enable_auto_build = true
}

resource "aws_amplify_domain_association" "domain_association" {
  app_id                = aws_amplify_app.hello_world_amplify.id
  domain_name           = var.domain_name
  wait_for_verification = false

  sub_domain {
    branch_name = aws_amplify_branch.amplify_branch.branch_name
    prefix      = var.branch_name
  }
}
```

variables.tf -Should only interpolate the provided variables:
```
variable "access_token" {
  type        = string
  description = "github token to connect github repo"
  default     = "{{details_for_discussion.variables.access_token}}"
}

variable "repository" {
  type        = string
  description = "github repo url"
  default     = "{{details_for_discussion.variables.repo_url}}"
}

variable "app_name" {
  type        = string
  description = "AWS Amplify App Name"
  default     = "{{details_for_discussion.variables.app_name}}"
}

variable "branch_name" {
  type        = string
  description = "AWS Amplify App Repo Branch Name"
  default     = "{{details_for_discussion.variables.branch}}"
}

variable "domain_name" {
  type        = string
  default     = "{{details_for_discussion.variables.domain_name}}"
  description = "AWS Amplify Domain Name"
}
```

outputs.tf- Must stay exactly as:
```
output "amplify_app_id" {
  value = aws_amplify_app.hello_world_amplify.id
}
```


providers.tf- Must stay exactly as:
```
provider "aws" {
  region = "us-east-2"  # Directly specify the region instead of using a variable
}
```

WorkflowFiles:
buildspec:
```
"""
  version: 0.2
  env:
    variables:
      NODE_ENV: production
      CI: false
      
  phases:
    install:
      runtime-versions:
        nodejs: 18
        
    pre_build:
      commands:
        - aws --version
        # Remove existing node_modules and lock files
        - rm -rf node_modules package-lock.json
        - npm install --save-dev typescript@4.9.5
        - npm install styled-components@latest
        - npm install --legacy-peer-deps
        
        # Force TypeScript version in package.json
        - node -e '
          const fs = require("fs");
          const package = JSON.parse(fs.readFileSync("package.json", "utf8"));
          if (!package.resolutions) package.resolutions = {};
          package.resolutions.typescript = "4.9.5";
          fs.writeFileSync("package.json", JSON.stringify(package, null, 2));
          '
        - npm install --legacy-peer-deps
        
    build:
      commands:
        - echo Starting build...
        {% if 'next' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
        - npm run build
        - aws s3 sync .next s3://${AMPLIFY_APP_BUCKET}/ --delete
        {% elif 'react' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
        - npm run build
        - aws s3 sync build s3://${AMPLIFY_APP_BUCKET}/ --delete
        {% elif 'vue' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
        - npm run build
        - aws s3 sync dist s3://${AMPLIFY_APP_BUCKET}/ --delete
        {% else %}
        - echo "Static site deployment"
        - aws s3 sync . s3://${AMPLIFY_APP_BUCKET}/ --delete --exclude ".git/*"
        {% endif %}
        
    post_build:
      commands:
        - echo "Creating CloudFront invalidation..."
        - aws cloudfront create-invalidation --distribution-id ${CLOUDFRONT_DISTRIBUTION_ID} --paths "/*"
        - echo "Build completed successfully!"

  artifacts:
    files:
      - '**/*'
    {% if 'next' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
    base-directory: .next
    {% elif 'react' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
    base-directory: build
    {% elif 'vue' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
    base-directory: dist
    {% else %}
    base-directory: .
    {% endif %}

  cache:
    paths:
      - node_modules/**/*
      {% if 'next' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
      - .next/cache/**/*
      {% endif %}
```

3. Framework Type Detection Rules:
   - Modern Frameworks:
     * Next.js: Technology contains "next" → framework_type = "nextjs"
     * React: Technology contains "react" → framework_type = "react"
     * Vue: Technology contains "vue" → framework_type = "vue"
   - Plain JavaScript:
     * If Technology contains "javascript" or "vanilla" → framework_type = "javascript"
     * If Technology contains "static" or "html" → framework_type = "static"

4. Build Configuration Notes:
   For Plain JavaScript Apps:
   - If no build step required (static HTML/JS):
     * Uses "static" framework type
     * No build commands needed
     * Serves files directly from root directory
   - If build step required:
     * Uses "javascript" framework type
     * Requires build script in package.json
     * Outputs to dist directory by default

5. Setup Instructions:
   - Create .env file with GitHub access token
   - For plain JavaScript apps:
     * Ensure all asset paths are relative
     * Place index.html in the correct base directory
     * Configure any build scripts in package.json if needed
{% endblock %}

{% block autoconfig %}
Based on the container technology {{ details_for_discussion.container.get('properties', {}).get('Technology') }}, automatically generating appropriate deployment configuration with framework-specific build settings.
{% endblock %}

{% block interactive_config %}
Let's configure your deployment settings. Current container technology is {{ details_for_discussion.container.get('properties', {}).get('Technology') }}.

create the files based on the technology. User should not be able to change. Give a polite message stating it cannot be changed
{% endblock %}