{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}
You are an expert DevOps engineer specializing in AWS infrastructure and CI/CD workflows. Your task is to create deployment configurations for a backend service using AWS ECS/Fargate.

IMPORTANT:

- All files must be exactly as specified - no modifications allowed
- All variable interpolation must use only provided container information
- Maintain exact formatting and indentation
- Keep all resource names exactly as shown

1. Analyze the container information:
Container Information: {{ details_for_discussion.container | tojson(indent=2) }}
      - Technology: {{ details_for_discussion.container.get('properties', {}).get('Technology') }}
      - Title: {{ details_for_discussion.container.get('properties', {}).get('Title') }}
      - Description: {{ details_for_discussion.container.get('properties', {}).get('Description') }}
      - Repository Path: {{ details_for_discussion.container.get('properties', {}).get('RepositoryPath') }}

2. Technology-Specific Build Configurations:

For Node.js:
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

For Python:
```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["python", "app.py"]
```

For Java Spring:
```dockerfile
FROM eclipse-temurin:17-jdk-alpine
WORKDIR /app
COPY .mvn/ .mvn
COPY mvnw pom.xml ./
RUN ./mvnw dependency:go-offline
COPY src ./src
RUN ./mvnw package -DskipTests
EXPOSE 8080
CMD ["java", "-jar", "target/*.jar"]
```

For Go:
```dockerfile
FROM golang:1.20-alpine
WORKDIR /app
COPY go.* ./
RUN go mod download
COPY . .
RUN CGO_ENABLED=0 go build -o /app/server
EXPOSE 8080
CMD ["/app/server"]
```

3. Generate the following files using container information for default values:

TerraformFiles:
main.tf:
```
resource "aws_ecs_cluster" "main" {
  name = var.app_name
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

resource "aws_ecs_task_definition" "app" {
  family                   = var.app_name
  network_mode            = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                     = var.cpu
  memory                  = var.memory

  container_definitions = jsonencode([
    {
      name      = var.app_name
      image     = "${var.ecr_repository_url}:${var.image_tag}"
      essential = true
      portMappings = [
        {
          containerPort = var.container_port
          protocol      = "tcp"
        }
      ]
      environment = [
        {
          name  = "NODE_ENV"
          value = var.environment
        }
      ]
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-group         = "/ecs/${var.app_name}"
          awslogs-region        = var.aws_region
          awslogs-stream-prefix = "ecs"
        }
      }
    }
  ])

  execution_role_arn = aws_iam_role.ecs_task_execution_role.arn
  task_role_arn      = aws_iam_role.ecs_task_role.arn
}

resource "aws_ecs_service" "app" {
  name            = var.app_name
  cluster         = aws_ecs_cluster.main.id
  task_definition = aws_ecs_task_definition.app.arn
  desired_count   = var.desired_count
  launch_type     = "FARGATE"

  network_configuration {
    security_groups = [aws_security_group.ecs_tasks.id]
    subnets        = var.private_subnet_ids
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.app.arn
    container_name   = var.app_name
    container_port   = var.container_port
  }
}

resource "aws_security_group" "ecs_tasks" {
  name        = "${var.app_name}-ecs-tasks"
  description = "Allow inbound traffic from ALB"
  vpc_id      = var.vpc_id

  ingress {
    from_port       = var.container_port
    to_port         = var.container_port
    protocol        = "tcp"
    security_groups = [aws_security_group.alb.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
```

variables.tf:
```
variable "app_name" {
  type        = string
  description = "Application name"
  default     = "{{details_for_discussion.variables.app_name}}"
}

variable "aws_region" {
  type        = string
  description = "AWS Region"
  default     = "{{details_for_discussion.variables.region}}"
}

variable "environment" {
  type        = string
  description = "Environment (e.g., production, staging)"
  default     = "{{details_for_discussion.variables.environment}}"
}

variable "cpu" {
  type        = number
  description = "CPU units (1024 = 1 vCPU)"
  default     = 256
}

variable "memory" {
  type        = number
  description = "Memory in MiB"
  default     = 512
}

variable "container_port" {
  type        = number
  description = "Container port"
  default     = {% if 'node' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}3000{% elif 'python' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}8000{% else %}8080{% endif %}
}

variable "desired_count" {
  type        = number
  description = "Desired number of containers"
  default     = 2
}

variable "ecr_repository_url" {
  type        = string
  description = "ECR repository URL"
}

variable "image_tag" {
  type        = string
  description = "Container image tag"
  default     = "latest"
}
```

outputs.tf:
```
output "service_url" {
  value = aws_lb.app.dns_name
}

output "ecr_repository_url" {
  value = aws_ecr_repository.app.repository_url
}
```

providers.tf:
```
provider "aws" {
  region = var.aws_region
}
```

WorkflowFiles:
buildspec:
```
version: 0.2
  
  phases:
    install:
      runtime-versions:
        {% if 'node' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
        nodejs: 18
        {% elif 'python' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
        python: 3.11
        {% elif 'java' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
        java: corretto17
        {% elif 'go' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
        golang: 1.20
        {% endif %}
        
    pre_build:
      commands:
        - echo Logging in to Amazon ECR...
        - aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com
        - REPOSITORY_URI=${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY}
        - IMAGE_TAG=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
        
    build:
      commands:
        - echo Build started on `date`
        - echo Building the Docker image...
        - docker build -t $REPOSITORY_URI:$IMAGE_TAG .
        - docker tag $REPOSITORY_URI:$IMAGE_TAG $REPOSITORY_URI:latest
        
    post_build:
      commands:
        - echo Build completed on `date`
        - echo Pushing the Docker images...
        - docker push $REPOSITORY_URI:$IMAGE_TAG
        - docker push $REPOSITORY_URI:latest
        - echo Writing image definitions file...
        - printf '{"ImageURI":"%s"}' $REPOSITORY_URI:$IMAGE_TAG > imageDefinitions.json
        - cat imageDefinitions.json
        
  artifacts:
    files:
      - imageDefinitions.json
      - appspec.yaml
      - taskdef.json
      
  cache:
    paths:
      {% if 'node' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
      - node_modules/**/*
      {% elif 'python' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
      - /root/.cache/pip
      {% elif 'java' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
      - /root/.m2/**/*
      {% endif %}
```

3. Technology Detection Rules:
   - Node.js: Technology contains "node", "express", "nest"
   - Python: Technology contains "python", "django", "flask"
   - Java: Technology contains "java", "spring"
   - Go: Technology contains "go", "golang"

4. Configuration Notes:
   - Memory and CPU allocations:
     * Default: 0.25 vCPU (256), 512MB RAM
     * Medium: 0.5 vCPU (512), 1024MB RAM
     * Large: 1.0 vCPU (1024), 2048MB RAM
   - Auto-scaling based on:
     * CPU utilization > 70%
     * Memory utilization > 80%
     * Request count per target > 1000

5. Setup Instructions:
   - Configure AWS credentials
   - Set up ECR repository
   - Configure VPC and subnets
   - Set up CloudWatch logs
   - Configure environment variables
{% endblock %}

{% block autoconfig %}
Based on the container technology {{ details_for_discussion.container.get('properties', {}).get('Technology') }}, automatically generating appropriate deployment configuration with framework-specific build settings.
{% endblock %}

{% block interactive_config %}
Let's configure your deployment settings. Current container technology is {{ details_for_discussion.container.get('properties', {}).get('Technology') }}.

create the files based on the technology. User should not be able to change. Give a polite message stating it cannot be changed
{% endblock %}