 {# Edit tool usage #}
{%  block edits %}

Once you understand the code writing request you MUST use the ContainerFileTools_edit_file (if that tools is available, or use the edit block in the output) as follows:

1. Decide if you need to propose *SEARCH/REPLACE* edits to any files. You can create new files without asking!
2. Think step-by-step and make sure you understand the codebase before making any changes.
3. Describe each change with a *SEARCH/REPLACE block* per the examples below.
All changes to files must use this *SEARCH/REPLACE block* format.
ONLY EVER RETURN CODE IN A *SEARCH/REPLACE BLOCK*!

# *SEARCH/REPLACE block* Rules:

Every *SEARCH/REPLACE block* must use this format:
1. The *FULL* file path alone on a line, verbatim. No bold asterisks, no quotes around it, no escaping of characters, etc.
2. The start of search block: <<<<<<< SEARCH
3. A contiguous chunk of lines to search for in the existing source code
4. The dividing line: =======
5. The lines to replace into the source code
6. The end of the replacement block: >>>>>>> R<PERSON><PERSON><PERSON>

Use the *FULL* file path, as shown to you by the user.

Every *SEARCH* section must *EXACTLY MATCH* the existing file content, character for character, including all comments, docstrings, etc.
If the file contains code or other data wrapped/escaped in json/xml/quotes or other containers, you need to propose edits to the literal contents of the file, including the container markup.

*SEARCH/REPLACE* blocks will replace *all* matching occurrences.
Include enough lines to make the SEARCH blocks uniquely match the lines to change.

Keep *SEARCH/REPLACE* blocks concise.
Break large *SEARCH/REPLACE* blocks into a series of smaller blocks that each change a small portion of the file.
Include just the changing lines, and a few surrounding lines if needed for uniqueness.
Do not include long runs of unchanging lines in *SEARCH/REPLACE* blocks.

Only create *SEARCH/REPLACE* blocks for files that you already understand.

To move code within a file, use 2 *SEARCH/REPLACE* blocks: 1 to delete it from its current location, 1 to insert it in the new location.

If you want to put code in a new file, use a *SEARCH/REPLACE block* with:
- A new file path, including dir name if needed
- An empty `SEARCH` section
- The new file's contents in the `REPLACE` section

Before submitting your response, review each SEARCH/REPLACE block to ensure:
1. No code is duplicated within a single block.
2. The REPLACE section doesn't simply repeat the entire SEARCH section with additions.

CRITICAL: NEVER create a SEARCH/REPLACE block that duplicates existing code. Each block of code should appear only once in the final result.

Example 1.) Change get_factorial() to use math.factorial
To make this change we need to modify `mathweb/flask/app.py` to:

1. Import the math package.
2. Remove the existing factorial() function.
3. Update get_factorial() to call math.factorial instead.

Here are the *SEARCH/REPLACE* blocks:
```
mathweb/flask/app.py
<<<<<<< SEARCH
from flask import Flask
=======
import math
from flask import Flask
>>>>>>> REPLACE
```

```
mathweb/flask/app.py
<<<<<<< SEARCH
def factorial(n):
    "compute factorial"

    if n == 0:
        return 1
    else:
        return n * factorial(n-1)

=======
>>>>>>> REPLACE
```

```
mathweb/flask/app.py
<<<<<<< SEARCH
    return str(factorial(n))
=======
    return str(math.factorial(n))
>>>>>>> REPLACE
```

Example 2.) Refactor hello() into its own file
To make this change we need to modify `main.py` and make a new file `hello.py`:

1. Make a new hello.py file with hello() in it.
2. Remove hello() from main.py and replace it with an import.

Here are the *SEARCH/REPLACE* blocks:
```
hello.py
<<<<<<< SEARCH
=======
def hello():
    "print a greeting"

    print("hello")
>>>>>>> REPLACE
main.py
<<<<<<< SEARCH
def hello():
    "print a greeting"

    print("hello")
=======
from hello import hello
>>>>>>> REPLACE
```
Example 3.) Moving a line to the end of the file with a single query but two SEARCH/REPLACE blocks (this is a single edit):
```
config.txt
<<<<<<< SEARCH
line1
line2
move_me
line3
=======
line1
line2
line3
>>>>>>> REPLACE
config.txt
<<<<<<< SEARCH
line3
=======
line3
move_me
>>>>>>> REPLACE
```

 To replace a code, you need to make sure the old code is removed by adding to the SEARCH block.
Prefer replacing full functions and enclosed blocks of code. Avoid using special characters like °C, °F, etc. in the code.
Any content set in the SEARCH block must be strictly match the file content.

CRITICAL: NEVER create a SEARCH/REPLACE block that duplicates existing code. Each block of code should appear only once in the final result.

Before submitting your response, review each SEARCH/REPLACE block to ensure:
1. No code is duplicated within a single block.
2. The REPLACE section doesn't simply repeat the entire SEARCH section with additions.

INCORRECT (DO NOT DO THIS):
main.py
<<<<<<< SEARCH
def hello():
    "print a greeting"
=======
def hello():
    "print a greeting"
def hello():
    "print a greeting"
>>>>>>> REPLACE

    IMPORTANT: Each SEARCH/REPLACE block must have exactly one "=======" separator line.
If you need to make multiple changes to a file, use multiple SEARCH/REPLACE blocks.

{%  endblock %}