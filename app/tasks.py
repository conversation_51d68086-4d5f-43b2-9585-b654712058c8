# Standard library imports
import asyncio
import json
import logging
import time

# Framework and core imports
from app.celery_app import (
    celery_app as app, 
    celery_task_id, 
    cleanup_websocket_session, 
    create_websocket_session, 
    user_context
)
from app.connection.tenant_middleware import tenant_context
from app.core.constants import FIGMA_BASE_PATH as BASE_PATH, TASKS_COLLECTION_NAME
from app.core.Settings import settings
from app.core.task_framework import Task, TaskStatus, TaskWrapper
from app.core.websocket import websocket_manager
from app.core.websocket.client import WebSocketClient

# Database and service imports
from app.connection.establish_db_connection import get_mongo_db
from app.services.notification_service import NotificationService

# Model imports
from app.models.notification_model import NotificationModel, CodeGenerationNotificationData
from app.models.uiux.figma_model import FigmaModel, UserModel, FigmaRequestModel, ProcessingStatus, FigmaSizesModel

from app.connection.tenant_middleware import set_global_user_id

# Utility imports
from app.utils.async_utils import async_to_sync
from app.utils.datetime_utils import generate_timestamp
from app.utils.figma_utils import (
    extract_file_key,
    extract_frame_data,
    fetch_frame_images_async,
    figma_access_token,
    get_figma_file_data_limited_async
)
from app.utils.task_utils import configuration_update
from app.telemetry.logger_config import get_logger
from app.classes.S3Handler import S3Handler
from llm_wrapper.core.llm_interface import LLMInterface
from app.discussions.figma.tools.work_input_discovery_tool import WorkInputDiscovery

# Agent and knowledge imports
import os
if not os.environ.get("BATCH_JOB_TRIGGER"):
    from app.agents.agent_main import configure_node
    
from app.knowledge.code_query import KnowledegeBuild
from app.knowledge.update_sync import KnowlegeUpdate
from app.utils.figma_utils import process_frame
# Third-party imports
import httpx

def generate_session_dir() -> str:
    session_root = "/tmp/kavia/code_gen"
    return session_root

# YM: The below tasks exist only for illustrative purposes
# Will be removed once we have at least one real task which
# can be triggered

class CommandManager:
    def __init__(self, task_id, ws_client: WebSocketClient):
        self.task_id = task_id
        self.ws_client = ws_client
        self.ws_client.add_message_handler(self.handle_commands)
        
    def initialize(self):
        self.ws_client.start_message_handler()
    
    def update_session (self, session_metadata):
        # get metadate with get_default and update it
        session_metadata: dict = websocket_manager.get_session_metadata(self.task_id)
        if session_metadata:
            session_metadata["status"] = "updated"
            websocket_manager.update_session_metadata(self.task_id, session_metadata)
        else:
            self.ws_client.send_message("error", {"message": "Session not found"})
            
    async def handle_commands(self, message):
        try:
            command = message.get("command")
            task_id = message.get("task_id")
            
            if command == "status_check":
                session_metadata = websocket_manager.get_session_metadata(task_id)
                
                if session_metadata:
                    self.ws_client.send_message("auto_configuration_status", session_metadata)
                try:
                    auto_configuration_update = await configuration_update(task_id)
                    if auto_configuration_update:
                        self.ws_client.send_message("auto_configuration_update", auto_configuration_update)
                except Exception as e:
                    print("Error in auto_configuration_update", e)
                    pass
        
        except Exception as e:
            print("Error in handle commands", e)
         


@app.task(bind=True)
def processing_autoconfig(self,  node_id, node_type, user_level, request, type="auto-config", tenant_id='neo4j', current_user='admin',project_id=None):
    task_id = self.request.id

    logging.info(f"[Task Start] {'='*50}")
    logging.info(f"[Task] Processing autoconfig initiated - Task ID: {task_id}")
    logging.info(f"[Task] Parameters: node_id={node_id}, node_type={node_type}, user_level={user_level}")
    logging.info(f"[Task] Type: {type}, Tenant: {tenant_id}, User: {current_user}")
    try:
        from app.services.session_tracker import get_session_tracker
        
        tracker = get_session_tracker()
        loop = asyncio.get_event_loop()
        
        # Initialize session with auto-configuration specific data
        session_result = loop.run_until_complete(tracker.initialize_session(
            task_id=str(task_id),
            tenant_id=tenant_id,
            user_id=current_user,
            project_id=project_id,
            service_type="auto-configuration",
            container_id=None,
            session_data={
                "session_name": "Auto Configuration",
                "node_id": node_id,
                "node_type": node_type,
                "user_level": user_level,
                "request_type": type
            }
        ))

        if session_result["success"]:
            logging.info(f"Session tracking initialized for auto-config task_id: {task_id}")
        else:
            logging.error(f"Failed to initialize session tracking: {session_result.get('error', 'Unknown error')}")
        
        if session_result["success"]:
            print(f"✅ Session tracking initialized for Auto-Configuration {task_id}")
        else:
            print(f"⚠️ Failed to initialize session tracking: {session_result.get('error', 'Unknown error')}")
        
    except Exception as e:
        print(f"⚠️ Error initializing session tracking: {e}")
    try:
        # Set tenant context
        logging.info(f"[Task] Setting celery task ID: {task_id}")
        celery_task_id.set(task_id)
        # Create WebSocket session
        logging.info("[Task] Creating WebSocket session")
        ws_client = create_websocket_session(task_id, metadata={
            'task_type': type,
            'tenant_id': tenant_id,
            'user_id': current_user
        })
        logging.info("[Task] WebSocket session created successfully")
        # Create command manager
        logging.info("[Task] Initializing CommandManager")
        command_manager = CommandManager(task_id, ws_client)
        command_manager.initialize()
        # Send initialization message
        logging.info("[Task] Sending initialization message")

        ws_client.send_message("initialized", {"task_id": task_id, "node_id": node_id, "node_type": node_type, "user_level": user_level, "request": request})
        logging.info(f"[Task] Setting up user context: {current_user} and tenant context: {tenant_id}")
        user_token = user_context.set(current_user)
        if current_user:
            set_global_user_id(current_user)
        token = tenant_context.set(tenant_id)
        # Task Wrapper execution
        logging.info("[Task] Starting TaskWrapper execution")
        with TaskWrapper(task_id) as task:
            logging.info("[Task] Reporting 50% progress")
            task.report_progress(50)
            logging.info("[Task] Calling configure_node")
            result = async_to_sync(configure_node(node_id, node_type, user_level, request, task_id))
            logging.info(f"[Task] Configure node result: {result}")
            logging.info("[Task] Reporting 80% progress")
            print(result)
            task.report_progress(80)
            logging.info("[Task] Task execution completed successfully")

            # End session tracking with success status
            try:
                end_result = loop.run_until_complete(tracker.end_session(str(task_id), "completed"))
                if end_result["success"]:
                    logging.info(f"Session tracking ended successfully for task_id: {task_id}")
                else:
                    logging.error(f"Failed to end session tracking: {end_result.get('error', 'Unknown error')}")
            except Exception as session_error:
                logging.error(f"Session tracking end error: {session_error}")

            return result
    except Exception as e:
        logging.error(f"[Task] Error in processing_autoconfig: {str(e)}")
        logging.exception(e)

        # End session tracking with error status
        try:
            end_result = loop.run_until_complete(tracker.end_session(str(task_id), "failed"))
            if end_result["success"]:
                logging.info(f"Session tracking ended with failure status for task_id: {task_id}")
        except Exception as session_error:
            logging.error(f"Session tracking end error: {session_error}")

    finally:
        # Cleanup
        logging.info("[Task] Starting cleanup process")
        cleanup_websocket_session(task_id)
        user_context.reset(user_token)
        tenant_context.reset(token)
        logging.info("[Task] Cleanup completed")
        logging.info(f"[Task End] {'='*50}\n")
        
@app.task
def processing(x, y):
    task_id = processing.request.id
    with TaskWrapper(task_id) as task:
        task.report_progress(50)
        result = x + y
        task.report_progress(80)
        time.sleep(30)
        return result



@app.task
def report_result(result):
    task_id = report_result.request.id
    with TaskWrapper(task_id) as task:
        raise Exception('Unfortunate error!')
    
@app.task(bind=True)
def send_notification_task(self, task_id: str, content: str = None, tenant_id: str = None, current_user: str = None):
    """Celery task for sending notifications"""
    try:
        print("started ", (self.request.id))
        # Set tenant context
        if current_user:
            set_global_user_id(current_user)
        user_token = user_context.set(current_user)
        token = tenant_context.set(tenant_id)
        db = get_mongo_db().db
        with TaskWrapper(self.request.id) as task:
            task.report_progress(20)
            
            # Get task info
            task_data = db[TASKS_COLLECTION_NAME].find_one(
                {"_id": task_id},
                projection={
                    "project_id": 1,
                    "architecture_id": 1,
                    "user_id": 1,
                    "_id": 0
                }
            )
            
            if task_data:
                task.report_progress(50)
                
                notification_data = NotificationModel(
                    receiver_id=task_data.get("user_id"),
                    type="code_generation",
                    action="code_generation",
                    data=CodeGenerationNotificationData(
                        message=f"Task ID: {task_id} - Waiting for user input to continue \n\n{content}",
                        link=f"/projects/{task_data.get('project_id')}/architecture/design?task_id={task_id}",
                        project_id=task_data.get('project_id'),
                        task_id=task_id,
                        design_id=task_data.get('architecture_id', 1) 
                    )
                )
                
                notification_service = NotificationService()
                async_to_sync(notification_service.send_notification(notification_data))
                
                task.report_progress(100)
                return True
                
    except Exception as e:
        print(f"Error in notification task: {e}")
        raise e
    finally:
        # Reset tenant context
        tenant_context.reset(token)


async def background_process_figma_file(
    project_id: str,
    figma_link: FigmaRequestModel,
    tenant_id: str,
    figma_api_key: str,
    is_new_file: bool = True,
):
    """Background task to process Figma file with frame status tracking"""
    print("Background task started")
    file_logger = get_logger(__name__)
    processed_frames = []
    completed_count = 0
    failed_count = 0
    total_frames = 0
    data = None

    try:
        figma_access_token.set(figma_api_key)
        file_key = extract_file_key(figma_link.url)
        figma_id = f"{tenant_id}-{project_id}-{file_key}"

        llm = LLMInterface(
            f"{BASE_PATH}/{project_id}/{tenant_id}/{figma_id}",
            instance_name=f"figma_discussion_{project_id}",
            user_id=tenant_id,
            project_id=project_id,
            agent_name="FigmaExtractionAgent"
        )

        work_input_discovery : WorkInputDiscovery = WorkInputDiscovery(
            callback_functions=None,
            base_path=f"{BASE_PATH}/{project_id}/{tenant_id}/{figma_id}",
            logger=file_logger,
            llm=llm

        )
        ws_client = WebSocketClient(f"figma-{project_id}", uri=settings.WEBSOCKET_URI)
        ws_client.connect()
   

        # Use async client for HTTP requests
        async with httpx.AsyncClient() as client:
            # Process Figma file data first to get total frames count
            data, sizes = await get_figma_file_data_limited_async(
                client, figma_link.url, figma_api_key, mb_limit=5
            )
            frames = extract_frame_data(data)
            total_frames = len(frames)

            # Update FigmaModel with initial frame count and status
            update_data = {
                "total_frames": total_frames,
                "completed_frames": 0,
                "failed_frames": 0,
                "sizes": FigmaSizesModel(**sizes).dict(),
                "time_updated": generate_timestamp(),
            }
            await FigmaModel.update(figma_id, update_data)

            # Process frames and track counts
            frame_ids = [frame["id"] for frame in frames]
            image_urls = await fetch_frame_images_async(client, file_key, frame_ids)
            has_errors = False

            for frame in frames:
                try:
                    # Process frame with status tracking
                    frame_model = await process_frame(frame, file_key, image_urls)

                    # Convert to format matching process_figma_file
                    # Ensure absoluteBoundingBox is properly structured
                    bounding_box = frame.get("absoluteBoundingBox", {})
                    if not bounding_box:
                        # Provide default dimensions if missing
                        bounding_box = {
                            "x": 0,
                            "y": 0,
                            "width": 800,  # default width
                            "height": 600,  # default height
                        }

                    processed_frame = {
                        "id": frame["id"],
                        "name": frame["name"],
                        "type": frame["type"],
                        "absoluteBoundingBox": bounding_box,
                        "imageUrl": (
                            frame_model.imageUrl
                            if frame_model.status == ProcessingStatus.COMPLETED
                            else None
                        ),
                        "status": frame_model.status,
                        "error_message": (
                            frame_model.error_message
                            if hasattr(frame_model, "error_message")
                            else None
                        ),
                        "time_updated": frame_model.time_updated,
                        # Add fields needed for frontend
                        "dimensions": {
                            "width": round(bounding_box.get("width", 800)),
                            "height": round(bounding_box.get("height", 600)),
                        },
                    }

                    processed_frames.append(processed_frame)

                    if frame_model.status == ProcessingStatus.COMPLETED:
                        completed_count += 1
                    elif frame_model.status == ProcessingStatus.FAILED:
                        failed_count += 1
                        has_errors = True

                    if(FigmaSizesModel(**sizes).model_dump()['size_kb'] >= 6000):
                        status = ProcessingStatus.FAILED
                    else:
                        status = ProcessingStatus.PROCESSING

                    # Update MongoDB with current counts
                    update_data = {
                        "total_frames": total_frames,
                        "completed_frames": completed_count,
                        "failed_frames": failed_count,
                        "time_updated": generate_timestamp(),
                        "sizes": FigmaSizesModel(**sizes).dict(),
                        "status": status,
                    }
                    if has_errors:
                        update_data["status"] = ProcessingStatus.PARTIALLY_COMPLETED
                    ws_client.send_message(
                        "figma_update",
                        {"figma_id": figma_id, "update_data": update_data},
                    )
                    await FigmaModel.update(figma_id, update_data)

                except Exception as e:
                    print(f"Error processing frame {frame['id']}: {str(e)}")
                    failed_count += 1
                    has_errors = True

            # Store frames data in S3 in the same format as process_figma_file
            file_data = {
                "frames": processed_frames,
                "fileKey": file_key,
                "document": data["document"],
                "sizes": sizes,
                "progress": {
                    "total": total_frames,
                    "completed": completed_count,
                    "failed": failed_count,
                },
            }
            hashes, work_item = work_input_discovery.process_figma_json(file_data)
            update_data["work_input_sh256"] = hashes
            update_data["work_input"] = work_item
            s3_handler = S3Handler(tenant_id)
            await s3_handler.update_file_async(
                f"{figma_id}.json", json.dumps(file_data)
            )

        # Final update
        final_status = (
            ProcessingStatus.COMPLETED
            if failed_count == 0
            else ProcessingStatus.PARTIALLY_COMPLETED
        )
        final_update = {
            "status": final_status,
            "error_message": (
                f"{failed_count} frames failed to process" if failed_count > 0 else None
            ),
            "time_updated": generate_timestamp(),
        }
        ws_client.send_message(
            "figma_update", {"figma_id": figma_id, "update_data": final_update}
        )
        await FigmaModel.update(figma_id, final_update)

    except Exception as e:
        print(f"Error in background task: {str(e)}")
        # Store any successfully processed frames
        if processed_frames and data:
            file_data = {
                "frames": processed_frames,
                "fileKey": file_key,
                "document": data["document"],
                "sizes": sizes,
                "progress": {
                    "total": total_frames,
                    "completed": completed_count,
                    "failed": failed_count,
                },
            }
            s3_handler = S3Handler(tenant_id)
            await s3_handler.update_file_async(
                f"{figma_id}.json", json.dumps(file_data)
            )

            # Update status to partially completed if any frames were processed
            if completed_count > 0:
                error_update = {
                    "status": ProcessingStatus.PARTIALLY_COMPLETED,
                    "error_message": str(e),
                    "time_updated": generate_timestamp(),
                }
            else:
                error_update = {
                    "status": ProcessingStatus.FAILED,
                    "error_message": str(e),
                    "time_updated": generate_timestamp(),
                }
            ws_client.send_message(
                "figma_update", {"figma_id": figma_id, "update_data": error_update}
            )
            await FigmaModel.update(figma_id, error_update)
    finally:
        ws_client.disconnect()


@app.task(bind=True)
def process_figma_in_celery(
    self,
    project_id: str,
    figma_link: dict,
    figma_api_key: str,
    is_new_file: bool = True,
    tenant_id: str = None,
    current_user: str = None,
):
    """Celery task for processing Figma file"""
    if current_user:
        set_global_user_id(current_user)

    user_token = user_context.set(current_user)
    token = tenant_context.set(tenant_id)

    try:
        # Call the background task
        figma_link = FigmaRequestModel(**figma_link)
        async_to_sync(
            background_process_figma_file(
                project_id,
                figma_link,
                tenant_id,
                figma_api_key,
                is_new_file=is_new_file,
            )
        )
    except Exception as e:
        print(f"Error in process_figma_in_celery: {str(e)}")
        return {"status": "failed", "error": str(e)}
    finally:
        user_context.reset(user_token)
        tenant_context.reset(token)


@app.task(bind=True)
def document_processing_task(self, temp_file_path, file_uuid, session_id, filename, 
                          project_id, tenant_id, user_id, user_name, original_s3_path=None, file_size=0, file_type=None, file_extension=None):
    """Celery task for document processing"""
    if user_id:
        set_global_user_id(user_id)

    user_token = user_context.set(user_id)
    token = tenant_context.set(tenant_id)
    try:
        from app.utils.prodefn.projdefn import ProjDefn
        from app.utils.prodefn.projdefn_helper import Reporter, Helpers

        # Release any existing ProjDefn instance first
        try:
            ProjDefn.releaseInstance("default")
        except Exception as e:
            print(f"Error releasing ProjDefn instance: {str(e)}")
            pass
        
        # Set up configuration
        base_path = "/tmp/doc_ingestion/dir"
        reporter = Reporter()
        configuration = {
            "base_path": base_path,
            "model": "gpt-4o-mini",
            "timeout": 60,
            "chunk_size": 64*1024,
            "cost_tracer": None,
            "reporter": reporter,
            "helpers": Helpers(base_path),
            "project_id": project_id
        }
        
        # Get ProjDefn instance
        projdefn = ProjDefn.getInstance(configuration, "default", session_id, tenant_id, project_id)
        projdefn.start()

        # Use async_to_sync to call process_document
        async_to_sync(projdefn.process_document(
            temp_file_path,
            file_uuid,
            session_id,
            filename,
            project_id,
            tenant_id,
            user_id,
            user_name,
            original_s3_path,
            file_size,
            file_type,
            file_extension
        ))
        
        return {"status": "completed", "file_uuid": file_uuid}
    except Exception as e:
        print(f"Document processing task failed: {str(e)}")
        return {"status": "failed", "file_uuid": file_uuid, "error": str(e)}
    finally:
        # Always clean up
        try:
            ProjDefn.releaseInstance("default")
        except Exception as e:
            print(f"Error releasing ProjDefn instance: {str(e)}")

        user_context.reset(user_token)
        tenant_context.reset(token)

@app.task(bind=True)
def clone(self, project_id, build_session_id, build_id, data_dir, repository, tenant_id, current_user, upstream, current_user_obj):
    user_token = user_context.set(current_user)
    token = tenant_context.set(tenant_id)
    
    if current_user:
        set_global_user_id(current_user)
    
    try:
        
        kg = KnowledegeBuild()
        async_to_sync(kg.clone(project_id, build_session_id, build_id,  current_user, data_dir, [repository], upstream, current_user_obj))

    finally:
        user_context.reset(user_token)
        tenant_context.reset(token)

@app.task(bind=True)
def ingest(self, project_id, project_name, build_session_id, build_id, data_dir, files_data, tenant_id, current_user):
    if current_user:
        set_global_user_id(current_user)
    user_token = user_context.set(current_user)
    token = tenant_context.set(tenant_id)
    try:
        kg = KnowledegeBuild()
        async_to_sync(kg.ingest(project_id, project_name, build_session_id, build_id, current_user, data_dir, files_data))
    finally:
        user_context.reset(user_token)
        tenant_context.reset(token)
        
@app.task(bind=True)
def upstream(self, project_id, build_session_id, build_id, tenant_id, current_user):
    if current_user:
        set_global_user_id(current_user)
    user_token = user_context.set(current_user)
    token = tenant_context.set(tenant_id)
    try:
        
        kg = KnowledegeBuild()
        async_to_sync(kg.upstream(project_id,build_session_id, build_id, current_user))
    finally:
        user_context.reset(user_token)
        tenant_context.reset(token)

@app.task(bind=True)
def detec_sync(self, project_id ,tenant_id=None, current_user=None):
    if current_user:
        set_global_user_id(current_user)
    logging.info(f"Starting detec_sync task for project {project_id}")
    user_token = user_context.set(current_user)
    token = tenant_context.set(tenant_id)
    try:
        kg = KnowlegeUpdate(project_id, current_user)
        result = async_to_sync(kg.detect_sync(current_user))
        logging.info(f"Sync detection result: {result}")
        return result


    except Exception as e:
        # Handle other exceptions
        return {
            "status_code": 500,
            "detail": str(e)
        }
    
    finally:
        user_context.reset(user_token)
        tenant_context.reset(token)
        
@app.task(bind=True)
def send_notification(
    self, 
    task_id: str, 
    message: str,
    status: str = "info",
    notification_type: str = "code_generation",
    notification_action: str = "code_generation",
    tenant_id: str = None, 
    current_user: str = None
):
    """Celery task for sending task notifications
    
    Args:
        task_id: ID of the related task
        message: Notification message to display
        status: Status of the notification (e.g., 'success', 'failure', 'info', 'warning')
        notification_type: Type of notification (e.g., 'code_generation', 'deployment')
        notification_action: Action associated with the notification
        tenant_id: Tenant identifier
        current_user: Current user making the request
    """
    try:
        if current_user:
            set_global_user_id(current_user)
        print("started ", (self.request.id))
        # Set tenant context
        user_token = user_context.set(current_user)
        token = tenant_context.set(tenant_id)
        db = get_mongo_db().db
        with TaskWrapper(self.request.id) as task:
            task.report_progress(20)
            
            # Get task info
            task_data = db[TASKS_COLLECTION_NAME].find_one(
                {"_id": task_id},
                projection={
                    "project_id": 1,
                    "architecture_id": 1,
                    "user_id": 1,
                    "_id": 0
                }
            )
            
            if task_data:
                task.report_progress(50)
                agent_name = task_data.get("agent_name")
                print("agent_name", agent_name)
                design_id = task_data.get("architecture_id")
                if not design_id:
                    design_id = 1
                if agent_name == "CodeMaintenance":
                    notification_data = NotificationModel(
                        receiver_id=task_data.get("user_id"),
                        type=notification_type,
                        action=notification_action,
                        data=CodeGenerationNotificationData(
                            message=f"Task ID: {task_id} - {message}",
                            link=f"/projects/{task_data.get('project_id')}/architecture/codemaintenance?task_id={task_id}",
                            project_id=task_data.get('project_id'),
                            task_id=task_id,
                            design_id=design_id
                        )
                    )
                else:
                    notification_data = NotificationModel(
                        receiver_id=task_data.get("user_id"),
                        type=notification_type,
                        action=notification_action,
                        data=CodeGenerationNotificationData(
                        message=f"Task ID: {task_id} - {message}",
                        link=f"/projects/{task_data.get('project_id')}/architecture/design?task_id={task_id}",
                        project_id=task_data.get('project_id'),
                        task_id=task_id,
                        design_id=design_id
                    )
                )
                
                notification_service = NotificationService()
                async_to_sync(notification_service.send_notification(notification_data))
                
                task.report_progress(100)
                return True
                
    except Exception as e:
        print(f"Error in notification task: {e}")
        raise e
    finally:
        # Reset tenant context
        tenant_context.reset(token)



