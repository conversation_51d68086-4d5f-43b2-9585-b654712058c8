# app/routes/announcement_route.py
from fastapi import APIRouter, HTTPException, Depends
from typing import List
from app.models.announcement_model import (
    AnnouncementDraft, 
    AnnouncementDraftCreate,
    AnnouncementPublish,
    AnnouncementPublishCreate,
    AnnouncementAcknowledgment,
    AnnouncementAcknowledgmentCreate,
    create_indexes
)
from app.utils.auth_utils import get_current_user
from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import get_tenant_id
from datetime import datetime, timezone
from app.utils.datetime_utils import generate_timestamp, from_isoformat, to_utc
from bson import ObjectId

_SHOW_NAME = "announcements"

router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {'description': 'Not found'}},
)

tenant_db =get_mongo_db().db
create_indexes(db=tenant_db)

@router.post("/drafts", response_model=AnnouncementDraft)
async def create_draft(
    draft: AnnouncementDraftC<PERSON>,
    current_user = Depends(get_current_user)
):
    db = get_mongo_db().db
    user_id = current_user.get("cognito:username")
    tenant_id = get_tenant_id()
    
    new_draft = AnnouncementDraft(
        **draft.dict(),
        user_id=user_id,
        tenant_id=tenant_id
    )
    
    result = db['announcement_drafts'].insert_one(new_draft.to_mongo())
    
    if not result.inserted_id:
        raise HTTPException(status_code=500, detail="Failed to create draft")
    
    return new_draft

@router.get("/drafts", response_model=List[AnnouncementDraft])
async def get_drafts(
    current_user = Depends(get_current_user)
):
    db = get_mongo_db().db
    user_id = current_user.get("cognito:username")
    tenant_id = get_tenant_id()
    
    records = db['announcement_drafts'].find({
        "user_id": user_id,
        "tenant_id": tenant_id
    })
    
    drafts = []
    for doc in records:
        drafts.append(AnnouncementDraft.from_mongo(doc))
    
    return drafts

@router.post("/publish", response_model=AnnouncementPublish)
async def publish_announcement(
    announcement: AnnouncementPublishCreate,
    current_user = Depends(get_current_user)
):
    db = tenant_db
    user_id = current_user.get("cognito:username")
    tenant_id = get_tenant_id()
    
    now = datetime.now(timezone.utc)
    is_published = not announcement.published_at or to_utc(announcement.published_at) <= now
    
    new_announcement = AnnouncementPublish(
        **announcement.dict(),
        user_id=user_id,
        tenant_id=tenant_id,
        is_published=is_published
    )
    
    result = db['announcements'].insert_one(new_announcement.to_mongo())
    
    if not result.inserted_id:
        raise HTTPException(status_code=500, detail="Failed to publish announcement")
    
    return new_announcement

@router.get("/active", response_model=List[AnnouncementPublish])
async def get_active_announcements(
    current_user = Depends(get_current_user)
):
    db = tenant_db
    now = datetime.now(timezone.utc)
    user_id = current_user.get("cognito:username")
    
    # Get most recent active announcement
    active_announcement = db['announcements'].find_one({
        "is_active": True,
        "published_at": {"$lte": now},
        "$or": [
            {"expires_at": None},
            {"expires_at": {"$gt": now}}
        ]
    }, sort=[("created_at", -1)])  # Sort by date descending
    
    if not active_announcement:
        return []
    
    # Check if user has acknowledged this announcement
    acknowledged = db['announcement_acknowledgments'].find_one({
        "announcement_id": active_announcement["_id"],
        "user_id": user_id,
        "do_not_show_again": True
    })
    
    if acknowledged:
        return []
    
    return [AnnouncementPublish.from_mongo(active_announcement)]

@router.delete("/drafts/{draft_id}")
async def delete_draft(
    draft_id: str,
    current_user = Depends(get_current_user)
):
    db = tenant_db
    user_id = current_user.get("cognito:username")
    tenant_id = get_tenant_id()
    
    result = db['announcement_drafts'].delete_one({
        "_id": ObjectId(draft_id),
        "user_id": user_id,
        "tenant_id": tenant_id
    })
    
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Draft not found")
    
    return {"message": "Draft deleted successfully"}


@router.post("/{announcement_id}/acknowledge")
async def acknowledge_announcement(
    announcement_id: str,
    acknowledgment: AnnouncementAcknowledgmentCreate,
    current_user = Depends(get_current_user)
):
    db = tenant_db
    tenant_id = get_tenant_id()
    user_id = current_user.get("cognito:username")
    now = generate_timestamp()

    new_acknowledgment = AnnouncementAcknowledgment(
        announcement_id=announcement_id,
        user_id=user_id,
        tenant_id=tenant_id,
        do_not_show_again=acknowledgment.do_not_show_again,
        acknowledged_at=now
    )

    result = db['announcement_acknowledgments'].update_one(
        {
            "announcement_id": ObjectId(announcement_id),
            "user_id": user_id,
            "tenant_id": tenant_id
        },
        {"$set": new_acknowledgment.to_mongo()},
        upsert=True
    )

    if not result.acknowledged:
        raise HTTPException(status_code=500, detail="Failed to acknowledge announcement")

    return {"message": "Announcement acknowledged successfully"}

@router.get("/all", response_model=List[AnnouncementPublish])
async def get_all_announcements(
    current_user = Depends(get_current_user)
):
    db = tenant_db
    now = datetime.now(timezone.utc)
    
    # Get all announcements and sort by is_active (desc) and created_at (desc)
    records = db['announcements'].find({}).sort([
        ("is_active", -1),  # Active ones first
        ("created_at", -1)  # Most recent first
    ])
    
    announcements = []
    for doc in records:
        # Calculate if announcement is currently active
        is_active = doc.get("is_active", True)
        expires_at = doc.get("expires_at")
        
        if expires_at:
            # Ensure expires_at is UTC
            if isinstance(expires_at, str):
                expires_at = datetime.fromisoformat(expires_at)
            expires_at = to_utc(expires_at)
            if expires_at <= now:
                is_active = False
        
        doc["is_active"] = is_active
        announcements.append(AnnouncementPublish.from_mongo(doc))
    
    return announcements

@router.delete("/{announcement_id}")
async def delete_announcement(
    announcement_id: str,
    current_user = Depends(get_current_user)
):
    db = tenant_db
    user_id = current_user.get("cognito:username")
    tenant_id = get_tenant_id()
    
    result = db['announcements'].delete_one({
        "_id": ObjectId(announcement_id),
        "user_id": user_id,
        "tenant_id": tenant_id
    })
    try:
        # Delete all acknowledgments for this announcement
        db['announcement_acknowledgments'].delete_many({
            "announcement_id": ObjectId(announcement_id)
        })
    except Exception as e:
        print(f"Error deleting acknowledgments: {e}")
        pass
    
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Announcement not found")
    
    return {"message": "Announcement deleted successfully"}