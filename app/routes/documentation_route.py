# app/routes/documentation.py
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, status, Depends, Body
from fastapi.responses import StreamingResponse, FileResponse
from typing import Optional, List

import urllib
from app.utils.file_utils.upload_utils import upload_and_process, s3_client, get_tenant_bucket
from app.utils.validation_utils import validate_name
from app.connection.tenant_middleware import get_tenant_id
from io import BytesIO
import json
from datetime import datetime
from botocore.exceptions import ClientError
from app.connection.establish_db_connection import NodeDB, get_node_db
from app.core.constants import MODERN_PDF_CSS
from app.models.documentation_model import CreateSection, SectionReorder, SectionOrder, CreateDocumentationRequest
from app.classes.S3Handler import S3<PERSON><PERSON><PERSON>
from typing import Optional
import traceback


router = APIRouter(
    prefix="/documentation",
    tags=["documentation"],
    responses={404: {"description": "Not found"}}
)

DOC_TYPES = {
    "PRD": "product_requirements",
    "SAD": "system_architecture",
    "API": "api_documentation",
    "SAVED": "saved_documentation",
}

class DocumentationService:
    def __init__(self, tenant_id: str):
        self.s3_base_path = "attachments"
        self.tenant_id = tenant_id
        self.bucket = get_tenant_bucket(tenant_id)

    def list_doc_types(self) -> List[str]:
        """List all available documentation types"""
        return list(DOC_TYPES.keys())

    async def list_versions(
        self, 
        project_id: int, 
        doc_type: str,
        interface_id: Optional[int] = None
    ) -> List[dict]:
        """List all versions of documents for a specific doc type"""
        if doc_type not in DOC_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid document type. Allowed types: {list(DOC_TYPES.keys())}"
            )

        try:

       
      
            prefix = f"{self.s3_base_path}/project-id-{project_id}/{DOC_TYPES[doc_type]}/"
            
            response = s3_client.list_objects_v2(
                Bucket=self.bucket,
                Prefix=prefix,
                Delimiter='/'
            )

            if 'CommonPrefixes' in response:
                if(doc_type == 'SAVED'):
                    saved_docs = []
                    for prefix in response['CommonPrefixes']:
                        prefix = prefix['Prefix']
                        contents = s3_client.list_objects_v2(
                            Bucket = self.bucket,
                            Prefix = prefix
                        )

                        files = []
                        if 'Contents' in contents:
                                for item in contents['Contents']:
                                    file_name = item['Key'].split('/')[-1]
                                    if file_name:
                                        files.append({
                                            "file_name": file_name,
                                            "size": item['Size'],
                                            "last_modified": item['LastModified'].isoformat(),
                                            "key": item['Key']
                                        })

                        saved_docs.extend(files)
                    return [{"version": 1,
                             "files": saved_docs
                            }]
                else:
                    versions = []
                    for prefix in response['CommonPrefixes']:
                        version_prefix = prefix['Prefix']
                        version = version_prefix.split('/')[-2]
                        if version.startswith('v'):
                            version_contents = s3_client.list_objects_v2(
                                Bucket=self.bucket,
                                Prefix=version_prefix
                            )
                            
                            files = []
                            if 'Contents' in version_contents:
                                for item in version_contents['Contents']:
                                    file_name = item['Key'].split('/')[-1]
                                    if file_name:
                                        files.append({
                                            "file_name": file_name,
                                            "size": item['Size'],
                                            "last_modified": item['LastModified'].isoformat(),
                                            "key": item['Key']
                                        })

                            versions.append({
                                "version": version[1:],
                                "files": files
                            })

                    return sorted(versions, key=lambda x: x['version'], reverse=True)

        except ClientError as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error accessing S3: {str(e)}"
            )

    async def download_document(
        self, 
        project_id: int, 
        doc_type: str, 
        version: str,
        folder_path: str, 
        file_name: str,
        interface_id: Optional[int] = None
    ):
        """Download a specific document version"""
        if doc_type not in DOC_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid document type. Allowed types: {list(DOC_TYPES.keys())}"
            )

        try:
  
            s3_key = f"{self.s3_base_path}/project-id-{project_id}/{DOC_TYPES[doc_type]}/{folder_path}/{file_name}"
            
            response = s3_client.get_object(
                Bucket=self.bucket,
                Key=s3_key
            )

            return StreamingResponse(
                response['Body'].iter_chunks(),
                media_type=response['ContentType'],
                headers={
                    'Content-Disposition': f'attachment; filename="{sanitize_filename(file_name)}"',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            )

        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Document not found"
                )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error downloading from S3: {str(e)}"
            )


@router.get("/types")
async def list_doc_types(tenant_id: str = Depends(get_tenant_id)):
    """List all available documentation types"""
    service = DocumentationService(tenant_id)
    return service.list_doc_types()

@router.get("/versions/{project_id}/{doc_type}")
async def list_versions(
    project_id: int,
    doc_type: str,
    interface_id: Optional[int] = None,
    tenant_id: str = Depends(get_tenant_id)
):
    """List all versions for a specific doc type"""
    try:
        service = DocumentationService(tenant_id)
        return await service.list_versions(project_id, doc_type, interface_id)
    except Exception as e:
        print(f"Error in list_versions: {str(e)}")
        print("Traceback:")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/download/{project_id}/{doc_type}/{version}/{folder_path}/{file_name}")
async def download_document(
    project_id: int,
    doc_type: str,
    version: str,
    folder_path: str,
    file_name: str,
    interface_id: Optional[int] = None,
    tenant_id: str = Depends(get_tenant_id)
):
    """Download a specific document version"""
    try:
        service = DocumentationService(tenant_id)
        return await service.download_document(
            project_id=project_id,
            doc_type=doc_type,
            version=version,
            file_name=file_name,
            folder_path=folder_path,
            interface_id=interface_id
        )
    except Exception as e:
        print(f"Error in download_document: {str(e)}")
        print("Traceback:")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/doc/{project_id}/{doc_type}")
async def get_doc_versions(
    project_id: int,
    doc_type: str,
    interface_id: Optional[int] = None,
    tenant_id: str = Depends(get_tenant_id)
):
    """Get all versions of a specific document type"""
    try:
        await validate_api_documentation(doc_type, interface_id)
        service = DocumentationService(tenant_id)
        return await service.list_versions(
            project_id=project_id,
            doc_type=doc_type,
            interface_id=interface_id
        )
    except Exception as e:
        print(f"Error in get_doc_versions: {str(e)}")
        print("Traceback:")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/latest/{project_id}/{doc_type}")
async def get_latest_version(
    project_id: int,
    doc_type: str,
    interface_id: Optional[int] = None,
    tenant_id: str = Depends(get_tenant_id)
):
    """Get the latest version of a specific document type"""
    try:
        await validate_api_documentation(doc_type, interface_id)
        service = DocumentationService(tenant_id)
        versions = await service.list_versions(
            project_id=project_id,
            doc_type=doc_type,
            interface_id=interface_id
        )
        
        if not versions:
            raise HTTPException(status_code=404, detail="No versions found")
            
        return versions[0]  # Already sorted by version in list_versions
    except Exception as e:
        print(f"Error in get_latest_version: {str(e)}")
        print("Traceback:")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

async def validate_api_documentation(documentation_type: str, interface_id: Optional[int]):
    """Validate interface_id requirement for API documentation"""
    if documentation_type == "API" and not interface_id:
        raise HTTPException(
            status_code=400,
            detail="interface_id is required for API documentation"
        )

async def get_documentation_filename(
    project_id: int, 
    documentation_type: str, 
    node_db: NodeDB,
    interface_id: Optional[int] = None
) -> str:
    """Generate filename for documentation PDF"""
    try:
        await validate_api_documentation(documentation_type, interface_id)
        
        if documentation_type == "API":
            default_file_name = f"API_interface_{interface_id}_{project_id}.pdf"
        else:
            default_file_name = f"{documentation_type.upper()}_{project_id}.pdf"
        
        result = await node_db.get_documentation(
            project_id=project_id, 
            documentation_type=documentation_type,
            interface_id=interface_id
        )
        
        if result and len(result) > 0:
            title = result[0]['doc'].get("Title", "").strip()
            if title:
                current_time = datetime.now().replace(microsecond=0).isoformat()
                return f"{title}_{current_time}.pdf"
                
        return default_file_name
        
    except Exception:
        if documentation_type == "API":
            return f"API_interface_{interface_id}_{project_id}.pdf"
        return f"{documentation_type.upper()}_{project_id}.pdf"

async def generate_documentation_pdf(
    project_id: int, 
    documentation_type: str, 
    node_db: NodeDB,
    interface_id: Optional[int] = None
) -> tuple:
    """Generate PDF content and return both the PDF bytes and the document result"""
    try:
        await validate_api_documentation(documentation_type, interface_id)
        
        result = await node_db.get_documentation(
            project_id=project_id, 
            documentation_type=documentation_type,
            interface_id=interface_id
        )
        
        if not result or len(result) == 0:
            raise HTTPException(status_code=404, detail="Documentation not found")

        import mistune
        from weasyprint import HTML, CSS
        
        doc = result[0]['doc']
        sections = result[0]['sections']

        markdown_parser = mistune.create_markdown(
            plugins=['table', 'footnotes', 'strikethrough']
        )

        html_content = generate_pdf_html_content(doc, sections, markdown_parser)
        css = CSS(string=MODERN_PDF_CSS)
        html = HTML(string=html_content)
        pdf = html.write_pdf(
            stylesheets=[css],
            presentational_hints=True,
            optimize_size=('fonts', 'images')
        )
        
        return pdf, result

    except HTTPException:
        raise
    except Exception as e:
        print(f"PDF Generation Error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/create_documentation")
async def create_documentation(
    request: CreateDocumentationRequest,
    node_db: NodeDB = Depends(get_node_db)
):
    """Create documentation root and sections for a project or API interface"""
    try:
        # Validate documentation type and interface_id combination
        await validate_api_documentation(request.documentation_type, request.interface_id)
        
        # Create documentation root node
        doc_root = await node_db.create_documentation_root(
            project_id=request.project_id,
            documentation_type=request.documentation_type,
            interface_id=request.interface_id
        )
        
        if not doc_root:
            raise HTTPException(
                status_code=400,
                detail=f"Failed to create {request.documentation_type} documentation root"
            )

        # Get the created documentation
        result = await node_db.get_documentation(
            project_id=request.project_id,
            documentation_type=request.documentation_type,
            interface_id=request.interface_id
        )
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail="Documentation not found after creation"
            )
            
        return result[0] if result else []

    except HTTPException:
        raise
    except Exception as e:
        print(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Error creating documentation: {str(e)}"
        )
             
@router.get("/")
async def get_documentation(
    project_id: int,
    documentation_type: str = "SAD",
    interface_id: Optional[int] = None,
    node_db: NodeDB = Depends(get_node_db)
):
    """Get documentation for a project or API interface"""
    try:
        await validate_api_documentation(documentation_type, interface_id)
        result = await node_db.get_documentation(
            project_id=project_id, 
            documentation_type=documentation_type,
            interface_id=interface_id
        )
        return result or []
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/create_section")
async def create_section(
    section: CreateSection,
    documentation_type: str = "SAD",
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        await validate_api_documentation(documentation_type, section.interface_id)
        
        # Validate section name
        is_valid, error_message = validate_name(section.section_name, type="Section")
        if not is_valid:
            raise HTTPException(status_code=400, detail=error_message)
            
        # Check for duplicate section names
        existing_docs = await node_db.get_documentation(
            section.project_id, 
            documentation_type,
            interface_id=section.interface_id
        )
        
        if existing_docs and len(existing_docs) > 0:
            sections = existing_docs[0].get('sections', [])
            if any(s['Title'].lower() == section.section_name.strip().lower() for s in sections):
                raise HTTPException(status_code=400, detail="Section name already exists")

        created_section = await node_db.create_section(
            project_id=section.project_id,
            section_name=section.section_name.strip(),
            documentation_type=documentation_type,
            interface_id=section.interface_id
        )
        return created_section
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in create_section: {str(e)}")
        print("Traceback:")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))
    
@router.put("/reorder-sections")
async def reorder_sections(
    reorder_data: SectionReorder,
    documentation_type: str = "SAD",
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        await validate_api_documentation(documentation_type, reorder_data.interface_id)
        
        reorder_data_dict = json.loads(reorder_data.model_dump_json())
        result = await node_db.reorder_sections(
            project_id=reorder_data_dict.get("project_id"),
            section_orders=reorder_data_dict.get("section_orders"),
            documentation_type=documentation_type,
            interface_id=reorder_data_dict.get("interface_id")
        )
        return result
    except ValueError as ve:
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/sections/{project_id}/{section_id}")
async def delete_section(
    project_id: int,
    section_id: int,
    documentation_type: str = "SAD",
    interface_id: Optional[int] = None,
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        await validate_api_documentation(documentation_type, interface_id)
        result = await node_db.delete_section(
            project_id=project_id,
            section_id=section_id,
            documentation_type=documentation_type,
            interface_id=interface_id
        )
        return {"message": "Section deleted successfully", "deleted_section": result}
    except ValueError as ve:
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/download-pdf/{project_id}")
async def download_documentation_pdf(
    project_id: int,
    documentation_type: str = "SAD",
    interface_id: Optional[int] = None,
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        await validate_api_documentation(documentation_type, interface_id)
        
        pdf, _ = await generate_documentation_pdf(
            project_id=project_id, 
            documentation_type=documentation_type,
            node_db=node_db,
            interface_id=interface_id
        )
        
        file_name = await get_documentation_filename(
            project_id=project_id,
            documentation_type=documentation_type,
            node_db=node_db,
            interface_id=interface_id
        )

        return StreamingResponse(
            BytesIO(pdf),
            media_type="application/pdf",
            headers={
                'Content-Disposition': f'attachment; filename="{sanitize_filename(file_name)}"',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        )
    except Exception as e:
        print(f"PDF Generation Error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/sync-to-s3/{project_id}")
async def sync_documentation_to_s3(
    project_id: int,
    documentation_type: str = "SAD",
    interface_id: Optional[int] = None,
    tenant_id: str = Depends(get_tenant_id),
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        await validate_api_documentation(documentation_type, interface_id)
        
        pdf, _ = await generate_documentation_pdf(
            project_id=project_id,
            documentation_type=documentation_type,
            node_db=node_db,
            interface_id=interface_id
        )
        
        file_name = await get_documentation_filename(
            project_id=project_id,
            documentation_type=documentation_type,
            node_db=node_db,
            interface_id=interface_id
        )
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        from tempfile import SpooledTemporaryFile
        spool = SpooledTemporaryFile()
        spool.write(pdf)
        spool.seek(0)
        
        upload_file = UploadFile(
            filename=file_name,
            file=spool,
            headers={"content-type": "application/pdf"}
        )

        try:
            result = await upload_document(
                project_id=project_id,
                doc_type=documentation_type.upper(),
                version=timestamp,
                file=upload_file,
                tenant_id=tenant_id,
                interface_id=interface_id
            )
            return result
        finally:
            spool.close()

    except Exception as e:
        print(f"Sync to S3 Error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    
@router.post("/fetch-saved-docs")
async def fetch_saved_docs(
    request_data: dict = Body(...),
    tenant_id: str = Depends(get_tenant_id)
):
    try:
        project_id = request_data.get('project_id')
        doc_type = request_data.get('doc_type')
        version = request_data.get('version', '')
        folder_id = request_data.get('folder_id', '')

        s3_handler = S3Handler(tenant_id)
        
        if(doc_type == "SAVED"):
            path = f"attachments/project-id-{project_id}/{DOC_TYPES[doc_type]}/{folder_id}"
        else:
            path = f"attachments/project-id-{project_id}/{DOC_TYPES[doc_type]}/v{version}"

        files = s3_handler.list_all_filenames(path)
        if(len(files) > 0):
            return {
                "message": "Files fetched successfully",
                "files": files
            }
        else: 
            return {
                "message": "No files found",
                "files": []
            }
    except Exception as e:
        raise HTTPException(status_code=404, detail="An error occured.")


    
@router.post("/upload")
async def upload_document(
    project_id: int = Form(...),
    doc_type: str = Form(...),
    version: str = Form(...),
    file: UploadFile = File(...),
    folder_id: str = Form(...),
    tenant_id: str = Depends(get_tenant_id),
    interface_id: Optional[int] = Form(None),
    description: Optional[str] = Form(None)
):
    """Upload a new document"""
    try:
        await validate_api_documentation(doc_type, interface_id)
        
        service = DocumentationService(tenant_id)
        if doc_type not in DOC_TYPES:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid document type. Allowed types: {list(DOC_TYPES.keys())}"
            )

        # Modify s3_path to include interface_id for API docs

        if(doc_type != "SAVED"):
            s3_path = f"project-id-{project_id}/{DOC_TYPES[doc_type]}/v{version}"
        else:
            s3_path = f"project-id-{project_id}/{DOC_TYPES[doc_type]}/{folder_id}"

        file_content = await file.read()
        result = upload_and_process(
            identifier=s3_path,
            file_content=file_content,
            file_name=file.filename,
            content_type=file.content_type,
            tenant_id=tenant_id,
            folder_name=service.s3_base_path
        )

        return {
            "message": "Document uploaded successfully",
            "location": result["s3_location"],
            "file_name": file.filename,
            "version": version,
            "bucket": result["bucket_name"],
            "interface_id": interface_id
        }
    except Exception as e:
        print(f"Upload Error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


def generate_pdf_html_content(doc, sections, markdown_parser):
    """Helper function to generate HTML content for PDF"""
    toc_items = ['<h2>Table of Contents</h2>']
    content_sections = []

    if doc.get("Description"):
        toc_items.append(
            '<li class="toc-item level-1">'
            '<a href="#introduction">1. Introduction</a>'
            '</li>'
        )

    section_start = 2 if doc.get("Description") else 1
    for idx, section in enumerate(sections, start=section_start):
        section_id = f"section-{idx}"
        section_title = section['Title']
        
        toc_items.append(
            f'<li class="toc-item level-1">'
            f'<a href="#{section_id}">{idx}. {section_title}</a>'
            f'</li>'
        )
        
        content = markdown_parser(section['Content'])
        content_sections.append(
            f'<section class="chapter" id="{section_id}">'
            f'<h1 class="chapter-title">{section_title}</h1>'
            f'<div class="chapter-content">{content}</div>'
            '</section>'
        )

    # Build HTML content
    html_content = [
        '<!DOCTYPE html>',
        '<html>',
        '<head>',
        f'<meta charset="UTF-8">',
        f'<title>{doc["Title"]}</title>',
        '</head>',
        '<body>',
        
        f'<div class="title-page">',
        f'<h1>{doc["Title"]}</h1>',
        f'<p class="metadata">Version: {doc.get("Version", "1.0")}</p>',
        f'<p class="metadata">Generated on: {datetime.now().strftime("%B %d, %Y")}</p>',
        '</div>',
        
        '<nav class="toc-nav">',
        '<ul class="toc-list">',
        ''.join(toc_items),
        '</ul>',
        '</nav>'
    ]

    # Add introduction if exists
    if doc.get("Description"):
        html_content.extend([
            '<div class="chapter">',
            '<h1 class="chapter-title" id="introduction">Introduction</h1>',
            f'<div class="chapter-content">{markdown_parser(doc["Description"])}</div>',
            '</div>'
        ])

    # Add content sections
    html_content.extend([
        ''.join(content_sections),
        '</body>',
        '</html>'
    ])

    return '\n'.join(html_content)

def sanitize_filename(filename):
    """Sanitize filename to ensure it's compatible with HTTP headers"""
    # Replace problematic Unicode characters
    unicode_replacements = {
        '\u2013': '-',    # en dash
        '\u2014': '--',   # em dash
        '\u201c': '"',    # left double quotation mark
        '\u201d': '"',    # right double quotation mark
        '\u2018': "'",    # left single quotation mark
        '\u2019': "'",    # right single quotation mark
        '\u00a0': ' ',    # non-breaking space
        '\u2026': '...',  # ellipsis
        '\u00ae': 'R',    # registered trademark
        '\u2122': 'TM',   # trademark
        '\u00a9': 'C',    # copyright
        # Add more as needed
    }
    
    for char, replacement in unicode_replacements.items():
        filename = filename.replace(char, replacement)
    
    # Further sanitize to ASCII-only characters
    import re
    filename = re.sub(r'[^\x00-\x7F]+', '_', filename)
    
    # Replace problematic characters with underscores
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # URL encode the filename to ensure it's safe in headers
    return urllib.parse.quote(filename)