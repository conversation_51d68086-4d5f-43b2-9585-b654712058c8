from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import HTMLResponse, StreamingResponse, FileResponse
from datetime import datetime, timedelta
from fastapi.templating import Jinja2Templates
import os, asyncio
import time

router = APIRouter(
    tags=['base'],
    responses={404: {'description': 'Not found'}},
)

@router.get("/", status_code=200)
def health(__: Request):
    return {"message": "Hey!, welcome to Kavia AI..."}

templates = Jinja2Templates(directory="templates")

def get_last_modified_time(file_path: str) -> str:
    if not os.path.exists(file_path):
        return "File not found"

    # Get the last modified time of the file
    last_modified_time = os.path.getmtime(file_path)
    last_modified_datetime = datetime.fromtimestamp(last_modified_time)
    
    # Calculate the time difference between now and the last modified time
    time_difference = datetime.now() - last_modified_datetime

    # Determine the appropriate time unit (minutes, hours, days)
    if time_difference < timedelta(minutes=1):
        duration = "just now"
    elif time_difference < timedelta(hours=1):
        minutes = int(time_difference.total_seconds() // 60)
        duration = f"{minutes} minutes ago"
    elif time_difference < timedelta(days=1):
        hours = int(time_difference.total_seconds() // 3600)
        duration = f"{hours} hours ago"
    else:
        days = time_difference.days
        duration = f"{days} days ago"

    # Format the output
    formatted_date = last_modified_datetime.strftime('%Y-%m-%d %H:%M:%S')
    return f"{formatted_date} - {duration}"

def sort_logs_by_date(logs):
    """
    Sorts the log files based on their last modified date in descending order (most recent first).

    Args:
        logs (list): A list of logs where each log is a list with the log name and last modified timestamp.

    Returns:
        list: A sorted list of logs by last modified date.
    """
    # Function to extract the datetime object from the log entry
    def get_log_datetime(log):
        log_date_str = log[1].split(' - ')[0]
        return datetime.strptime(log_date_str, '%Y-%m-%d %H:%M:%S')

    # Sort logs by the datetime extracted from each log entry
    sorted_logs = sorted(logs, key=get_log_datetime, reverse=True)

    return sorted_logs

@router.get("/logs/{filename}")
async def logs(filename: str):
    file_path = os.path.join('logs',filename)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")
    # This route will directly link to the streaming endpoint
    return FileResponse(file_path, media_type="text/plain")

@router.get('/logs')
async def read_item(request: Request):
    log_file_list = ''.join(f"<li><a href='/logs/{filename}'>{filename}</a> - [ Last Modified {get_last_modified_time(f'logs/{filename}')}]</li>" for filename in os.listdir('logs') if any(filename.strip()))
    log_file_list = [[filename,get_last_modified_time(f'logs/{filename}')] for filename in os.listdir("logs")]
    return templates.TemplateResponse(
        request=request, name="content.html", context= {"request": request, "log_file_list":sort_logs_by_date(log_file_list)}
    )

@router.get("/logs/stream/{filename}")
async def log_content(filename: str):
    file_path = os.path.join('logs',filename)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")
    async def log_generator():
        try:
            with open(file_path, "r") as log_file:
                while True:
                    line = log_file.readline()
                    if not line:
                        await asyncio.sleep(1)  # Wait before checking for new content
                        continue
                    yield line
        except asyncio.CancelledError:
            # Handle client disconnection
            print("Client disconnected")
        finally:
            # Cleanup if necessary
            print("Stream ended")

    return StreamingResponse(log_generator(), media_type="text/plain")