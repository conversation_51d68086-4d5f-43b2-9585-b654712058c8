from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from typing import List, Dict, Any, Optional
from app.connection.establish_db_connection import get_node_db, NodeDB
from app.utils.auth_utils import get_current_user
from datetime import datetime

_SHOW_NAME = "testcase"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

# Routes will be implemented here later 
@router.get("/")
async def get_testcases(
    db: NodeDB = Depends(get_node_db)
):
    return {"message": "Testcases fetched successfully"}


@router.get("/get_categories/{project_id}")
async def get_categories(
    project_id: int,
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        query = """
MATCH (p:Project)-[:HAS_CHILD]->(:TestCaseRoot)-[:HAS_CHILD]->(m)
WHERE ID(p) = $project_id
WITH 
  CASE 
    WHEN m.CanBeAutomated = true THEN ["Automation", m.TestLevel, m.Type]
    ELSE ["Manual", m.TestLevel, m.Type]
  END AS nodeTypeArray,
  CASE 
    WHEN m.Category IS NOT NULL AND m.Category <> 'default' AND m.Category <> '' 
    THEN m.Category
    ELSE NULL
  END AS category
WHERE category IS NOT NULL
WITH nodeTypeArray, COLLECT(DISTINCT category) AS categories
RETURN nodeTypeArray, categories
ORDER BY nodeTypeArray[0], nodeTypeArray[1];
        """
        
        result = await node_db.async_run(query, project_id=project_id)
        categories_data = result.data()
        print(query, categories_data, project_id)
        
        if not categories_data:
            return {}

        # Process results dynamically into a nested dictionary
        processed_data = {}

        for item in categories_data:
            node_type_array = item["nodeTypeArray"]
            categories = item["categories"]
            
            # Create nested structure dynamically
            current_level = processed_data
            for key in node_type_array[:-1]:  # Iterate through all except the last key
                if key not in current_level:
                    current_level[key] = {}  # Initialize if missing
                current_level = current_level[key]  # Go deeper into the structure
            
            # Assign categories to the last key
            last_key = node_type_array[-1]
            current_level[last_key] = categories
        
        return processed_data

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch test case categories: {str(e)}"
        )

    
@router.get("/get_testcases_by_category/{project_id}/{category}")
async def get_testcases_by_category(
    project_id: int,
    category: str,
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        query = """
        MATCH (p:Project)-[:HAS_CHILD]->(tcr:TestCaseRoot)-[:HAS_CHILD]->(n) 
        WHERE ID(p) = $project_id AND n.Category = $category
        RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
        """
        
        result = await node_db.async_run(query, project_id=project_id, category=category)
        testcases = result.data()
        
        if not testcases:
            return []
        
        return testcases
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch testcases by category: {str(e)}"
        )

@router.get("/get_testcases_by_node_type/{project_id}/{node_type}")
async def get_testcases_by_node_type(
    project_id: int,
    node_type: str,
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        query = """
        MATCH (p:Project)-[:HAS_CHILD]->(tcr:TestCaseRoot)-[:HAS_CHILD]->(m)-[:VERIFIES]->(n)
        WHERE ID(p) = $project_id AND $node_type IN LABELS(n)
        RETURN ID(m) AS id, LABELS(m) AS labels, properties(m) AS properties
        """
        
        result = await node_db.async_run(query, project_id=project_id, node_type=node_type)
        testcases = result.data()
        
        if not testcases:
            return []
        
        return testcases
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch testcases for node type {node_type}: {str(e)}"
        )

@router.get("/get_automatable_testcases/{project_id}")
async def get_automatable_testcases(
    project_id: int,
    can_be_automated: bool = True,
    test_case_type: str = None,
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        # Base query
        if test_case_type:
            # If test case type is specified (e.g., FunctionalTestCase)
            query = f"""
            MATCH (p:Project)-[:HAS_CHILD]->(tcr:TestCaseRoot)-[:HAS_CHILD]->(testcase:{test_case_type})
            WHERE ID(p) = $project_id AND testcase.CanBeAutomated = $can_be_automated
            RETURN ID(testcase) AS id, LABELS(testcase) AS labels, properties(testcase) AS properties
            """
        else:
            # Original query for all test case types
            query = """
            MATCH (p:Project)-[:HAS_CHILD]->(tcr:TestCaseRoot)-[:HAS_CHILD]->(testcase)
            WHERE ID(p) = $project_id AND testcase.CanBeAutomated = $can_be_automated
            RETURN ID(testcase) AS id, LABELS(testcase) AS labels, properties(testcase) AS properties
            """
        
        result = await node_db.async_run(query, project_id=project_id, can_be_automated=can_be_automated)
        testcases = result.data()
        
        if not testcases:
            return []
        
        return testcases
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch automatable testcases: {str(e)}"
        )

@router.get("/get_testcase_root/{project_id}/{testcase_id}")
async def get_testcase_root(
    project_id: int,
    testcase_id: int,
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        query = """
        MATCH (p:Project)-[:HAS_CHILD]->(tcr:TestCaseRoot)-[:HAS_CHILD]->(testcase)-[:VERIFIES]->(verified_node)
        WHERE ID(p) = $project_id AND ID(testcase) = $testcase_id
        RETURN ID(verified_node) AS id, LABELS(verified_node) AS labels, properties(verified_node) AS properties
        """
        
        result = await node_db.async_run(query, project_id=project_id, testcase_id=testcase_id)
        verified_nodes = result.data()
        
        if not verified_nodes:
            return []
        
        return verified_nodes
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch verified node for testcase {testcase_id}: {str(e)}"
        )

@router.get("/get_testcase_root_by_id/{testcase_id}")
async def get_testcase_root_by_id(
    testcase_id: int,
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        query = """
        MATCH (tr)-[:VERIFIES]-(r) 
        WHERE ID(tr) = $testcase_id 
        RETURN ID(r) AS id, LABELS(r) AS labels, properties(r) AS properties
        """
        
        result = await node_db.async_run(query, testcase_id=testcase_id)
        testcase_data = result.data()
        
        if not testcase_data:
            return {"message": "No testcase root found with the given ID"}
        
        # Format the response to include the ID
        response = testcase_data[0]
        
        return response
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch testcase root with ID {testcase_id}: {str(e)}"
        )
