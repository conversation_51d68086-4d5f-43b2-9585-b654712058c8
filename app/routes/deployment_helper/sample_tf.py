     
def get_main_tf() -> str:
    """Retrieve the main.tf file content."""
    return """
resource "aws_amplify_app" "hello_world_amplify" {
  name       = var.app_name
  repository = var.repository #This will be your reactjs project

  access_token             = var.access_token
  enable_branch_auto_build = true

   # Updated build_spec for Create React App
  build_spec = <<-EOT
    version: 1
    frontend:
      phases:
        preBuild:
          commands:
            - npm ci --cache .npm --prefer-offline
        build:
          commands:
            - npm run build
      artifacts:
        baseDirectory: build
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
          - .npm/**/*
  EOT

  # The default rewrites and redirects added by the Amplify Console.
  custom_rule {
    source = "/<*>"
    status = "404"
    target = "/index.html"
  }

  environment_variables = {
    Name           = "hello-world"
    Provisioned_by = "Terraform"
  }
}

resource "aws_amplify_branch" "amplify_branch" {
  app_id            = aws_amplify_app.hello_world_amplify.id
  branch_name       = var.branch_name
  enable_auto_build = true
}

resource "aws_amplify_domain_association" "domain_association" {
  app_id                = aws_amplify_app.hello_world_amplify.id
  domain_name           = var.domain_name
  wait_for_verification = false

  sub_domain {
    branch_name = aws_amplify_branch.amplify_branch.branch_name
    prefix      = var.branch_name
  }

}
"""

def get_variables_tf( repository, app_name, branch_name, domain_name) -> str:
    """Retrieve the variables.tf file content with input values.
    
    Args:
        access_token (str): GitHub access token
        repository (str): GitHub repository URL
        app_name (str): AWS Amplify app name
        branch_name (str): Branch name to deploy
        domain_name (str, optional): Custom domain name, defaults to awsamplifyapp.com
    
    Returns:
        str: Formatted variables.tf content
    """
    # Use default domain if none provided
    domain_name = domain_name or "awsamplifyapp.com"
    
    return f'''variable "access_token" {{
  type        = string
  description = "github token to connect github repo"
  default     = "****************************************"
}}

variable "repository" {{
  type        = string
  description = "github repo url"
  default     = "{repository}"
}}

variable "app_name" {{
  type        = string
  description = "AWS Amplify App Name"
  default     = "{app_name}"
}}

variable "branch_name" {{
  type        = string
  description = "AWS Amplify App Repo Branch Name"
  default     = "{branch_name}"
}}

variable "domain_name" {{
  type        = string
  default     = "{domain_name}"
  description = "AWS Amplify Domain Name"
}}'''

def get_outputs_tf() -> str:
    """Retrieve the outputs.tf file content."""
    return """
output "amplify_app_id" {
  value = aws_amplify_app.hello_world_amplify.id
}
"""

def get_providers_tf() -> str:
    """Retrieve the terraform.tf file content."""
    return """
provider "aws" {
  region = "us-east-2"  # Directly specify the region instead of using a variable
}
"""

def get_workflows(branch: str) -> str:
    """
    Retrieve the workflow file content with dynamic branch name.
    
    Args:
        branch: The Git branch name to use for deployment
    """
    return f"""
name: Deploy to AWS Amplify

on:
  push:
    branches:
      - {branch}
  pull_request:
    branches:
      - {branch}

env:
  AWS_REGION: us-east-2
  AMPLIFY_APP_ID: ${{{{ secrets.AMPLIFY_APP_ID }}}}
  
jobs:
  deploy:
    name: Deploy to Amplify
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          
      - uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{{{ secrets.AWS_ACCESS_KEY_ID }}}}
          aws-secret-access-key: ${{{{ secrets.AWS_SECRET_ACCESS_KEY }}}}
          aws-region: us-east-2
          
      - name: Validate Amplify App ID
        run: |
          # Validate that AMPLIFY_APP_ID is set
          if [ -z "${{{{ secrets.AMPLIFY_APP_ID }}}}" ]; then
            echo "Error: AMPLIFY_APP_ID is not set in GitHub secrets"
            exit 1
          fi
          
          # Validate the format of AMPLIFY_APP_ID
          if [[ ! "${{{{ secrets.AMPLIFY_APP_ID }}}}" =~ ^d[a-z0-9]+$ ]]; then
            echo "Error: AMPLIFY_APP_ID must start with 'd' followed by alphanumeric characters"
            exit 1
          fi
          
          echo "AMPLIFY_APP_ID validation passed"

       # Updated dependency installation steps with TypeScript version fix
      - name: Clean and reinstall dependencies
        run: |
          # Remove existing node_modules and lock files
          rm -rf node_modules package-lock.json

          # First, install TypeScript and styled-components
          npm install --save-dev typescript@4.9.5
          npm install styled-components@latest

          # Install remaining dependencies
          npm install --legacy-peer-deps

          # Verify TypeScript version
          echo "Installed TypeScript version:"
          npm list typescript

          # Force resolution of TypeScript version in package.json
          node -e '
            const fs = require("fs");
            const package = JSON.parse(fs.readFileSync("package.json", "utf8"));
            if (!package.resolutions) package.resolutions = {{}};
            package.resolutions.typescript = "4.9.5";
            fs.writeFileSync("package.json", JSON.stringify(package, null, 2));
          '

          # Clean install with forced resolutions
          npm install --legacy-peer-deps
        
      - name: Build application
        run: |
          npm run build
        env:
          NODE_ENV: production
          CI: false  # Added to prevent treating warnings as errors
          
      - name: Deploy to Amplify
        env:
          AMPLIFY_APP_ID: ${{{{ secrets.AMPLIFY_APP_ID }}}}
        run: |
          BRANCH_NAME="{branch}"
          echo "Deploying branch: $BRANCH_NAME"
          
          check_and_cancel_jobs() {{
            echo "Checking for existing jobs..."
            JOBS=$(aws amplify list-jobs \\
              --app-id "$AMPLIFY_APP_ID" \\
              --branch-name "$BRANCH_NAME" \\
              --query 'jobSummaries[?status==`PENDING` || status==`RUNNING`].jobId' \\
              --output text || echo "")
              
            if [ ! -z "$JOBS" ]; then
              for JOB_ID in $JOBS; do
                echo "Cancelling job: $JOB_ID"
                aws amplify stop-job \\
                  --app-id "$AMPLIFY_APP_ID" \\
                  --job-id "$JOB_ID" || true
                sleep 5
              done
            fi
          }}
          
          start_deployment() {{
            echo "Starting deployment..."
            aws amplify start-job \\
              --app-id "$AMPLIFY_APP_ID" \\
              --branch-name "$BRANCH_NAME" \\
              --job-type RELEASE
          }}
          
          MAX_RETRIES=3
          RETRY_COUNT=0
          DEPLOY_SUCCESS=false
          
          while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$DEPLOY_SUCCESS" = false ]; do
            echo "Attempt $((RETRY_COUNT + 1)) of $MAX_RETRIES"
            
            check_and_cancel_jobs
            
            sleep 10
            
            if start_deployment; then
              DEPLOY_SUCCESS=true
              echo "Deployment started successfully"
            else
              echo "Deployment attempt failed"
              RETRY_COUNT=$((RETRY_COUNT + 1))
              
              if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
                echo "Retrying in 15 seconds..."
                sleep 15
              fi
            fi
          done
          
          if [ "$DEPLOY_SUCCESS" = true ]; then
            echo "Deployment process completed successfully"
            exit 0
          else
            echo "Failed to deploy after $MAX_RETRIES attempts"
            exit 1
          fi
"""