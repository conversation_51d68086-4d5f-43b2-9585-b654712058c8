import os
import json

async def find_application_directory(repo_path: str) -> str:
    """
    Get working directory from .init-run-tool file in repository.
    
    Args:
        repo_path: Path to cloned repository
        
    Returns:
        str: Working directory path
    Raises:
        Exception: If working directory cannot be determined
    """
    try:
        init_file_path = os.path.join(repo_path, '.init-run-tool')
        if not os.path.exists(init_file_path):
            raise Exception(".init-run-tool file not found in repository")

        # Read and parse .init-run-tool file
        with open(init_file_path, 'r') as f:
            init_config = json.load(f)
            
        working_dir = init_config.get('working_directory')
        if not working_dir:
            raise Exception("No working_directory specified in .init-run-tool")
            
        # Handle absolute vs relative paths
        if os.path.isabs(working_dir):
            # For absolute paths, extract the relative part after repo name
            repo_name = os.path.basename(repo_path)
            working_dir_parts = working_dir.split(repo_name)
            if len(working_dir_parts) > 1:
                relative_path = working_dir_parts[1].lstrip('/')
                working_dir = os.path.join(repo_path, relative_path)
            else:
                working_dir = os.path.join(repo_path, working_dir.lstrip('/'))
        else:
            working_dir = os.path.join(repo_path, working_dir)

        # Verify directory exists
        if not os.path.exists(working_dir):
            raise Exception(f"Working directory {working_dir} not found in repository")

        logger.info(f"Found working directory: {working_dir}")
        return working_dir

    except json.JSONDecodeError:
        raise Exception("Invalid JSON in .init-run-tool file")
        
    except Exception as e:
        logger.error(f"Error getting working directory: {str(e)}")
        raise