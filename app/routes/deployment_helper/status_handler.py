from fastapi import APIRout<PERSON>, HTTPException, Depends, Body
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional,List
import os
import logging
import subprocess 
import json
import asyncio
from urllib.parse import urlparse
from datetime import datetime
from github import Github, GithubException
import shutil
import boto3
from app.connection.establish_db_connection import get_node_db, NodeDB
from app.routes.deployment_helper.sample_tf import get_workflows  
from app.routes.repository_route import get_repository,list_branches
from app.routes.deployment_helper.node_helper import get_deployment_node
from app.routes.deployment_helper.directory_finder import find_application_directory
from app.routes.deployment_helper.aws_handler import modify_codecommit_repo_name,push_to_codecommit,clone_codecommit_repo,create_codecommit_repository
from app.routes.deployment_helper.get_tf_files import get_backend_terraform_files

import time
import random
import string
from fastapi.responses import StreamingResponse
from typing import AsyncGenerator, Union, Callable
from enum import Enum
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse, JSONResponse
import boto3
import json
import asyncio
from datetime import datetime
import subprocess

async def get_terraform_outputs(infrastructure_path: str) -> dict:
    """Get outputs from Terraform state."""
    try:
        result = subprocess.run(
            ["terraform", "output", "-json"],
            cwd=infrastructure_path,
            capture_output=True,
            text=True,
            check=True
        )
        return json.loads(result.stdout)
    except subprocess.CalledProcessError as e:
        raise Exception(f"Failed to get Terraform outputs: {e.stderr}")
    except json.JSONDecodeError as e:
        raise Exception(f"Failed to parse Terraform outputs: {str(e)}")

async def get_amplify_details(app_id: str) -> dict:
    """Get AWS Amplify app details including the deployed URL."""
    try:
        amplify_client = boto3.client('amplify', region_name='us-east-2')
        response = amplify_client.get_app(appId=app_id)
        
        branches = amplify_client.list_branches(appId=app_id)
        branch_domains = {}
        
        for branch in branches.get('branches', []):
            branch_name = branch['branchName']
            branch_url = f"https://{branch_name}.{response['app']['defaultDomain']}"
            branch_domains[branch_name] = branch_url
            
        return {
            'app_id': app_id,
            'default_domain': response['app']['defaultDomain'],
            'branch_domains': branch_domains,
            'app_name': response['app']['name'],
            'repository': response['app'].get('repository')
        }
    except Exception as e:
        raise Exception(f"Failed to get Amplify app details: {str(e)}")

async def get_elb_details(app_name: str) -> dict:
    """Get ELB details including the deployed URL."""
    try:
        elb_client = boto3.client('elbv2')
        response = elb_client.describe_load_balancers(
            Names=[f"{app_name}-alb"]
        )
        
        if not response['LoadBalancers']:
            raise Exception(f"No load balancer found for {app_name}")
            
        lb = response['LoadBalancers'][0]
        
        return {
            'load_balancer_dns': lb['DNSName'],
            'url': f"http://{lb['DNSName']}",
            'load_balancer_name': lb['LoadBalancerName'],
            'vpc_id': lb['VpcId']
        }
    except Exception as e:
        raise Exception(f"Failed to get ELB details: {str(e)}")

async def get_ecs_service_status(cluster_name: str, service_name: str) -> dict:
    """Get ECS service status."""
    try:
        ecs_client = boto3.client('ecs')
        response = ecs_client.describe_services(
            cluster=cluster_name,
            services=[service_name]
        )
        
        if not response['services']:
            raise Exception(f"No service found: {service_name}")
            
        service = response['services'][0]
        return {
            'status': service['status'],
            'running_count': service['runningCount'],
            'desired_count': service['desiredCount'],
            'pending_count': service['pendingCount']
        }
    except Exception as e:
        raise Exception(f"Failed to get ECS service status: {str(e)}")

async def get_amplify_deployment_status(app_id: str, branch_name: str) -> dict:
    """
    Get deployment status of an Amplify app branch.
    
    Args:
        app_id: Amplify app ID
        branch_name: Branch name to check
        
    Returns:
        dict: Deployment status details
    """
    try:
        region = os.environ.get('AWS_DEFAULT_REGION', 'us-east-2')
        amplify_client = boto3.client('amplify', region_name=region)
        
        # Get latest job for the branch
        jobs = amplify_client.list_jobs(
            appId=app_id,
            branchName=branch_name,
            maxResults=1  # Get only the latest job
        )
        
        if not jobs.get('jobSummaries'):
            return {
                'status': 'NOT_FOUND',
                'message': f'No deployments found for branch {branch_name}'
            }
            
        latest_job = jobs['jobSummaries'][0]
        
        # Get branch details
        branch = amplify_client.get_branch(
            appId=app_id,
            branchName=branch_name
        )
        
        return {
            'status': latest_job.get('status'),
            'step': latest_job.get('steps', []),
            'job_id': latest_job.get('jobId'),
            'commit_id': latest_job.get('commitId'),
            'start_time': latest_job.get('startTime'),
            'end_time': latest_job.get('endTime'),
            'branch_displayable_status': branch.get('branch', {}).get('displayableStatus'),
            'branch_framework': branch.get('branch', {}).get('framework'),
            'has_active_job': latest_job.get('status') in ['PENDING', 'PROVISIONING', 'RUNNING'],
            'is_deployed': latest_job.get('status') == 'SUCCEED'
        }

    except amplify_client.exceptions.BranchNotFoundException:
        return {
            'status': 'NOT_FOUND',
            'message': f'Branch {branch_name} not found'
        }
    except Exception as e:
        raise Exception(f"Failed to get Amplify deployment status: {str(e)}")