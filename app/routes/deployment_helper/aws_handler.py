from fastapi import APIRouter, HTTPException, Depends, Body
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional
import os
import logging
import subprocess
import json
from urllib.parse import urlparse
from datetime import datetime
from github import Github, GithubException
from github.Repository import Repository
from app.connection.establish_db_connection import get_node_db, NodeDB
from app.utils.auth_utils import get_current_user
import os
import logging
import yaml
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import tempfile
import shutil
import asyncio
import boto3
import time
# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(handler)
logger.setLevel(logging.INFO)

def modify_codecommit_repo_name(repo_url: str) -> str:
    """
    Modify CodeCommit repository URL preserving the original region.
    
    Args:
        repo_url: Original repository URL (e.g. https://git-codecommit.us-east-2.amazonaws.com/...)
    
    Returns:
        str: Modified repository URL with correct region
    """
    if repo_url.endswith('/'):
        repo_url = repo_url[:-1]
    

    aws_region = 'us-east-2'
    
    # Split URL to get repo name
    repo_name = repo_url.split('/')[-1].replace('.git', '').replace('-experimental', '')
    new_repo_name = f"{repo_name}_deploy"
    
    # Construct new URL with correct region
    base_url = f"https://git-codecommit.{aws_region}.amazonaws.com/v1/repos"
    return f"{base_url}/{new_repo_name}"

# AWS credential and configuration helper functions
async def configure_aws_git_credentials(repo_path: str) -> None:
    """Configure git to use AWS credential helper for CodeCommit"""
    try:
        commands = [
            # Remove any existing credential helper
            ["git", "config", "--unset", "credential.helper"],
            # Configure AWS credential helper
            ["git", "config", "credential.helper", "!aws codecommit credential-helper $@"],
            ["git", "config", "credential.UseHttpPath", "true"],
            # Set user info
            ["git", "config", "user.name", "AWS CodeCommit"],
            ["git", "config", "user.email", "<EMAIL>"]
        ]

        for cmd in commands:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=repo_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
            
    except Exception as e:
        logger.error(f"AWS git configuration failed: {str(e)}")
        raise

async def setup_codecommit_remote(repo_url: str, source_directory: str) -> None:
    """Setup CodeCommit remote"""
    try:
        # Check if remote exists
        process = await asyncio.create_subprocess_exec(
            "git", "remote",
            cwd=source_directory,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, _ = await process.communicate()
        
        repo_url = modify_codecommit_repo_name(repo_url)

        if "origin" in stdout.decode():
            # Update existing remote
            process = await asyncio.create_subprocess_exec(
                "git", "remote", "set-url", "origin", repo_url,
                cwd=source_directory,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
        else:
            # Add new remote
            process = await asyncio.create_subprocess_exec(
                "git", "remote", "add", "origin", repo_url,
                cwd=source_directory,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
        
        await process.communicate()
            
    except Exception as e:
        logger.error(f"CodeCommit remote setup failed: {str(e)}")
        raise

async def trigger_codepipeline(pipeline_name: str, branch: str) -> Dict:
    """
    Trigger AWS CodePipeline execution
    
    Args:
        pipeline_name: Name of the CodePipeline
        branch: Branch that triggered the pipeline
    Returns:
        Dict with execution details
    """
    try:
        client = boto3.client('codepipeline')
        
        # Start pipeline execution
        response = client.start_pipeline_execution(
            name=pipeline_name
        )
        
        logger.info(f"Triggered pipeline: {pipeline_name}")
        return {
            "pipeline_name": pipeline_name,
            "execution_id": response['pipelineExecutionId'],
            "status": "InProgress",
            "branch": branch
        }
        
    except Exception as e:
        logger.error(f"Failed to trigger CodePipeline: {str(e)}")
        raise

async def get_pipeline_status(pipeline_name: str, execution_id: str) -> Dict:
    """
    Get AWS CodePipeline execution status
    
    Args:
        pipeline_name: Name of the CodePipeline
        execution_id: Pipeline execution ID
    Returns:
        Dict with execution status details
    """
    try:
        client = boto3.client('codepipeline')
        
        response = client.get_pipeline_execution(
            pipelineName=pipeline_name,
            pipelineExecutionId=execution_id
        )
        
        execution = response['pipelineExecution']
        
        return {
            "status": execution['status'],
            "start_time": execution['startTime'].isoformat(),
            "last_update_time": execution.get('lastUpdateTime', '').isoformat(),
            "pipeline_name": pipeline_name,
            "execution_id": execution_id
        }
        
    except Exception as e:
        logger.error(f"Failed to get pipeline status: {str(e)}")
        raise

import base64
async def create_codecommit_repository(
    repo_url: str, 
    region: str,
    branch_name: str = 'main', 
    description: str = None
) -> tuple[bool, str]:
    try:
        # Extract repository name
        repo_name = repo_url.split('/')[-1].replace('.git', '').replace('-experimental', '')
        new_repo_name = f"{repo_name}_deploy"
        new_url = modify_codecommit_repo_name(repo_url)
        
        # Base command with region
        base_cmd = ["aws", "codecommit", "--region", region]
        
        # Create repository if it doesn't exist
        process = await asyncio.create_subprocess_exec(
            *base_cmd, "get-repository",
            "--repository-name", new_repo_name,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            logger.info(f"Repository {new_repo_name} does not exist. Creating in region {region}...")
            cmd = [*base_cmd, "create-repository", "--repository-name", new_repo_name]
            if description:
                cmd.extend(["--repository-description", description])
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"Failed to create repository in region {region}: {stderr.decode()}")
                return False, new_url

        # Create initial commit with base64 encoded content
        initial_content = "# Initial Repository Setup\nDeployment repository"
        encoded_content = base64.b64encode(initial_content.encode()).decode()
        
        commit_cmd = [
            *base_cmd, "create-commit",
            "--repository-name", new_repo_name,
            "--branch-name", branch_name,
            "--author-name", "System",
            "--email", "<EMAIL>",
            "--commit-message", "Initial commit",
            "--put-files", json.dumps([{
                "filePath": "README.md",
                "fileContent": encoded_content,
                "fileMode": "NORMAL"
            }])
        ]
        
        commit_process = await asyncio.create_subprocess_exec(
            *commit_cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        commit_stdout, commit_stderr = await commit_process.communicate()
        
        if commit_process.returncode == 0:
            commit_response = json.loads(commit_stdout)
            commit_id = commit_response['commitId']
            
            # Create branch with the actual commit ID
            branch_cmd = [
                *base_cmd, "create-branch",
                "--repository-name", new_repo_name,
                "--branch-name", branch_name,
                "--commit-id", commit_id
            ]
            
            branch_process = await asyncio.create_subprocess_exec(
                *branch_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            branch_stdout, branch_stderr = await branch_process.communicate()
            
            if branch_process.returncode != 0:
                logger.warning(f"Failed to create branch in region {region}: {branch_stderr.decode()}")
        else:
            logger.warning(f"Failed to create initial commit in region {region}: {commit_stderr.decode()}")

        logger.info(f"Successfully created repository: {new_repo_name} in region {region}")
        return True, new_url
        
    except Exception as e:
        logger.error(f"Error creating CodeCommit repository in region {region}: {str(e)}")
        return False, new_url
    
async def push_to_codecommit(repo_path: str, branch: str, source_directory: str, force: bool = False) -> None:
    """
    Push changes to AWS CodeCommit with enhanced remote verification
    """
    try:
        # Configure AWS credentials
        await configure_aws_git_credentials(source_directory)
        
        # Verify git initialization
        init_process = await asyncio.create_subprocess_exec(
            "git", "init",
            cwd=source_directory,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        await init_process.communicate()
        
        # Log AWS identity before pushing
        process = await asyncio.create_subprocess_exec(
            "aws", "sts", "get-caller-identity",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        if process.returncode == 0:
            identity = stdout.decode().strip()
            logger.info(f"Current AWS identity: {identity}")
        else:
            logger.warning(f"Failed to get AWS identity: {stderr.decode()}")

        # Check existing remotes
        remote_process = await asyncio.create_subprocess_exec(
            "git", "remote", "-v",
            cwd=source_directory,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        remote_stdout, remote_stderr = await remote_process.communicate()
        logger.info(f"Existing remotes: {remote_stdout.decode()}")

        # Remove existing origin if any
        await asyncio.create_subprocess_exec(
            "git", "remote", "remove", "origin",
            cwd=source_directory,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        repo_path = modify_codecommit_repo_name(repo_path)
        # Setup remote with verification
        logger.info(f"Setting up remote with URL: {repo_path}")
        remote_add_process = await asyncio.create_subprocess_exec(
            "git", "remote", "add", "origin", repo_path,
            cwd=source_directory,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        _, remote_add_stderr = await remote_add_process.communicate()
        if remote_add_process.returncode != 0:
            raise Exception(f"Failed to add remote: {remote_add_stderr.decode()}")

        # Verify remote was added
        verify_process = await asyncio.create_subprocess_exec(
            "git", "remote", "-v",
            cwd=source_directory,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        verify_stdout, _ = await verify_process.communicate()
        logger.info(f"Verified remotes after setup: {verify_stdout.decode()}")

        # Add all files
        add_process = await asyncio.create_subprocess_exec(
            "git", "add", ".",
            cwd=source_directory,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        await add_process.communicate()

        # Create initial commit if needed
        commit_process = await asyncio.create_subprocess_exec(
            "git", "commit", "-m", "Initial commit",
            cwd=source_directory,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        await commit_process.communicate()

        # Checkout branch
        logger.info(f"Checking out branch: {branch}")
        checkout_process = await asyncio.create_subprocess_exec(
            "git", "checkout", "-B", branch,
            cwd=source_directory,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        _, checkout_stderr = await checkout_process.communicate()
        if checkout_process.returncode != 0:
            raise Exception(f"Checkout failed: {checkout_stderr.decode()}")

        # Push command with verbose output
        push_cmd = ["git", "push", "-v", "origin", branch]
        if force:
            push_cmd.append("--force")

        logger.info(f"Executing push command: {' '.join(push_cmd)}")
        process = await asyncio.create_subprocess_exec(
            *push_cmd,
            cwd=source_directory,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            logger.error(f"Push stderr: {stderr.decode()}")
            logger.error(f"Push stdout: {stdout.decode()}")
            raise Exception(f"Push failed: {stderr.decode()}")

        logger.info(f"Successfully pushed to CodeCommit branch: {branch}")

    except Exception as e:
        logger.error(f"CodeCommit push failed: {str(e)}")
        raise

async def clone_codecommit_repo(repo_url: str, local_path: str, branch: str, target_account_id: str = None) -> bool:
    """
    Clone a repository from AWS CodeCommit using AWS CLI credential helper.
    
    Args:
        repo_url (str): CodeCommit repository URL
        local_path (str): Local path to clone to
        branch (str): Branch to clone
        target_account_id (str): Target AWS account ID for cross-account access
    """
    try:
        aws_region = os.environ.get('AWS_DEFAULT_REGION', 'us-east-1')
        aws_region = 'us-east-1'
        target_account_id = os.environ.get('AWS_DEFAULT_ID', '762233764946_AdministratorAccess')
        # Modify URL for cross-account access if account ID is provided
        if target_account_id:
            # Extract repository name from original URL
            repo_name = repo_url.split('/')[-1].replace('.git', '')
            # Construct cross-account URL
            repo_url = f"codecommit::{aws_region}://{target_account_id}@{repo_name}"
            logger.info(f"Using cross-account URL: {repo_url}")

        # Ensure target directory doesn't exist
        if os.path.exists(local_path):
            shutil.rmtree(local_path)

        # Configure git to use AWS credential helper
        git_config_commands = [
            ['git', 'config', '--global', '--unset', 'credential.helper'],
            ['git', 'config', '--global', 'credential.helper', '!aws codecommit credential-helper $@'],
            ['git', 'config', '--global', 'credential.UseHttpPath', 'true']
        ]

        # Set up git configuration
        for cmd in git_config_commands:
            try:
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()
            except:
                pass

        logger.info(f"Attempting to clone repository to: {local_path} (branch: {branch})")

        # Clone repository
        process = await asyncio.create_subprocess_exec(
            'git', 'clone', '-b', branch, repo_url, local_path,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            env={
                **os.environ,
                'AWS_DEFAULT_REGION': aws_region
            }
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            error_msg = stderr.decode() if stderr else "Unknown error"
            logger.error(f"Clone failed: {error_msg}")
            
            # Try cloning without branch specification if branch doesn't exist
            if "Remote branch not found" in error_msg or "couldn't find remote ref" in error_msg:
                logger.info(f"Branch {branch} not found, attempting to clone default branch")
                
                if os.path.exists(local_path):
                    shutil.rmtree(local_path)
                
                process = await asyncio.create_subprocess_exec(
                    'git', 'clone', repo_url, local_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    env={
                        **os.environ,
                        'AWS_DEFAULT_REGION': aws_region
                    }
                )
                
                stdout, stderr = await process.communicate()
                
                if process.returncode != 0:
                    error_msg = stderr.decode() if stderr else "Unknown error"
                    logger.error(f"Clone failed with default branch: {error_msg}")
                    return False
                
                # Create and checkout the specified branch
                checkout_process = await asyncio.create_subprocess_exec(
                    'git', 'checkout', '-b', branch,
                    cwd=local_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                checkout_stdout, checkout_stderr = await checkout_process.communicate()
                if checkout_process.returncode != 0:
                    logger.error(f"Failed to create branch {branch}: {checkout_stderr.decode()}")
                    return False
            else:
                return False
            
        logger.info(f"Successfully cloned repository to {local_path}")
        return True

    except Exception as e:
        logger.error(f"Error cloning repository: {str(e)}")
        return False


async def commit_changes(repo_path: str, commit_message: str, working_dir: str) -> bool:
    """Commit changes with improved file tracking"""
    try:
        logger.info("Starting commit process...")
        
        # List all files in directory
        process = await asyncio.create_subprocess_exec(
            'ls', '-la',
            cwd=working_dir,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        logger.info(f"Directory contents:\n{stdout.decode()}")

        # Initialize repository if needed
        init_process = await asyncio.create_subprocess_exec(
            'git', 'init',
            cwd=working_dir,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        await init_process.communicate()

        # Force add all files
        add_process = await asyncio.create_subprocess_exec(
            'git', 'add', '-A', '-f',
            cwd=working_dir,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        add_stdout, add_stderr = await add_process.communicate()
        
        if add_process.returncode != 0:
            logger.error(f"Git add failed: {add_stderr.decode()}")
            return False

        # Check git status
        status_process = await asyncio.create_subprocess_exec(
            'git', 'status',
            cwd=working_dir,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        status_stdout, status_stderr = await status_process.communicate()
        logger.info(f"Git status:\n{status_stdout.decode()}")

        # Commit changes
        commit_process = await asyncio.create_subprocess_exec(
            'git', 'commit', '-m', commit_message,
            cwd=working_dir,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        commit_stdout, commit_stderr = await commit_process.communicate()
        
        if commit_process.returncode != 0 and "nothing to commit" not in commit_stderr.decode():
            logger.error(f"Commit failed: {commit_stderr.decode()}")
            return False

        logger.info(f"Commit completed: {commit_stdout.decode()}")
        return True

    except Exception as e:
        logger.error(f"Commit failed with exception: {str(e)}")
        return False

    
async def check_and_create_repo(github_token: str, github_username: str, repo_name: str) -> str:
    """
    Check if repository exists and create if it doesn't.
    Returns repository URL.
    """
    try:
        g = Github(github_token)
        user = g.get_user()

        try:
            repo = user.get_repo(f"{github_username}/{repo_name}")
            logger.info(f"Repository already exists: {repo.clone_url}")
        except GithubException:
            logger.info(f"Creating new repository: {repo_name}")
            repo = user.create_repo(
                name=repo_name,
                private=True,
                auto_init=False,
                description=f"Deployment repository"
            )
            logger.info(f"Successfully created repository: {repo.clone_url}")
        
        return f"https://github.com/{github_username}/{repo_name}.git"

    except Exception as e:
        logger.error(f"GitHub operation failed: {str(e)}")
        # Return default URL format instead of raising exception
        return f"https://github.com/{github_username}/{repo_name}.git"

async def get_file_from_codecommit(repo_name: str, file_path: str, branch: str) -> str:
    """
    Get file content directly from CodeCommit without cloning.
    """
    try:
        codecommit = boto3.client('codecommit', region_name='us-east-1')
        response = codecommit.get_file(
            repositoryName=repo_name,
            filePath=file_path,
            commitSpecifier=branch
        )
        
        content = response['fileContent']
        # Content is returned as bytes, decode to string
        if isinstance(content, bytes):
            return content.decode('utf-8')
        return content
        
    except codecommit.exceptions.FileDoesNotExistException:
        raise Exception(f"File {file_path} not found in repository")
    except Exception as e:
        raise Exception(f"Error reading file from CodeCommit: {str(e)}")

async def read_init_tool_content(repo_name: str, branch: str) -> dict:
    """
    Read and parse .init-run-tool file content with handling for line breaks.
    
    Args:
        repo_name: Repository name
        branch: Branch name
        
    Returns:
        dict: Parsed configuration
    """
    try:
        # Get file content
        content = await get_file_from_codecommit(
            repo_name=repo_name,
            file_path='.init-run-tool',
            branch=branch
        )
        
        # First try to parse the raw content
        try:
            config = json.loads(content)
        except json.JSONDecodeError:
            # Clean the content
            # Remove surrounding single quotes if present
            content = content.strip().strip("'")
            # Replace \r\n with forward slash in working directory path
            content = content.replace('\r\n', '/')
            # Remove any duplicate forward slashes
            content = content.replace('//', '/')
            config = json.loads(content)
        
        # Clean up working directory path
        if 'working_directory' in config:
            working_dir = config['working_directory']
            # Remove any trailing or leading slashes
            working_dir = working_dir.strip('/')
            # Handle any remaining line breaks
            working_dir = working_dir.replace('\r', '').replace('\n', '')
            # Ensure path uses forward slashes
            working_dir = working_dir.replace('\\', '/')
            # Reconstruct path with single forward slashes
            working_dir = '/' + '/'.join(filter(None, working_dir.split('/')))
            config['working_directory'] = working_dir
            
            # Get working directory
            full_path = config.get('working_directory', '')
            if not full_path:
                raise ValueError("No working_directory specified in .init-run-tool")
                
            # Extract path after repository name
            try:
                # Split path by repository name and get the part after it
                path_parts = full_path.split(repo_name)
                if len(path_parts) > 1:
                    relative_path = path_parts[1].strip('/')  # Remove leading/trailing slashes
                    relative_path = relative_path.split('\r')[0]  # Remove any carriage returns
                    relative_path = relative_path.split('\n')[0]  # Remove any newlines
                else:
                    # If repo name not found in path, use original path
                    relative_path = full_path
                    
                config['working_directory'] = relative_path
                logger.info(f"Extracted working directory: {relative_path}")
                
            except Exception as e:
                logger.error(f"Error extracting working directory: {str(e)}")
                raise
            logger.info(f"Successfully parsed .init-run-tool with working directory: {config.get('working_directory')}")
            return config
        
    except Exception as e:
        logger.error(f"Error reading .init-run-tool: {str(e)}")
        logger.error(f"Original content: {content}")
        raise Exception(f"Error reading .init-run-tool: {str(e)}")
    

async def trigger_amplify_deployment(app_id: str, branch_name: str):
    try:
        # Initialize Amplify client
        amplify_client = boto3.client('amplify', region_name='us-east-1')
        
        # Start the deployment
        response = amplify_client.start_job(
            appId=app_id,
            branchName=branch_name,
            jobType='RELEASE'
        )
        
        # Get job ID for tracking
        job_id = response['jobSummary']['jobId']
        
        print(f"Started deployment job: {job_id}")
        

    except Exception as e:
        print(f"Error triggering deployment: {str(e)}")
        raise