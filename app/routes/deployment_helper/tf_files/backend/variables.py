def generate_variable_config():
   variable_config = '''
variable "app_name" {
  description = "Name of the application used for naming resources"
  type        = string
  default     = "cosmetics-app-hjhk"  # You can override this default value
}

variable "environment" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "aws_region" {
  description = "AWS region to deploy resources"
  type        = string
  default     = "us-west-1"
}'''
   
   return variable_config