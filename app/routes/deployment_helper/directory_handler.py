from fastapi import APIRout<PERSON>, HTTPEx<PERSON>, Depends, Body
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional
import os
import logging
import subprocess
import json
from urllib.parse import urlparse
from datetime import datetime
from github import Github, GithubException
from github.Repository import Repository
from app.connection.establish_db_connection import get_node_db, NodeDB
from app.utils.auth_utils import get_current_user
import os
import logging
import yaml
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import tempfile
import shutil
import asyncio
from botocore.exceptions import ClientError
import boto3
import glob
# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(handler)
logger.setLevel(logging.INFO)


def setup_infrastructure_directory(target_directory: str) -> bool:
    """
    Sets up the infrastructure directory and configuration.
    
    Args:
        target_directory (str): Base directory path
        config (dict): Infrastructure configuration dictionary
    
    Returns:
        bool: True if setup successful, False otherwise
    """
    try:
        # Create infrastructure directory path
        infrastructure_path = os.path.join(target_directory, "infrastructure")
        
        # Create directory if it doesn't exist
        os.makedirs(infrastructure_path, exist_ok=True)
        

        print(f"Infrastructure directory setup complete at: {infrastructure_path}")
        return infrastructure_path
        
    except Exception as e:
        print(f"Error setting up infrastructure directory: {str(e)}")
        return False
    
def setup_workflows_directory(target_directory: str) -> bool:
    """
    Sets up the infrastructure directory and configuration.
    
    Args:
        target_directory (str): Base directory path
        config (dict): Infrastructure configuration dictionary
    
    Returns:
        bool: True if setup successful, False otherwise
    """
    try:
        # Create infrastructure directory path
        workflow_path = os.path.join(target_directory, ".github/workflows")
        
        # Create directory if it doesn't exist
        os.makedirs(workflow_path, exist_ok=True)
        

        print(f"Infrastructure directory setup complete at: {workflow_path}")
        return workflow_path
        
    except Exception as e:
        print(f"Error setting up infrastructure directory: {str(e)}")
        return False
    
def cleanup_terraform_files(directory: str) -> None:
    """
    Clean up only Terraform lock file and .tf files in the specified directory.
    
    Args:
        directory (str): Path to the directory to clean
        
    Returns:
        None
    """
    logger = logging.getLogger(__name__)
    
    try:
        if not os.path.exists(directory):
            logger.debug(f"Directory does not exist: {directory}")
            return

        # Files to remove
        patterns = [
            "*.tf",
            "*.tfvars",
            ".terraform.lock.hcl"
        ]
        
        files_removed = []
        for pattern in patterns:
            # Use glob to find files matching pattern
            matching_files = glob.glob(os.path.join(directory, pattern))
            for file_path in matching_files:
                try:
                    os.remove(file_path)
                    files_removed.append(os.path.basename(file_path))
                except PermissionError:
                    logger.warning(f"Permission denied when trying to remove: {file_path}")
                except OSError as e:
                    logger.warning(f"Error removing file {file_path}: {str(e)}")

        if files_removed:
            logger.debug(f"Removed Terraform files in {directory}: {', '.join(files_removed)}")
        else:
            logger.debug(f"No Terraform files found to remove in {directory}")

    except Exception as e:
        logger.error(f"Error during Terraform files cleanup: {str(e)}")
        raise


def setup_deployment_directory(base_path: str, cleanup_existing: bool = False) -> Optional[str]:
    """
    Set up deployment directory with proper cleanup handling.
    
    Args:
        base_path: Base directory path to create/manage
        cleanup_existing: Whether to clean up existing terraform files if directory exists
        
    Returns:
        str: Path to created/managed directory
        None: If directory creation fails
    """
    try:
        # Check if directory exists
        if os.path.exists(base_path):
            logger.info(f"Directory already exists: {base_path}")
            
            if cleanup_existing:
                # Clean up only terraform-related files
                cleanup_terraform_files(base_path)
                logger.info("Cleaned up existing terraform files")
        else:
            # Create new directory
            os.makedirs(base_path, exist_ok=True)
            logger.info(f"Created new directory: {base_path}")
            
        # Create required subdirectories
        subdirs = [
            os.path.join(base_path, 'infrastructure'),
            os.path.join(base_path, 'workspaces'),
            os.path.join(base_path, '.terraform')
        ]
        
        for subdir in subdirs:
            os.makedirs(subdir, exist_ok=True)
            logger.debug(f"Ensured subdirectory exists: {subdir}")
            
        return base_path
        
    except Exception as e:
        logger.error(f"Failed to set up deployment directory: {str(e)}")
        return None
