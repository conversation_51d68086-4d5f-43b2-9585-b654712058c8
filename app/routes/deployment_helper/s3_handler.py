from fastapi import APIRouter, HTTPException, Depends, Body
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional,List
import os
import logging
import subprocess 
import json
import asyncio
from urllib.parse import urlparse
from datetime import datetime
from github import Github, GithubException
import shutil
import boto3
from app.connection.establish_db_connection import get_node_db, NodeDB

from app.routes.deployment_helper.sample_tf import get_workflows  
from app.routes.repository_route import get_repository,list_branches
from app.routes.deployment_helper.node_helper import get_deployment_node
from app.routes.deployment_helper.directory_finder import find_application_directory
from app.routes.deployment_helper.aws_handler import modify_codecommit_repo_name,push_to_codecommit,clone_codecommit_repo,create_codecommit_repository
from app.routes.deployment_helper.get_tf_files import get_backend_terraform_files
import time
import random
import string
from fastapi.responses import StreamingResponse
from typing import AsyncGenerator, Union, Callable
from enum import Enum

# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(handler)
logger.setLevel(logging.INFO)

async def upload_repo_to_s3(repo_path: str, bucket: str, prefix: str, exclude_dirs: List[str] = ["infrastructure"]):
    """
    Upload repository to S3 excluding specified directories
    
    Args:
        repo_path (str): Local path to repository
        bucket (str): S3 bucket name
        prefix (str): S3 prefix (folder path)
        exclude_dirs (list): Directories to exclude
    """
    try:
        s3_client = boto3.client('s3')
        
        # Walk through the directory
        for root, dirs, files in os.walk(repo_path):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if d not in exclude_dirs]
            
            for file in files:
                # Get the full path of the file
                file_path = os.path.join(root, file)
                
                # Calculate relative path for S3 key
                relative_path = os.path.relpath(file_path, repo_path)
                s3_key = f"{prefix}/repo/{relative_path}"
                
                try:
                    # Upload file
                    s3_client.upload_file(file_path, bucket, s3_key)
                    logger.info(f"Uploaded {relative_path} to {s3_key}")
                except Exception as e:
                    logger.error(f"Failed to upload {file_path}: {str(e)}")
                    
        return True
    except Exception as e:
        logger.error(f"Error uploading repo to S3: {str(e)}")
        return False
    
async def save_terraform_files_to_s3(plan_output: str, tf_output: dict, bucket: str, prefix: str, region: str = 'us-east-2'):
    """
    Save Terraform plan and output files to S3
    
    Args:
        plan_output (str): Terraform plan output
        tf_output (dict): Terraform output
        bucket (str): S3 bucket name
        prefix (str): S3 prefix path
        region (str): AWS region name (default: 'us-east-1')
    
    Returns:
        dict: Dictionary containing the S3 keys of saved files
        None: If an error occurs
    """
    try:
        # Initialize S3 client with specified region
        s3_client = boto3.client('s3', region_name=region)
        
        # Try to create bucket directly without checking existence
        try:
            if region == 'us-east-1':
                s3_client.create_bucket(Bucket=bucket)
            else:
                s3_client.create_bucket(
                    Bucket=bucket,
                    CreateBucketConfiguration={
                        'LocationConstraint': region
                    }
                )
            logger.info(f"Created bucket {bucket} in region {region}")
        except s3_client.exceptions.ClientError as e:
            error_code = e.response['Error']['Code']
            # Ignore BucketAlreadyOwnedByYou and BucketAlreadyExists errors
            if error_code not in ['BucketAlreadyOwnedByYou', 'BucketAlreadyExists', '409']:
                if error_code in ['AccessDenied', 'Forbidden', '403']:
                    logger.warning(f"No permission to create bucket {bucket}. Attempting to use existing bucket.")
                else:
                    logger.error(f"Unexpected error during bucket creation: {str(e)}")
                    raise e

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save plan output
        plan_key = f"{prefix}/terraform_plan_{timestamp}.txt"
        s3_client.put_object(
            Bucket=bucket,
            Key=plan_key,
            Body=plan_output.encode('utf-8')
        )
        logger.info(f"Saved terraform plan to {plan_key}")
        
        # Save terraform output
        output_key = f"{prefix}/terraform_output_{timestamp}.json"
        s3_client.put_object(
            Bucket=bucket,
            Key=output_key,
            Body=json.dumps(tf_output, indent=2).encode('utf-8')
        )
        logger.info(f"Saved terraform output to {output_key}")
        
        return {
            "plan_file": plan_key,
            "output_file": output_key
        }
    except Exception as e:
        logger.error(f"Error saving terraform files to S3: {str(e)}")
        return None