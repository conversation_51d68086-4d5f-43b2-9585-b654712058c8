from fastapi import APIR<PERSON><PERSON>, HTTPException, Depends
from fastapi.responses import JSONResponse
from typing import Dict, Any, Literal
import json
from datetime import datetime


# Import backend helpers
from app.routes.deployment_helper.tf_files.backend.alb import generate_alb_config
from app.routes.deployment_helper.tf_files.backend.ecs import generate_ecs_config
from app.routes.deployment_helper.tf_files.backend.iam import generate_iam_config
from app.routes.deployment_helper.tf_files.backend.outputs import generate_output_config
from app.routes.deployment_helper.tf_files.backend.pipeline import generate_pipeline_config
from app.routes.deployment_helper.tf_files.backend.providers import generate_provider_config
from app.routes.deployment_helper.tf_files.backend.security import generate_security_groups_config
from app.routes.deployment_helper.tf_files.backend.vpc import generate_vpc_config
from app.routes.deployment_helper.tf_files.backend.variables import generate_variable_config


def get_frontend_tf_files(repo_name,app_name,branch,working_directory,technology) -> dict:
    """
    Generate Terraform configuration files including main.tf and terraform.tfvars.
    
    Args:
        params (dict): Dictionary containing:
            - app_name (str): Base name of the application
            - branch_name (str): Git branch name
            - account_id (str): AWS account ID
            - repository_name (str): CodeCommit repository name
            
    Returns:
        dict: Dictionary containing file contents for main.tf and terraform.tfvars
    """
    base_directory = get_base_directory(technology)
    # Main Terraform configuration
    main_tf = """# variables.tf
variable "app_name" {
  description = "Base name of the application"
  type        = string
}

variable "branch_name" {
  description = "Name of the branch to deploy"
  type        = string
}

variable "account_id" {
  description = "AWS Account ID"
  type        = string
}

variable "repository_name" {
  description = "Name of the CodeCommit repository"
  type        = string
}

variable "working_directory" {
  description = "Name of the working directory"
  type        = string
}

variable "base_directory" {
  description = "Name of the base directory"
  type        = string
}


# main.tf
resource "aws_amplify_app" "app" {
  provider   = aws.east1
  name       = "${var.app_name}"
  repository = "https://git-codecommit.us-east-1.amazonaws.com/v1/repos/${var.repository_name}"
  enable_branch_auto_build = true
  iam_service_role_arn = aws_iam_role.amplify_role.arn

  build_spec = <<-EOT
    version: 1
    frontend:
      phases:
        preBuild:
          commands:
            - cd ${var.working_directory}
            - npm ci
        build:
          commands:
            - npm run build
      artifacts:
        baseDirectory: ${var.working_directory}/${var.base_directory}
        files:
          - '**/*'
      cache:
        paths:
          - frontend/node_modules/**/*
  EOT

  custom_rule {
    source = "/<*>"
    status = "404"
    target = "/index.html"
  }
}

resource "aws_amplify_branch" "main" {
  provider    = aws.east1
  app_id      = aws_amplify_app.app.id
  branch_name = var.branch_name
  stage       = "PRODUCTION"
  enable_auto_build = true
}

resource "aws_iam_role" "amplify_role" {
  provider = aws.east1
  name     = "amplify-role-${var.app_name}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "amplify.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "amplify_codecommit_policy" {
  name = "amplify-codecommit-policy-${var.app_name}"
  role = aws_iam_role.amplify_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "codecommit:BatchGet*",
          "codecommit:BatchDescribe*",
          "codecommit:List*",
          "codecommit:Get*",
          "codecommit:Describe*",
          "codecommit:GitPull",
          "codecommit:CancelUploadArchive",
          "codecommit:GetBranch",
          "codecommit:GetCommit",
          "codecommit:GetRepository",
          "codecommit:GetUploadArchiveStatus",
          "codecommit:UploadArchive"
        ]
        Resource = [
          "arn:aws:codecommit:us-east-1:${var.account_id}:${var.repository_name}",
          aws_amplify_app.app.arn
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "codecommit:ListRepositories",
          "codecommit:ListBranches",
          "codecommit:GetFile"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy" "amplify_cross_account" {
  name = "amplify-cross-account-policy-${var.app_name}"
  role = aws_iam_role.amplify_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sts:AssumeRole"
        ]
        Resource = "*"
      }
    ]
  })
}

# outputs.tf
output "app_id" {
  value = aws_amplify_app.app.id
}

output "app_default_domain" {
  value = aws_amplify_app.app.default_domain
}

# provider.tf
provider "aws" {
  alias  = "east1"
  region = "us-east-1"
}"""

    # Generate terraform.tfvars content
    tfvars = f"""app_name        = "{app_name}"
branch_name     = "{branch}"
account_id      = "************"
repository_name = "{repo_name}"
working_directory = "{working_directory}"
base_directory = "{base_directory}"
"""

    return {
        "main_tf": main_tf,
        "terraform_tfvars": tfvars
    }

def get_backend_terraform_files(technology: str, repo_name: str, branch: str,app_name:str) -> Dict[str, str]:
    """Generate backend Terraform configurations."""
    # Get configurations from helper files
    account_id = "************"
    buildspec = f'''version: 0.2

phases:
  pre_build:
    commands:
      - aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin {account_id}.dkr.ecr.us-east-2.amazonaws.com
      - REPOSITORY_URI={account_id}.dkr.ecr.us-east-2.amazonaws.com/{app_name}
      - IMAGE_TAG=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
  build:
    commands:
      - docker build -t $REPOSITORY_URI:latest .
      - docker tag $REPOSITORY_URI:latest $REPOSITORY_URI:$IMAGE_TAG
  post_build:
    commands:
      - echo Pushing Docker image...
      - docker push $REPOSITORY_URI:latest
      - docker push $REPOSITORY_URI:$IMAGE_TAG
      - echo Creating image definitions file...
      - printf '[{{"name":"{app_name}","imageUri":"%s:%s"}}]' $REPOSITORY_URI $IMAGE_TAG > imageDefinitions.json
      - cat imageDefinitions.json

artifacts:
  files:
    - imageDefinitions.json
  base-directory: '.'
'''
    main_tf = '''
# Provider Configuration
provider "aws" {
  region = "us-east-2"
}
# VPC Configuration
resource "aws_vpc" "app_vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name = "${var.app_name}-vpc"
  }
}

# Public Subnet
resource "aws_subnet" "public_subnet" {
  vpc_id                  = aws_vpc.app_vpc.id
  cidr_block              = "********/24"
  availability_zone       = "us-east-2b"

  map_public_ip_on_launch = true

  tags = {
    Name = "${var.app_name}-public-subnet"
  }
}

resource "aws_subnet" "public_subnet_2" {
  vpc_id                  = aws_vpc.app_vpc.id
  cidr_block              = "********/24"
  availability_zone       = "us-east-2c"

  map_public_ip_on_launch = true

  tags = {
    Name = "${var.app_name}-public-subnet-2"
  }
}

# Internet Gateway
resource "aws_internet_gateway" "app_igw" {
  vpc_id = aws_vpc.app_vpc.id

  tags = {
    Name = "${var.app_name}-igw"
  }
}

# Route Table
resource "aws_route_table" "public_rt" {
  vpc_id = aws_vpc.app_vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.app_igw.id
  }

  tags = {
    Name = "${var.app_name}-public-rt"
  }
}



# Route Table Association
resource "aws_route_table_association" "public_rt_assoc" {
  subnet_id      = aws_subnet.public_subnet.id
  route_table_id = aws_route_table.public_rt.id
}

resource "aws_route_table_association" "public_rt_assoc_2" {
  subnet_id      = aws_subnet.public_subnet_2.id
  route_table_id = aws_route_table.public_rt.id
}


# Security Group
resource "aws_security_group" "app_sg" {
  name        = "${var.app_name}-sg"
  description = "Security group for chat app"
  vpc_id      = aws_vpc.app_vpc.id

  ingress {
    from_port   = 3000
    to_port     = 3000
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

# ECR Repository
resource "aws_ecr_repository" "app_repo" {
  name = var.app_name
}

# ECS Cluster
resource "aws_ecs_cluster" "app_cluster" {
  name = "${var.app_name}-cluster"
}

# ECS Task Execution Role
resource "aws_iam_role" "ecs_task_execution_role" {
  name = "${var.app_name}-ecs-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })
}

# Attach ECS Task Execution Policy
resource "aws_iam_role_policy_attachment" "ecs_task_execution_role_policy" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# Task Definition
resource "aws_ecs_task_definition" "app_task" {
  family                   = var.app_name
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = "256"
  memory                   = "512"
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn  # Add this line

  container_definitions = jsonencode([
    {
      name  = var.app_name
      image = "${aws_ecr_repository.app_repo.repository_url}:latest"
      portMappings = [
        {
          containerPort = 3000
          hostPort      = 3000
          protocol      = "tcp"
        }
      ]
    }
  ])
}


# CodeBuild IAM Role
resource "aws_iam_role" "codebuild_role" {
  name = "${var.app_name}-codebuild-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "codebuild.amazonaws.com"
        }
      }
    ]
  })
}

# CodeBuild Policy
resource "aws_iam_role_policy" "codebuild_policy" {
  name = "${var.app_name}-codebuild-policy"
  role = aws_iam_role.codebuild_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Resource = ["*"]
        Action = [
          "ecr:BatchCheckLayerAvailability",
          "ecr:CompleteLayerUpload",
          "ecr:GetAuthorizationToken",
          "ecr:InitiateLayerUpload",
          "ecr:PutImage",
          "ecr:UploadLayerPart",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "s3:PutObject",
          "s3:GetObject",
          "s3:GetObjectVersion",
          "s3:GetBucketAcl",
          "s3:GetBucketLocation",
          "ecs:DescribeTaskDefinition",
          "ecs:DescribeServices",
          "ecs:UpdateService",
          "ecs:RegisterTaskDefinition",
          "iam:PassRole"
        ]
      }
    ]
  })
}

# CodePipeline IAM Role
resource "aws_iam_role" "codepipeline_role" {
  name = "${var.app_name}-codepipeline-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "codepipeline.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# CodePipeline Policy
resource "aws_iam_role_policy" "codepipeline_policy" {
  name = "${var.app_name}-codepipeline-policy"
  role = aws_iam_role.codepipeline_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Resource = ["*"]
        Action = [
          "codecommit:CancelUploadArchive",
          "codecommit:GetBranch",
          "codecommit:GetCommit",
          "codecommit:GetUploadArchiveStatus",
          "codecommit:UploadArchive",
          "codecommit:GitPull",
          "codebuild:BatchGetBuilds",
          "codebuild:StartBuild",
          "ecs:DescribeServices",
          "ecs:DescribeTaskDefinition",
          "ecs:DescribeTasks",
          "ecs:ListTasks",
          "ecs:RegisterTaskDefinition",
          "ecs:UpdateService",
          "iam:PassRole",
          "s3:GetObject",
          "s3:GetObjectVersion",
          "s3:GetBucketVersioning",
          "s3:PutObject"
        ]
      }
    ]
  })
}

# S3 Bucket for artifacts
resource "aws_s3_bucket" "artifact_store" {
  bucket = "${var.app_name}-artifact-store-${data.aws_caller_identity.current.account_id}"
}

resource "aws_s3_bucket_versioning" "artifact_store" {
  bucket = aws_s3_bucket.artifact_store.id
  versioning_configuration {
    status = "Enabled"
  }
}

# CodeBuild Project
resource "aws_codebuild_project" "app" {
  name          = "${var.app_name}-build"
  description   = "Builds chat app Docker image"
  service_role  = aws_iam_role.codebuild_role.arn

  artifacts {
    type = "CODEPIPELINE"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/standard:5.0"
    type                        = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = true

    environment_variable {
      name  = "AWS_ACCOUNT_ID"
      value = data.aws_caller_identity.current.account_id
    }
  }

  source {
    type      = "CODEPIPELINE"
    buildspec = "buildspec.yml"
  }
}

# CodePipeline
resource "aws_codepipeline" "app" {
  name     = "${var.app_name}-pipeline"
  role_arn = aws_iam_role.codepipeline_role.arn

  artifact_store {
    location = aws_s3_bucket.artifact_store.bucket
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      name             = "Source"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["source_output"]

      configuration = {
        RepositoryName = var.repository_name
        BranchName     = var.branch_name
      }
    }
  }

  stage {
    name = "Build"

    action {
      name            = "Build"
      category        = "Build"
      owner           = "AWS"
      provider        = "CodeBuild"
      input_artifacts = ["source_output"]
      output_artifacts = ["build_output"]  # Add this line
      version         = "1"

      configuration = {
        ProjectName = aws_codebuild_project.app.name
      }
    }
  }

  stage {
    name = "Deploy"

    action {
      name            = "Deploy"
      category        = "Deploy"
      owner           = "AWS"
      provider        = "ECS"
      input_artifacts = ["build_output"]
      version         = "1"

      configuration = {
        ClusterName = aws_ecs_cluster.app_cluster.name
        ServiceName = aws_ecs_service.app_service.name
        FileName    = "imageDefinitions.json"
      }
    }
  }
}

# Get current account ID
data "aws_caller_identity" "current" {}

# ALB Security Group
resource "aws_security_group" "alb" {
  name        = "${var.app_name}-alb-sg"
  description = "Security group for ALB"
  vpc_id      = aws_vpc.app_vpc.id

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

# Update ECS Security Group to accept traffic from ALB
resource "aws_security_group_rule" "ecs_from_alb" {
  type                     = "ingress"
  from_port                = 3000
  to_port                  = 3000
  protocol                 = "tcp"
  source_security_group_id = aws_security_group.alb.id
  security_group_id        = aws_security_group.app_sg.id
}

# Application Load Balancer
resource "aws_lb" "app" {
  name               = "${var.app_name}-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets            = [aws_subnet.public_subnet.id, aws_subnet.public_subnet_2.id]
}


# Target Group
resource "aws_lb_target_group" "app" {
  name        = "${var.app_name}-tg"
  port        = 3000
  protocol    = "HTTP"
  vpc_id      = aws_vpc.app_vpc.id
  target_type = "ip"

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    matcher            = "200,302,404"  # Accept more response codes
    path               = "/"
    port               = "traffic-port"
    protocol           = "HTTP"
    timeout            = 10             # Increased timeout
    unhealthy_threshold = 5             # More attempts before marking unhealthy
  }
}

# Listener
resource "aws_lb_listener" "app" {
  load_balancer_arn = aws_lb.app.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.app.arn
  }
}

resource "aws_ecs_service" "app_service" {
  name            = "${var.app_name}-service"
  cluster         = aws_ecs_cluster.app_cluster.id
  task_definition = aws_ecs_task_definition.app_task.arn
  desired_count   = 1
  launch_type     = "FARGATE"

  network_configuration {
    subnets          = [aws_subnet.public_subnet.id, aws_subnet.public_subnet_2.id]
    security_groups  = [aws_security_group.app_sg.id]
    assign_public_ip = true
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.app.arn
    container_name   = var.app_name
    container_port   = 3000
  }

  # Add this depends_on block
  depends_on = [aws_lb_listener.app]
}

output "alb_dns_name" {
 value = aws_lb.app.dns_name
}

variable "app_name" {
  description = "Name of the application"
  type        = string
}

variable "branch_name" {
  description = "Name of the branch to deploy"
  type        = string
}

variable "repository_name" {
  description = "Name of the repository"
  type        = string
}

variable "account_id" {
  description = "AWS Account ID"
  type        = string
}

variable "aws_region" {
  description = "AWS region"
  type        = string
}
'''
# Generate terraform.tfvars content
    tfvars = f"""app_name        = "{app_name}"
branch_name     = "{branch}"
account_id      = "************"
repository_name = "{repo_name}"
aws_region = "us-east-2"

"""

    return {
        "main_tf": main_tf,
        "terraform_tfvars": tfvars,  
        "dockerfile": get_dockerfile_content(technology),
        "docker_compose": get_docker_compose_content(technology),
        "buildspec_yml":buildspec
    }

def get_base_directory(technology: str) -> str:
    """Get the appropriate base directory for build artifacts based on technology."""
    return {
        "next": ".next",
        "react": "build",
        "vue": "dist",
        "angular": "dist"
    }.get(technology.lower(), "build")

def get_additional_cache_paths(technology: str) -> str:
    """Get additional cache paths based on technology."""
    return {
        "next": "- .next/cache/**/*",
        "react": "",
        "vue": "",
        "angular": ""
    }.get(technology.lower(), "")

def get_dockerfile_content(technology: str) -> str:
    """Get Dockerfile content based on technology."""
    if "python" in technology.lower():
        return """FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["python", "app.py"]"""
    elif "node" in technology.lower():
        return """FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "start"]"""
    return ""

def get_docker_compose_content(technology: str) -> str:
    """Get docker-compose content based on technology."""
    if "python" in technology.lower():
        return """version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - .:/app"""
    elif "node" in technology.lower():
        return """version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules"""
    return ""