from app.connection.establish_db_connection import get_node_db, NodeDB
from fastapi import APIRouter, Depends, HTTPException


async def get_deployment_node(
        project_id: int,
    container_id: int,
    db = Depends(get_node_db)
):
    # Get system context and container
        system_context = await db.get_child_nodes(project_id, "SystemContext")
        if not system_context:
            raise ValueError("System context not found")
        
        system_context = system_context[0]
        container = await db.get_node_by_id(container_id)
        if not container:
            raise HTTPException(status_code=404, detail="Container not found")
            
        # Get deployment node
        deployment_nodes = await db.get_child_nodes(container['id'], "Deployment")
        if not deployment_nodes:
            raise HTTPException(status_code=404, detail="Deployment configuration not found")
        
        deployment_node = deployment_nodes[0]

        return deployment_node