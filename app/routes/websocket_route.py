# routes/websocket.py
import json
import uuid
from app.core.websocket.client import WebSocketClient
from app.knowledge.redis_kg import getRedisKnowledge
from app.utils.auth_utils import decode_token
from app.utils.cost_utils import check_free_credit_limits_crossed
from app.utils.kg_inspect.answer_question import answer_question
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from typing import Any, Dict
from app.connection.tenant_middleware import tenant_context

# Create router
router = APIRouter()

# Connection manager handles multiple active connections
class ConnectionManager:
    def __init__(self):
        # Store active connections with IDs
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_counter = 0
    
    async def connect(self, websocket: WebSocket) -> str:
        # Accept the connection
        await websocket.accept()
        # Generate unique connection ID
        connection_id = str(self.connection_counter)
        self.connection_counter += 1
        # Store the connection
        self.active_connections[connection_id] = websocket
        return connection_id
    
    def disconnect(self, connection_id: str) -> None:
        # Remove the connection
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
    
    async def send_personal_message(self, message: str, connection_id: str) -> None:
        # Send message to a specific client
        if connection_id in self.active_connections:
            await self.active_connections[connection_id].send_text(message)
    
    async def broadcast(self, message: str, exclude: str = None) -> None:
        # Send message to all connected clients
        for connection_id, connection in self.active_connections.items():
            # Skip the excluded connection if specified
            if exclude and connection_id == exclude:
                continue
            await connection.send_text(message)


# Create a connection manager instance
manager = ConnectionManager()

class Reporter:
    def __init__(self, connection_id: str):
        """
        Initialize the Reporter to handle communications with WebSocket clients.
        
        Args:
            connection_id: The unique identifier of the WebSocket connection
        """
        self.connection_id = connection_id
        self.is_answering_question = True
    
    async def send_message(self, message_type: str, data: Dict[str, Any]) -> None:
        """
        Format the message and send it to the WebSocket client through the manager.
        
        Args:
            message_type: Type of message being sent (e.g., 'code_query')
            data: Dictionary containing message content and metadata
        """
        message = {
            "type": message_type,
            "task_id": self.connection_id,
            "data": data
        }
        print("MESSAGE :")
        print(message)
        # Convert the message to JSON and send through the connection manager
        await manager.send_personal_message(json.dumps(message), self.connection_id)
        
@router.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    # Connect to the WebSocket
    connection_id = await manager.connect(websocket)
    reporter = Reporter(connection_id)
    
    try:
        # Notify about new connection
        # await manager.broadcast(f"Client #{client_id} joined the chat")
        
        # Main WebSocket loop
        while True:
            # Receive message from client
            data_str = await websocket.receive_text()
            
            # Parse the JSON data
            try:
                data = json.loads(data_str)
                tenant_id = data.get('tenant_id')
                session_id = data.get('session_id')
                message_uuid = str(uuid.uuid4())
                discussion_id = data.get('discussion_id')
                
                if await check_free_credit_limits_crossed(tenant_id, data.get('user_id', '')):
                    await reporter.send_message("credits_limit_crossed", {"status": 402})
            
                elif isinstance(data, dict) and 'input' in data:
                    await reporter.send_message("input_received", {"message": data})
                        
                    await reporter.send_message("code_query", {"message": 'Thinking...', 'message_uuid': message_uuid})
                
                    auth_token = data.get('auth_token')
                    user_prompt = data.get('input')
                    project_id = data.get('project_id')
                    user_id = data.get('user_id')
                    
                    
                    tenant_context.set(tenant_id)
                    user_details = decode_token(auth_token)
                    if "Error" in user_details:
                        await manager.send_personal_message(json.dumps({
                            "type": "error",
                            "discussion_id": None,
                            "message": "Invalid token found"
                        }), connection_id)
                    else:
                        print(f"User details: {user_details}")
                        actual_username = user_details['custom:Name']
                    
                        # Get the async generator from answer_question
                        answer_generator = answer_question(
                            reporter=reporter,
                            session_id=session_id,
                            project_id=project_id,
                            user_id=user_id,
                            question=user_prompt,
                            message_uuid=message_uuid,
                            actual_username=actual_username,
                            discussion_id=discussion_id
                        )
                        
                        async for _ in answer_generator:
                            pass 

                else:
                    # Handle invalid message format
                    await manager.send_personal_message(json.dumps({
                        "type": "error",
                        "discussion_id": None,
                        "message": "Invalid message format"
                    }), connection_id)
            except json.JSONDecodeError:
                # Handle invalid JSON
                await manager.send_personal_message(json.dumps({
                    "type": "error",
                    "message": "Invalid JSON format"
                }), connection_id)
                
    except WebSocketDisconnect:
        # Handle client disconnection
        manager.disconnect(connection_id)
