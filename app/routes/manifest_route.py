from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any, List, Optional, get_origin, get_args
from dataclasses import fields, is_dataclass
from enum import Enum
import json

from code_generation_core_agent.project_schemas import (
    ProjectSchema, ProjectOverview, Container, ContainerType
)

_SHOW_NAME = "manifest"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

def get_field_type_config(field_info) -> Dict[str, Any]:
    """Map dataclass field to form field configuration"""
    field_type = field_info.type
    field_name = field_info.name
    
    # Get description from metadata
    description = field_info.metadata.get("metadata", {}).get("description", "")
    
    # Handle Optional types
    if get_origin(field_type) is type(Optional[str]):
        field_type = get_args(field_type)[0]
    
    # Handle Union types (Optional)
    origin = get_origin(field_type)
    if origin is not None:
        args = get_args(field_type)
        if len(args) == 2 and type(None) in args:
            field_type = args[0] if args[1] is type(None) else args[1]
    
    base_config = {
        "label": field_name.replace("_", " ").title(),
        "placeholder": description or f"Enter {field_name.replace('_', ' ')}"
    }
    
    # Handle different field types
    if field_type == str:
        if "description" in field_name.lower() or "command" in field_name.lower():
            return {"type": "textarea", **base_config, "rows": 3}
        return {"type": "text", **base_config}
    
    elif field_type == int:
        return {"type": "number", **base_config}
    
    elif hasattr(field_type, '__mro__') and Enum in field_type.__mro__:
        return {
            "type": "select", 
            **base_config,
            "options": [e.value for e in field_type],
            "required": True
        }
    
    elif get_origin(field_type) is list:
        inner_type = get_args(field_type)[0] if get_args(field_type) else str
        return {
            "type": "array",
            **base_config,
            "item_type": "text",
            "placeholder": f"Add {field_name.replace('_', ' ')}"
        }
    
    elif field_type == dict or get_origin(field_type) is dict:
        return {
            "type": "object",
            **base_config,
            "placeholder": "Key-value pairs"
        }
    
    else:
        return {"type": "text", **base_config}

def generate_dataclass_config(dataclass_type) -> Dict[str, Any]:
    """Generate form config from dataclass"""
    config = {}
    
    for field_info in fields(dataclass_type):
        # Skip private fields
        if field_info.name == 'private':
            continue
            
        # Skip complex nested objects for now (can be extended)
        if is_dataclass(field_info.type):
            continue
            
        config[field_info.name] = get_field_type_config(field_info)
    
    return config

def generate_form_config() -> Dict[str, Any]:
    """Generate complete form configuration from ProjectSchema"""
    
    # Generate overview config
    overview_config = generate_dataclass_config(ProjectOverview)
    
    # Generate container config  
    container_config = generate_dataclass_config(Container)
    
    # Set required fields
    overview_config["project_name"]["required"] = True
    overview_config["description"]["required"] = True
    container_config["container_name"]["required"] = True
    container_config["description"]["required"] = True
    container_config["framework"]["required"] = True
    
    return {
        "overview": overview_config,
        "containers": {
            "type": "dynamic_array",
            "label": "Containers",
            "min_items": 1,
            "item_schema": container_config
        }
    }

def project_schema_to_form_data(schema: ProjectSchema) -> Dict[str, Any]:
    """Convert ProjectSchema instance to form data"""
    
    def dataclass_to_dict(obj):
        if obj is None:
            return {}
        result = {}
        for field_info in fields(obj):
            if field_info.name == 'private':
                continue
            value = getattr(obj, field_info.name)
            if isinstance(value, Enum):
                result[field_info.name] = value.value
            elif is_dataclass(value):
                result[field_info.name] = dataclass_to_dict(value)
            elif isinstance(value, list) and value and is_dataclass(value[0]):
                result[field_info.name] = [dataclass_to_dict(item) for item in value]
            else:
                result[field_info.name] = value
        return result
    
    overview_data = dataclass_to_dict(schema.overview) if schema.overview else {}
    containers_data = [dataclass_to_dict(container) for container in schema.containers]
    
    return {
        "overview": overview_data,
        "containers": containers_data
    }

def get_default_values(project_id: int) -> Dict[str, Any]:
    """Get current values for the project"""
    # TODO: Load actual ProjectSchema from database/file
    # For now, create a sample schema
    schema = ProjectSchema(
        overview=ProjectOverview(
            project_name=f"project_{project_id}",
            description="Sample project description",
            third_party_services=["Firebase"]
        ),
        containers=[
            Container(
                container_name="frontend",
                description="React frontend application", 
                container_type=ContainerType.FRONTEND,
                interfaces="HTTP REST API",
                framework="React",
                port="3000",
                buildCommand="npm run build",
                startCommand="npm start",
                installCommand="npm install"
            )
        ]
    )
    
    return project_schema_to_form_data(schema)

@router.get("/form-config/{project_id}/")
async def get_manifest_form_config(project_id: int):
    """Get form configuration for dynamic manifest editor"""
    try:
        form_config = generate_form_config()
        current_values = get_default_values(project_id)
        
        return {
            "form_config": form_config,
            "current_values": current_values
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating form config: {str(e)}")

@router.post("/form-data/{project_id}/")
async def save_manifest_from_form(project_id: int, form_data: Dict[str, Any]):
    """Save manifest from form data"""
    try:
        # TODO: Convert form_data back to ProjectSchema and save
        print(f"Saving form data for project {project_id}:", form_data)
        return {"message": "Manifest saved successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error saving manifest: {str(e)}")