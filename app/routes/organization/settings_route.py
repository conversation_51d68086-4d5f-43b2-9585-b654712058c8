from fastapi import APIRouter
from app.models.tenant.settings_model import TenantSettings, TenantSettingsModel
from app.core.Settings import settings
from app.connection.establish_db_connection import get_mongo_db
from fastapi import HTTPException
from app.connection.tenant_middleware import get_tenant_id
from app.models.organization_models import Organization
from fastapi import Depends
from app.utils.auth_utils import get_current_user
from app.utils.hash import encrypt_string, decrypt_string

router = APIRouter(
    prefix="/settings",
    tags=["settings"]
)

def get_admin_user(current_user = Depends(get_current_user)):
    if not current_user.get("custom:is_admin",'false') == "true":
        raise HTTPException(status_code=403, detail="Access denied for this organization")
    return current_user

def secure_response(data: dict, secure_fields: list) -> dict:
    """
    Encrypts specified fields in the data dictionary, including nested fields.
    Supports dot notation for nested fields (e.g. "topfield.innerfield.childfield")
    
    Args:
        data: Dictionary containing data to encrypt
        secure_fields: List of field names to encrypt, can include dot notation
        
    Returns:
        Dictionary with specified fields encrypted
    """
    def encrypt_nested(obj, path=""):
        if isinstance(obj, dict):
            result = {}
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                
                # Check if current path matches any secure field pattern
                should_encrypt = any(
                    secure_field == current_path or 
                    secure_field.startswith(f"{current_path}.")
                    for secure_field in secure_fields
                )
                
                if should_encrypt and isinstance(value, str):
                    result[key] = encrypt_string(value)
                elif isinstance(value, (dict, list)):
                    result[key] = encrypt_nested(value, current_path)
                else:
                    result[key] = value
            return result
        elif isinstance(obj, list):
            return [encrypt_nested(item, path) for item in obj]
        return obj

    return encrypt_nested(data)

def secure_settings_response(data: TenantSettingsModel) -> TenantSettingsModel:
    """
    Securely encrypt sensitive fields in settings response
    """
    # Extract secure field names from integrations
    if data.integrations and data.integrations.figma:
        for field in data.integrations.figma:
            if field.secure:
                field.value = encrypt_string(field.value)
    
    return data

@router.get("/")
async def get_settings(current_user = Depends(get_admin_user)):
    tenant_id = get_tenant_id()
    settings = await TenantSettings.get_settings(tenant_id)
    return secure_settings_response(settings)

@router.post("/")
async def update_settings(settings: TenantSettingsModel, current_user = Depends(get_admin_user)):
    tenant_id = get_tenant_id()
    await TenantSettings.update_settings(tenant_id, settings)
    return {"message": "Settings updated successfully"}
