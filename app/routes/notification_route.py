from fastapi import APIRouter, HTTPException
import boto3
from typing import Optional, Dict, Any, List
from pydantic import BaseModel
from app.repository.mongodb.device_token_repository import DeviceTokenRepository
from app.connection.tenant_middleware import get_tenant_id
from app.connection.establish_db_connection import get_mongo_db_v1
from app.repository.mongodb.client import get_db
from app.core.Settings import settings
from app.utils.auth_utils import get_current_user
from fastapi import Depends

_SHOW_NAME = "notification"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

pinpoint = boto3.client('pinpoint')
APP_ID = "26b49d923b944e0b88edb97911484485"

class DeviceRegistration(BaseModel):
    token: str
    platform: str
    app_version: Optional[str] = None
    device_info: Optional[Dict[str, Any]] = None

class Notification(BaseModel):
    title: str
    body: str
    device_tokens: List[str]

@router.post("/register")
async def register_device(
    device: DeviceRegistration,
    current_user = Depends(get_current_user)
):
    """Register a device token for push notifications"""
    try:
        device_token_repo : DeviceTokenRepository = await get_mongo_db_v1("device_token")
        tenant_id = get_tenant_id()
        db = await get_db(f"{settings.MONGO_DB_NAME}_{tenant_id}")
        
        success = await device_token_repo.add_device_token(
            user_id=current_user.get("cognito:username"),
            token=device.token,
            platform=device.platform,
            tenant_id=tenant_id,
            db=db,
            app_version=device.app_version,
            device_info=device.device_info
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to register device")
            
        return {"status": "success", "message": "Device registered successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/send-notification")
async def send_notification(notification: Notification):
    try:
        # Create message requests for each device token
        message_requests = {
            token: {'ChannelType': 'GCM'} 
            for token in notification.device_tokens
        }

        response = pinpoint.send_messages(
            ApplicationId=APP_ID,
            MessageRequest={
                'Addresses': message_requests,
                'MessageConfiguration': {
                    'GCMMessage': {
                        'Action': 'OPEN_APP',
                        'Title': notification.title,
                        'Body': notification.body,
                        'SilentPush': False,
                    }
                }
            }
        )
        return {"status": "success", "message_response": response}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

