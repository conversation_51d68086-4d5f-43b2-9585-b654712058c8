import asyncio
import os
import logging
from fastapi import APIRouter, Depends, Request, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, List, Optional
from app.connection.establish_db_connection import get_mongo_db
from app.utils.auth_utils import get_current_user
from app.utils.kg_inspect.knowledge import Knowledge, KnowledgeCodeBase
from app.utils.kg_inspect.knowledge_helper import Knowledge_Helper
from app.utils.kg_inspect.knowledge_reporter import Reporter
from app.core.websocket.client import WebSocketClient
from app.core.Settings import settings
from app.connection.tenant_middleware import tenant_context
from datetime import datetime
import time
_SHOW_NAME = "code_query_v2"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)
class CodeQueryRequest(BaseModel):
    session_id: str
    user_id: str
    tenant_id: str
    project_id: int
    build_ids: List[str]
    session_name: str = "untitled"
    description: str = ""
# Background job function that replaces the screen-based approach
async def process_knowledge_job(
    session_id: str,
    user_id: str,
    tenant_id: str,
    project_id: int,
    build_ids: List[str],
    session_name: str,
    description: str
):
    try:
        # Set tenant context
        tenant_context.set(tenant_id)
        # Initialize WebSocket client for real-time updates
        ws_client = WebSocketClient(session_id, settings.WEBSOCKET_URI)
        # Get MongoDB connection
        mongo_handler = get_mongo_db().db
        # Fetch project data
        project_data = mongo_handler["project_repositories"].find_one({"project_id": project_id})
        if not project_data:
            ws_client.send_message("code_query", {
                "message": f'ERROR: Project with ID {project_id} not found',
                "session_id": session_id
            })
            return
        # Track build paths and incomplete builds
        build_paths = []
        incomplete_builds = []
        # Process repository data - optimize by gathering all data before processing
        for repo in project_data.get('repositories', []):
            for branch in repo.get('branches', []):
                branch_name = branch['name']
                build_info = branch.get('builds', {})
                if build_info.get('build_id') in build_ids:
                    # Check if knowledge generation is complete
                    if build_info.get('kg_creation_status') != 2:
                        incomplete_builds.append({
                            'build_id': build_info.get('build_id'),
                            'status': build_info.get('kg_creation_status')
                        })
                        continue
                    build_path = build_info.get('path')
                    if build_path:
                        # Fast git operations
                        await asyncio.to_thread(lambda: os.chdir(build_path))
                        # Run git commands concurrently where possible
                        await asyncio.gather(
                            asyncio.to_thread(lambda: os.system(f'git stash')),
                            asyncio.to_thread(lambda: os.system(f'git pull --rebase'))
                        )
                        await asyncio.to_thread(lambda: os.system(f'git switch {branch_name}'))
                        await asyncio.to_thread(lambda: os.system(f'git stash pop'))
                        await asyncio.to_thread(lambda: os.chdir(os.path.dirname(os.path.dirname(build_path))))
                        # Remove lock file if exists
                        lock_file = os.path.join(build_path, '.knowledge', '.vector_db', '.milvus.db.lock')
                        if os.path.exists(lock_file):
                            try:
                                os.remove(lock_file)
                            except Exception as e:
                                logging.error(f"Error removing lock file: {e}")
                        build_paths.append({
                            'build_id': build_info['build_id'],
                            'path': build_path,
                            'repo_url': repo['git_url'],
                            'branch': branch['name']
                        })
        # Handle incomplete builds
        if incomplete_builds:
            ws_client.send_message("code_query", {
                "message": "Knowledge generation is not complete for some builds",
                "incomplete_builds": incomplete_builds,
                "session_id": session_id
            })
            return
        # Ensure we have valid builds
        if not build_paths:
            ws_client.send_message("code_query", {
                "message": "No valid build paths found for the specified build IDs",
                "session_id": session_id
            })
            return
        # Prepare codebase paths efficiently
        codebase_paths = [
            KnowledgeCodeBase(
                build_path["path"], f"{build_path['repo_url']}:{build_path['branch']}",
            )
            for build_path in build_paths
        ]
        # Setup reporter and knowledge helper
        reporter = Reporter(ws_client)
        reporter.initialize()
        reporter.ws_client.send_message("code_query", {"message": 'NOT STARTED', "session_id": session_id})
        knowledge_helper = Knowledge_Helper(session_id, reporter, os.getcwd(), codebase_paths)
        # Count files for progress reporting
        total_files = sum(len(knowledge_helper.knowledge._list_important_files(base.base_path)) for base in codebase_paths)
        # Process knowledge
        reporter.ws_client.send_message("code_query", {"message": 'NOT STARTED - STARTING KNOWLEDGE', "session_id": session_id})
        # Start knowledge processing (potentially heavy operation)
        await asyncio.to_thread(knowledge_helper.knowledge.start)
        # Record session in database
        await asyncio.to_thread(
            lambda: mongo_handler["code_query_session"].insert_one({
                "session_id": session_id,
                "build_ids": build_ids,
                "start_time": datetime.now(),
                "session_name": session_name,
                "description": description
            })
        )
        # Record start time and report progress
        start_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        reporter.ws_client.send_message("code_query", {
            "message": f'NOT STARTED - KNOWLEDGE CREATION COMPLETE - {start_datetime} - {total_files}',
            "session_id": session_id
        })
        # More efficient readiness check with timeout
        init_message_sent = False
        max_wait_time = 3600  # 1 hour timeout
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            if reporter.is_ready():
                if not init_message_sent:
                    reporter.send_message("code_query", {"message": 'STARTED', "session_id": session_id})
                    init_message_sent = True
                    break
            # Reduced CPU usage with efficient sleep
            await asyncio.sleep(0.1)
    except Exception as e:
        logging.error(f"Knowledge job error: {str(e)}")
        if 'ws_client' in locals():
            ws_client.send_message("code_query", {
                "message": f'ERROR: {str(e)}',
                "session_id": session_id
            })
@router.post("/initialize_code_query")
async def code_query(
    request: CodeQueryRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    try:
        user_id = current_user.get("cognito:username")
        session_id = str(request.session_id)
        # Add task to background queue - returns immediately while processing continues
        background_tasks.add_task(
            process_knowledge_job,
            session_id,
            request.user_id,
            request.tenant_id,
            request.project_id,
            request.build_ids,
            request.session_name,
            request.description
        )
        return {
            "status": "success",
            "session_id": session_id,
            "project_id": request.project_id,
            "total_files": 0  # Will be updated by background task
        }
    except Exception as e:
        logging.error(f"Error initializing code query: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error initializing code query: {str(e)}")
# Keeping the existing endpoints unchanged
@router.get("/get-session/{session_id}")
async def get_session(request: Request, session_id: str):
    try:
        if session_id:
            knowledge = Knowledge.getKnowledge(id=session_id)
            if knowledge:
                return {
                    'status': 'active',
                    'session_id': session_id,
                    'source_files_count': len(knowledge.source_files) if hasattr(knowledge, 'source_files') else 0,
                    'state': knowledge._state
                }
            return {'status': 'not_found'}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving session: {str(e)}")
@router.delete("/delete-session/{session_id}")
async def delete_session(request: Request, session_id: str):
    try:
        if session_id:
            Knowledge.releaseKnowledge(session_id)
            return {'status': True}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting session: {str(e)}")