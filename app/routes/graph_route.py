import json
from bson import ObjectId
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi import Header, APIRouter, Depends, Body, Query, Request, responses, HTTPException
from app.classes.Chat import Chat
from app.classes.InspectorDB import InspectorDB
from app.connection.establish_db_connection import get_inspector_db, get_node_db, NodeDB
from app.utils.auth_utils import get_current_user
from app.utils.node_utils import get_node_type
from typing import Optional
from fastapi import Query
from typing import List
from pydantic import BaseModel
from typing import Any, Dict, List

_SHOW_NAME = "graph"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

def load_json_file(file_path):
    with open(file_path, 'r') as file:
        return json.load(file)

@router.get("/code_view")
async def get_code_view(
    node_type: str = Query(...,
                           description="Type of node (e.g., Module, Function, Class)"),
    name: Optional[str] = Query(None, description="Optional name filter"),
    limit: int = Query(1, description="Limit of nodes to return"),
    current_user=Depends(get_current_user),
    inspector_db: InspectorDB = Depends(get_inspector_db)
):
    valid_types = ["Module", "Function", "Class", "py", "ClassMethod",
                   "Constant", "Method", "Library", "Attribute", "Variable"]

    if node_type not in valid_types:
        raise HTTPException(
            status_code=400, detail=f"Invalid node type. Must be one of: {', '.join(valid_types)}")

    result = await inspector_db.get_graph_code_view(node_type, name, limit)
    return JSONResponse(result, status_code=200)

# Define a Pydantic model for the incoming JSON data
class GraphViewRequest(BaseModel):
    graph: str  # 'project' or 'code'
    depth: int = 2  # Default depth is 2
    node_types: List[str] = ["File", "Folder", "SuperRoot"]  # Default node types
    node_id: Optional[int] = None  # Optional node ID

@router.post("/get_old_graph_nodes_and_edges")
async def get_old_graph_nodes_and_edges(
    request_data: GraphViewRequest,  # Automatically parses JSON body
    current_user=Depends(get_current_user),
    # inspector_db: InspectorDB = Depends(get_inspector_db),
    node_db: NodeDB = Depends(get_node_db)
):
    # Access the data from the request body
    graph = request_data.graph
    depth = request_data.depth
    node_types = request_data.node_types
    node_id = request_data.node_id

    if graph not in ["project", "code"]:
        raise HTTPException(status_code=400, detail="Invalid graph. Must be 'project' or 'code'")

    db = node_db

    try:
        if graph == "code":
            
            # if node_types is default remove them 
            if node_types == ["File", "Folder", "SuperRoot"]:
                node_types = []
                
            result = await db.get_old_graph_nodes_and_edges(depth, node_types)  # For InspectorDB
        else:

            result = await db.get_old_graph_nodes_and_edges(node_id, depth, node_types)  # For NodeDB

        return JSONResponse(result, status_code=200)
    except Exception as e:
        print("Exception: ", e)
        raise HTTPException(
            status_code=500, detail=f"An error occurred: {str(e)}"
        )
    

class DemoGraphViewRequest(BaseModel):
    graph: str  # 'project' or 'code'
    depth: int = 2  # Default depth is 2
    node_types: List[str] = ["File", "Folder", "SuperRoot"]  # Default node types
    node_id: Optional[int] = None  # Optional node ID

@router.post("/get_new_graph_nodes_and_edges")
async def get_new_graph_nodes_and_edges(
    request_data: DemoGraphViewRequest,  # Automatically parses JSON body
    # inspector_db: InspectorDB = Depends(get_inspector_db),
    node_db: NodeDB = Depends(get_node_db)
):
    # Access the data from the request body
    graph = request_data.graph
    depth = request_data.depth
    node_types = request_data.node_types
    node_id = request_data.node_id

    if graph not in ["project", "code"]:
        raise HTTPException(status_code=400, detail="Invalid graph. Must be 'project' or 'code'")

    db = node_db

    try:
        if graph == "code":
            
            # if node_types is default remove them 
            if node_types == ["File", "Folder", "SuperRoot"]:
                node_types = []
                
            result = await db.get_new_graph_nodes_and_edges(depth, node_types)  # For InspectorDB
        else:

            result = await db.get_new_graph_nodes_and_edges(node_id, depth, node_types)  # For NodeDB
        return JSONResponse(result, status_code=200)
    except Exception as e:
        print("Exception: ", e)
        raise HTTPException(
            status_code=500, detail=f"An error occurred: {str(e)}"
        )

@router.get("/labels")
async def get_all_labels(
    graph: str = Query(...,
                         description="Type of database to query: 'project' or 'code'"),
    # inspector_db: InspectorDB = Depends(get_inspector_db),
    db:NodeDB = Depends(get_node_db)
):
    if graph not in ["project", "code"]:
        raise HTTPException(
            status_code=400, detail="Invalid graph. Must be 'project' or 'code'")

    # if graph == "code":
    #     db = inspector_db   
        
    try:
        # Execute the Cypher query to get all labels
        query = "CALL db.labels()"
        result = await db.async_run(query, limit=1)
        # Extract labels from the result
        labels = [record["label"] for record in result]
        # print(labels)
        if graph == "project":
            exclude = ['GeneralDiscussion', 'User' ,'ProductRoot', 'Product', 'Conversation', "Test"]
            labels = [label for label in labels if label not in exclude]

        return {'count': len(labels), 'lables': labels}
    
    except Exception as e:
        print(f"Error fetching labels: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"An error occurred while fetching labels: {str(e)}")


@router.get("/{node_id}")  # Adjust response_model as necessary
async def get_node(
    node_id: int,
    graph: str = Query(...,
                        description="Type of database to query: 'project' or 'code'"),
    # inspector_db: InspectorDB = Depends(get_inspector_db),
    db: NodeDB =Depends(get_node_db), 
):

    if graph not in ["project", "code"]:
        raise HTTPException(status_code=400, detail="Invalid graph. Must be 'project' or 'code'")

    # Select the correct database based on the graph
    # selected_db = inspector_db if graph == "code" else db
    selected_db = db

    try:
        # Fetch all available node types (labels) for the selected graph
        # query = "CALL db.labels()"
        # result = await selected_db.async_run(query)
        # available_node_types = [record["label"] for record in result]

        # For 'project', filter out excluded node types
        # if graph == "project":
        #     exclude_node_types = ['GeneralDiscussion', 'User', 'ProductRoot', 'Product', 'Conversation', 'Test']
        #     available_node_types = [label for label in available_node_types if label not in exclude_node_types]

        #     # normalized_available_node_types = [label.lower() for label in available_node_types]

        #     # # Validate the provided node_type
        #     if node_type not in available_node_types:
        #         raise HTTPException(status_code=400, detail="Invalid node type for the selected graph")
            # if node_type is not None and node_type.lower() not in normalized_available_node_types:
            #     raise HTTPException(status_code=400, detail="Invalid node type for the selected graph")


        if graph == "project":
            node = await selected_db.get_node_by_id(node_id)

        else:  # If querying 'code', only send node_id to InspectorDB
            node = await selected_db.get_node_by_id(node_id)
        
        # print(node)

        if node:
            # Handle NodeDB and InspectorDB separately
            # if graph == "project":
                # For NodeDB (project), load the data model and add ui_metadata
                
                
                #  # Check if node_type exists in the data model
                # ui_metadata = data_model["model"].get(node_type, {}).get("ui_metadata")
                # if ui_metadata is not None:
                #     node["ui_metadata"] = ui_metadata  # Only set if it exists

            # else:
            #     # For InspectorDB (code), just format the node properties

            return node
        else:
            return JSONResponse(content={"message": "Node data not found"})

    except Exception as e:
        print(f"Exception: {e}")
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

@router.get("/newgraph_search_nodes/{node_id}")
async def newgraph_search_nodes(
    node_id: int,
    graph: str = Query(..., description="Type of database to query: 'project' or 'code'"),
    search_text: str = Query(..., description="Text to search in node's Title or Name"),
    # inspector_db: InspectorDB = Depends(get_inspector_db),
    node_db: NodeDB = Depends(get_node_db)
):
    # Validate the graph type
    if graph not in ["project", "code"]:
        raise HTTPException(status_code=400, detail="Invalid graph. Must be 'project' or 'code'")

    # Select appropriate database
    # db = inspector_db if graph == "code" else node_db
    db = node_db

    try:
        result = await db.newgraph_search_nodes(node_id, search_text)
        return JSONResponse(result, status_code=200)
    except Exception as e:
        print("Exception: ", e)
        raise HTTPException(
            status_code=500, detail=f"An error occurred: {str(e)}"
        )
    
@router.get("/oldgraph_search_nodes/{node_id}")
async def oldgraph_search_nodes(
    node_id: int,
    graph: str = Query(..., description="Type of database to query: 'project' or 'code'"),
    search_text: str = Query(..., description="Text to search in node's Title or Name"),
    # inspector_db: InspectorDB = Depends(get_inspector_db),
    node_db: NodeDB = Depends(get_node_db)
):
    # Validate the graph type
    if graph not in ["project", "code"]:
        raise HTTPException(status_code=400, detail="Invalid graph. Must be 'project' or 'code'")

    # Select appropriate database
    # db = inspector_db if graph == "code" else node_db
    db = node_db

    try:
        result = await db.oldgraph_search_nodes(node_id, search_text)
        return JSONResponse(result, status_code=200)
    except Exception as e:
        print("Exception: ", e)
        raise HTTPException(
            status_code=500, detail=f"An error occurred: {str(e)}"
        )