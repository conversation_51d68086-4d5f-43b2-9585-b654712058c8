from app.connection.establish_db_connection import get_node_db, NodeDB

from app.utils.code_generation_utils import json_to_yaml, yaml_to_json
from app.utils.project_utils import name_to_slug
from app.utils.node_utils import get_node_type
from fastapi import APIRouter, Depends, HTTPException
from app.models.repository_model import RepositoryUpdateRequest
from fastapi.responses import JSONResponse, StreamingResponse
from app.core.constants import GIT_IGNORE_CONTENT
from app.connection.tenant_middleware import get_tenant_id
import boto3
from github import Github
from typing import Optional
import json
import os
from app.routes.deployment_helper.node_helper import get_deployment_node
from app.routes.scm_route import scm_manager
from app.models.scm import SCMType, SCMConfiguration
from app.utils.auth_utils import get_current_user
from app.utils.hash import decrypt_string
import gitlab
import github
from math import ceil
from app.routes.kg_route import import_codebase, CodebaseImportRequest, RepoBranchRequest
from fastapi import BackgroundTasks
import logging
import requests
import re
from pydantic import BaseModel, SecretStr, Field
from fastapi import Query
from github.Repository import Repository
from app.utils.respository_utils import get_github_client, _run_clone_in_background
from app.connection.establish_db_connection import get_mongo_db, MongoDBHandler
from app.core.constants import TASKS_COLLECTION_NAME, REPOSITORIES_COLLECTION
from app.models.scm import ACCESS_TOKEN_PATH
import httpx
import asyncio
from app.utils.prompts import manifest_prompt
from app.utils.manifest_utils import get_project_manifest_for_maintenance, get_project_manifest_for_generation
from app.utils.datetime_utils import generate_timestamp
import threading
from app.core.Settings import settings

# Logger
logger = logging.getLogger(__name__)

_SHOW_NAME = "repository"

router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

async def create_github_branch(
    repository_name: str, 
    access_token: str, 
    organization: str, 
    source_branch: str, 
    new_branch: str,
    max_retries: int = 3
) -> dict:
    """
    Create a new branch in GitHub repository using REST API
    """
    headers = {
        "Authorization": f"token {access_token}",
        "Accept": "application/vnd.github+json",
        "X-GitHub-Api-Version": "2022-11-28"
    }
    
    for attempt in range(max_retries):
        try:
            async with httpx.AsyncClient() as client:
                # Get source branch reference
                source_ref_url = f"https://api.github.com/repos/{organization}/{repository_name}/git/ref/heads/{source_branch}"
                source_response = await client.get(source_ref_url, headers=headers)
                
                if source_response.status_code != 200:
                    if attempt == max_retries - 1:
                        raise HTTPException(
                            status_code=400, 
                            detail=f"Failed to get source branch {source_branch}: {source_response.text}"
                        )
                    # Wait before retry
                    await asyncio.sleep(2 ** attempt)
                    continue
                
                source_sha = source_response.json()["object"]["sha"]
                
                # Create new branch reference
                create_ref_url = f"https://api.github.com/repos/{organization}/{repository_name}/git/refs"
                create_ref_data = {
                    "ref": f"refs/heads/{new_branch}",
                    "sha": source_sha
                }
                
                create_response = await client.post(create_ref_url, headers=headers, json=create_ref_data)
                
                if create_response.status_code in [200, 201]:
                    logger.info(f"Successfully created branch {new_branch} from {source_branch} in {repository_name}")
                    return create_response.json()
                elif create_response.status_code == 422 and "already exists" in create_response.text:
                    # Branch already exists, that's fine
                    logger.info(f"Branch {new_branch} already exists in {repository_name}")
                    return {"message": "Branch already exists"}
                else:
                    if attempt == max_retries - 1:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Failed to create branch {new_branch}: {create_response.text}"
                        )
                    # Wait before retry
                    await asyncio.sleep(2 ** attempt)
                    continue
                    
        except Exception as e:
            if attempt == max_retries - 1:
                logger.error(f"Error creating GitHub branch after {max_retries} attempts: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to create branch: {str(e)}")
            # Wait before retry
            await asyncio.sleep(2 ** attempt)
    
    raise HTTPException(status_code=500, detail="Failed to create branch after maximum retries")

async def create_github_repository(repository_name: str,
                                 config: SCMConfiguration,
                                 is_private: bool = True,
                                ):
    access_token = config.credentials.access_token
    github_client = Github(access_token)
    org = config.credentials.organization
    
    try:
        if org:
            try:
                # Try to get organization and create repo
                organization = github_client.get_organization(org)
                repo = organization.create_repo(
                    name=repository_name,
                    private=is_private,
                    auto_init=True,
                    gitignore_template="Python"
                )
            except github.GithubException as e:
                if e.status == 404:
                    # Fall back to creating personal repo if org not found
                    print(f"Organization {org} not found, creating personal repository instead")
                    repo = github_client.get_user().create_repo(
                        name=repository_name,
                        private=is_private,
                        auto_init=True,
                        gitignore_template="Python"
                    )
                else:
                    raise e
        else:
            # Create personal repository
            repo: Repository = github_client.get_user().create_repo(
                name=repository_name,
                private=is_private,
                auto_init=True,
                gitignore_template="Python"
            )
        
        # Wait a moment for repository to be fully initialized
        await asyncio.sleep(2)
        # Create kavia-main branch from main branch
        try:
            # Get the default branch (usually 'main' or 'master')
            default_branch = repo.default_branch
            logger.info(f"Default branch for {repository_name}: {default_branch}")
            
            # Create kavia-main branch from the default branch
            await create_github_branch(
                repository_name=repository_name,
                access_token=access_token,
                organization=org,
                source_branch=default_branch,
                new_branch="kavia-main"
            )
            
            # Set kavia-main as default branch
            try:
                repo.edit(default_branch="kavia-main")
                logger.info(f"Set kavia-main as default branch for {repository_name}")

            except Exception as e:
                logger.warning(f"Could not set kavia-main as default branch: {str(e)}")
            
        except Exception as e:
            logger.error(f"Failed to create kavia-main branch: {str(e)}")
            # Continue with the repository creation even if branch operations fail
        scm_id = decrypt_string(config.encrypted_scm_id)
        repository_metadata = {
            'service': 'github',
            'repositoryName': repo.name,
            'repositoryId': str(repo.id),
            'cloneUrlHttp': repo.clone_url,
            'cloneUrlSsh': repo.ssh_url,
            'organization': org,
            'encrypted_scm_id': config.encrypted_scm_id,
            'scm_id': scm_id,
            'repositoryStatus': 'initialized',
            'default_branch': 'kavia-main'
        }
        
        # Update README and .gitignore on kavia-main branch
        try:
            # Wait a moment for branch to be available
            await asyncio.sleep(2)
            
            # Update README content
            try:
                readme = repo.get_contents("README.md", ref="kavia-main")
                repo.update_file(
                    path="README.md",
                    message="Update README",
                    content="# Project Repository\n\nThis is the initial README file for the project.",
                    sha=readme.sha,
                    branch="kavia-main"
                )
            except Exception as e:
                logger.warning(f"Could not update README on kavia-main: {str(e)}")
            
            # Update .gitignore content
            try:
                gitignore = repo.get_contents(".gitignore", ref="kavia-main")
                repo.update_file(
                    path=".gitignore",
                    message="Update .gitignore",
                    content=GIT_IGNORE_CONTENT,
                    sha=gitignore.sha,
                    branch="kavia-main"
                )
            except Exception as e:
                logger.warning(f"Could not update .gitignore on kavia-main: {str(e)}")
                
        except Exception as e:
            logger.warning(f"Could not update files on kavia-main branch: {str(e)}")
        
        
        if os.getenv("task_id"):
            tenant_id = get_tenant_id()
            task_id = os.environ.get("task_id")
            repository_metadata_copy = repository_metadata.copy()
            repository_metadata_copy["access_token"] = access_token
            clone_thread = threading.Thread(
                target=_run_clone_in_background,
                args=(
                    repository_metadata_copy,
                    tenant_id,
                    task_id,
                    repository_name
                ),
                daemon=True
            )
            clone_thread.start()
            print(generate_timestamp(), f"Started background clone thread for repository: {repository_name}")
        return repository_metadata
        
    except Exception as e:
        print(f"Error creating GitHub repository: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create GitHub repository: {str(e)}"
        )

# curl --location 'https://api.github.com/user/repos?per_page=30&page=2' \
# --header 'Authorization: Bearer <YOUR-TOKEN>' \
# --header 'X-GitHub-Api-Version: 2022-11-28'
async def get_github_repositories(
    access_token: str, 
    page: int = 1, 
    per_page: int = 30, 
    search: Optional[str] = None,
    organization: Optional[str] = None,
    visibility: Optional[str] = None
) -> dict:
    headers = {
        "Accept": "application/vnd.github+json",
        "Authorization": f"Bearer {access_token}",
        "X-GitHub-Api-Version": "2022-11-28"
    }
    
    # Build search query with filters
    search_query = []
    if search and search.strip():
        search_query.append(search.strip())
    if organization:
        search_query.append(f"org:{organization}")
    if visibility:
        search_query.append(f"is:{visibility}")
    
    # Always add user:@me if no organization specified
    if not organization:
        search_query.append("user:@me")
    
    # If we have any search criteria, use the search API
    if search_query:
        query = "+".join(search_query)
        api_url = f"https://api.github.com/search/repositories?q={query}&per_page={per_page}&page={page}"
        response = requests.get(api_url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            repositories = data.get("items", [])
            total_count = data.get("total_count", 0)
            total_pages = ceil(total_count / per_page) if total_count > 0 else 1
            
            return {
                "repositories": repositories,
                "total_count": total_count,
                "pagination": {
                    "current_page": page,
                    "per_page": per_page,
                    "total_pages": total_pages
                }
            }
    else:
        # Default repository listing API with visibility filter if specified
        api_url = f"https://api.github.com/user/repos?per_page={per_page}&page={page}"
        if visibility:
            api_url += f"&visibility={visibility}"
            
        response = requests.get(api_url, headers=headers)
        
        if response.status_code == 200:
            repositories = response.json()
            
            total_count = len(repositories)
            total_pages = 1
            
            if "Link" in response.headers:
                link_header = response.headers["Link"]
                last_page_match = re.search(r'page=(\d+)>; rel="last"', link_header)
                if last_page_match:
                    total_pages = int(last_page_match.group(1))
                    if page < total_pages:
                        total_count = (total_pages - 1) * per_page + len(repositories)
            
            return {
                "repositories": repositories,
                "total_count": total_count,
                "pagination": {
                    "current_page": page,
                    "per_page": per_page,
                    "total_pages": total_pages
                }
            }
    
    raise HTTPException(
        status_code=response.status_code, 
        detail=response.json().get("message", "Unknown error fetching GitHub repositories")
    )

async def get_gitlab_repositories(
    access_token: str, 
    page: int = 1, 
    per_page: int = 30, 
    search: Optional[str] = None,
    organization: Optional[str] = None,
    visibility: Optional[str] = None
) -> dict:
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    
    # Build base GitLab API URL with filters
    api_url = f"https://gitlab.com/api/v4/projects?page={page}&per_page={per_page}&order_by=id&sort=desc"
    
    # Add filters
    if organization:
        api_url += f"&owned=false&search_namespaces=true&group={organization}"
    else:
        api_url += "&owned=true"
    if visibility:
        api_url += f"&visibility={visibility}"
    if search and search.strip():
        api_url += f"&search={search.strip()}"
    
    response = requests.get(api_url, headers=headers)

    if response.status_code == 200:
        repositories = response.json()
        total_count = int(response.headers.get("x-total", len(repositories)))
        total_pages = int(response.headers.get("x-total-pages", 1))
        
        return {
            "repositories": repositories,
            "total_count": total_count, 
            "pagination": {
                "current_page": page,
                "per_page": per_page,
                "total_pages": total_pages
            }
        }
    else:
        raise HTTPException(
            status_code=response.status_code, 
            detail=response.json().get("message", "Unknown error fetching GitLab repositories")
        )

async def get_github_repository(repository_name: str, access_token: str, organization: str ) -> dict:

    headers = {
        "Accept": "application/vnd.github+json",
        "Authorization": f"Bearer {access_token}",
        "X-GitHub-Api-Version": "2022-11-28"
    }

    api_url= f"https://api.github.com/repos/{organization}/{repository_name}"
    response = requests.get(api_url, headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        raise HTTPException(status_code=response.status_code, detail=response.json().get("message", "Unknown error"))

async def get_github_branches(repository_name: str, access_token: str, organization: str, page: int = 1, per_page: int = 30) -> dict:
    headers = {
        "Accept": "application/vnd.github+json",
        "Authorization": f"Bearer {access_token}",
        "X-GitHub-Api-Version": "2022-11-28"
    }
    
    api_url = f"https://api.github.com/repos/{organization}/{repository_name}/branches"
    params = {
        "per_page": per_page,
        "page": page
    }
    
    response = requests.get(api_url, headers=headers, params=params)
    
    if response.status_code == 200:
        branches_data = response.json()
        
        # Calculate total count from Link header if available
        total_count = len(branches_data)
        total_pages = 1
        
        if "Link" in response.headers:
            link_header = response.headers["Link"]
            last_page_match = re.search(r'page=(\d+)>; rel="last"', link_header)
            if last_page_match:
                total_pages = int(last_page_match.group(1))
                # Estimate total count if we're not on the last page
                if page < total_pages:
                    total_count = (total_pages - 1) * per_page + len(branches_data)
        
        # Format the response
        return {
            "branches": branches_data,
            "total_count": total_count,
            "pagination": {
                "current_page": page,
                "per_page": per_page,
                "total_pages": total_pages
            }
        }
    else:
        raise HTTPException(
            status_code=response.status_code, 
            detail=response.json().get("message", "Unknown error")
        )

# #curl -L \
#   -H "Accept: application/vnd.github+json" \
#   -H "Authorization: Bearer <YOUR-TOKEN>" \
#   -H "X-GitHub-Api-Version: 2022-11-28" \
#   https://api.github.com/repos/OWNER/REPO/git/ref/REF

async def get_a_ref(repository_name: str, access_token: str, organization: str, source_branch: str) -> dict:
    headers = {
        "Accept": "application/vnd.github+json",
        "Authorization": f"Bearer {access_token}",
        "X-GitHub-Api-Version": "2022-11-28"
    }
    ref = f"refs/heads/{source_branch}"
    api_url = f"https://api.github.com/repos/{organization}/{repository_name}/git/{ref}"
    response = requests.get(api_url, headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        raise HTTPException(status_code=response.status_code, detail=response.json().get("message", "Unknown error"))

# curl -L \
#   -X POST \
#   -H "Accept: application/vnd.github+json" \
#   -H "Authorization: Bearer <YOUR-TOKEN>" \
#   -H "X-GitHub-Api-Version: 2022-11-28" \
#   https://api.github.com/repos/OWNER/REPO/git/refs \
#   -d '{"ref":"refs/heads/featureA","sha":"aa218f56b14c9653891f9e74264a383fa43fefbd"}'

async def create_a_ref(repository_name: str, access_token: str, organization: str, ref: str, sha: str) -> dict:
    headers = {
        "Accept": "application/vnd.github+json",
        "Authorization": f"Bearer {access_token}",
        "X-GitHub-Api-Version": "2022-11-28"
    }
    api_url = f"https://api.github.com/repos/{organization}/{repository_name}/git/refs"
    data = {
        "ref": ref,
        "sha": sha
    }
    response = requests.post(api_url, headers=headers, json=data)
    if response.status_code == 201:
        return response.json()
    else:
        raise HTTPException(status_code=response.status_code, detail=response.json().get("message", "Unknown error"))
    

async def create_gitlab_repository(
    repository_name: str,
    config: SCMConfiguration,
    is_private: bool = True
) -> dict:
    access_token = config.credentials.access_token
    gl = gitlab.Gitlab(
        config.api_url or 'https://gitlab.com',
        oauth_token=access_token
    )
    gl.auth()
    
    org = config.credentials.organization
    visibility = 'private' if is_private else 'public'
    
    try:
        if org:
            try:
                # Try to create repo in organization/group
                group = gl.groups.get(org)
                project = group.projects.create({
                    'name': repository_name,
                    'visibility': visibility,
                    'initialize_with_readme': True
                })
            except gitlab.exceptions.GitlabGetError as e:
                if e.response_code == 404:
                    # Fall back to creating personal repo if group not found
                    print(f"Group {org} not found, creating personal repository instead")
                    project = gl.projects.create({
                        'name': repository_name,
                        'visibility': visibility,
                        'initialize_with_readme': True
                    })
                else:
                    raise e
        else:
            # Create personal repository
            project = gl.projects.create({
                'name': repository_name,
                'visibility': visibility,
                'initialize_with_readme': True
            })

        # Wait a moment for repository to be fully initialized
        await asyncio.sleep(2)
        
        # Create kavia-main branch from main branch
        try:
            # Get the default branch (usually 'main' or 'master')
            default_branch = project.default_branch or 'main'
            logger.info(f"Default branch for {repository_name}: {default_branch}")
            
            # Create kavia-main branch from the default branch
            await create_gitlab_branch(
                repository_name=repository_name,
                access_token=access_token,
                organization=org,
                project_id=str(project.id),
                source_branch=default_branch,
                new_branch="kavia-main"
            )
            
            # Set kavia-main as default branch
            try:
                project.default_branch = 'kavia-main'
                project.save()
                logger.info(f"Set kavia-main as default branch for {repository_name}")
                
            except Exception as e:
                logger.warning(f"Could not set kavia-main as default branch: {str(e)}")
            
        except Exception as e:
            logger.error(f"Failed to create kavia-main branch: {str(e)}")
            # Continue with the repository creation even if branch operations fail

        repository_metadata = {
            'service': 'gitlab',
            'repositoryName': project.name,
            'repositoryId': str(project.id),
            'cloneUrlHttp': project.http_url_to_repo,
            'cloneUrlSsh': project.ssh_url_to_repo,
            'organization': org,
            'encrypted_scm_id': config.encrypted_scm_id,
            'repositoryStatus': 'initialized',
            'default_branch': 'kavia-main'
        }
        
        # Update README and .gitignore on kavia-main branch
        try:
            # Wait a moment for branch to be available
            await asyncio.sleep(2)
            
            # Update README content
            try:
                readme_file = project.files.get('README.md', ref='kavia-main')
                readme_file.content = "# Project Repository\n\nThis is the initial README file for the project."
                readme_file.save(branch='kavia-main', commit_message='Update README')
                logger.info(f"Updated README on kavia-main branch for {repository_name}")
            except Exception as e:
                logger.warning(f"Could not update README on kavia-main: {str(e)}")
            
            # Update .gitignore content
            try:
                try:
                    # Try to get existing .gitignore
                    gitignore_file = project.files.get('.gitignore', ref='kavia-main')
                    gitignore_file.content = GIT_IGNORE_CONTENT
                    gitignore_file.save(branch='kavia-main', commit_message='Update .gitignore')
                except gitlab.exceptions.GitlabGetError:
                    # Create .gitignore if it doesn't exist
                    project.files.create({
                        'file_path': '.gitignore',
                        'branch': 'kavia-main',
                        'content': GIT_IGNORE_CONTENT,
                        'commit_message': 'Add .gitignore file'
                    })
                logger.info(f"Updated .gitignore on kavia-main branch for {repository_name}")
            except Exception as e:
                logger.warning(f"Could not update .gitignore on kavia-main: {str(e)}")
                
        except Exception as e:
            logger.warning(f"Could not update files on kavia-main branch: {str(e)}")
        
        # Background cloning if task_id is present
        if os.getenv("task_id"):
            tenant_id = get_tenant_id()
            task_id = os.environ.get("task_id")
            repository_metadata_copy = repository_metadata.copy()
            repository_metadata_copy["access_token"] = access_token
            clone_thread = threading.Thread(
                target=_run_clone_in_background,
                args=(
                    repository_metadata_copy,
                    tenant_id,
                    task_id,
                    repository_name
                ),
                daemon=True
            )
            clone_thread.start()
            print(generate_timestamp(), f"Started background clone thread for repository: {repository_name}")
            
        return repository_metadata
        
    except Exception as e:
        print(f"Error creating GitLab repository: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create GitLab repository: {str(e)}"
        )

async def create_gitlab_branch(
    repository_name: str,
    access_token: str,
    organization: str,
    project_id: str,
    source_branch: str,
    new_branch: str
) -> dict:
    """Create a new branch in GitLab repository"""
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    api_url = f"https://gitlab.com/api/v4/projects/{project_id}/repository/branches"
    data = {
        "branch": new_branch,
        "ref": source_branch
    }
    
    response = requests.post(api_url, headers=headers, json=data)
    
    if response.status_code == 201:
        logger.info(f"Successfully created branch {new_branch} from {source_branch} in {repository_name}")
        return response.json()
    else:
        error_detail = response.json().get("message", "Unknown error creating branch")
        logger.error(f"Failed to create branch {new_branch}: {error_detail}")
        raise HTTPException(
            status_code=response.status_code,
            detail=f"Failed to create branch {new_branch}: {error_detail}"
        )
        
# Pydantic model for repository download request
class DownloadRepoRequest(BaseModel):
    scm_type: str
    owner: str
    repo: str
    access_token: SecretStr  # Using SecretStr to protect the token in logs
    file_extension: str = "zip"  

def download_repo(scm: SCMType, owner: str, repo: str, access_token: str, file_extension: str):
    """Downloads a repository from GitHub or GitLab as a .zip or .tar file and returns a streaming response."""
    if scm == "github":
        url = f"https://api.github.com/repos/{owner}/{repo}/{file_extension}ball"
        headers = {"Authorization": f"token {access_token}"}
    elif scm == "gitlab":
        url = f"https://gitlab.com/api/v4/projects/{owner}%2F{repo}/repository/archive.{file_extension}"
        headers = {"PRIVATE-TOKEN": access_token}
    else:
        raise ValueError("scm must be either 'github' or 'gitlab'")

    try:
        response = requests.get(url, headers=headers, stream=True)
        response.raise_for_status()  # Raise exception for 4XX/5XX responses
        
        # Return a streaming response to the client
        return response
    except requests.exceptions.RequestException as e:
        # Handle network errors, timeouts, etc.
        error_message = str(e)
        if hasattr(e, 'response') and e.response is not None:
            status_code = e.response.status_code
            error_message = f"{error_message}: {e.response.text}"
        else:
            status_code = 500
        raise HTTPException(status_code=status_code, detail=error_message)

@router.post("/download_repo/")
async def download_repository(request: DownloadRepoRequest):
    try:
        # Extract the plain text value from SecretStr
        access_token = request.access_token.get_secret_value()
        
        # Get the streamed response from the SCM provider
        response = download_repo(
            request.scm_type, 
            request.owner, 
            request.repo, 
            access_token, 
            request.file_extension
        )
        
        # Create a streaming response to send to the client
        filename = f"{request.repo}.{request.file_extension}"
        
        # Set the appropriate content type based on file extension
        content_type = "application/zip" if request.file_extension == "zip" else "application/x-tar"
        
        # Define a generator function to stream the content in chunks
        def stream_content():
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:  # filter out keep-alive new chunks
                    yield chunk
        
        # Return streaming response with the appropriate headers
        return StreamingResponse(
            stream_content(),
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
    except ValueError as e:
        # Handle validation errors raised within download_repo
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/create_repository/{project_id}/")
async def create_repository(
    project_id: int, 
    container_id: int,
    scm_id: str,
    repository_name: Optional[str] = None,
    is_private: bool = True,
    force_new: bool = False,
    db: NodeDB = Depends(get_node_db),
    current_user: dict = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    project = await db.get_node_by_id(project_id)
    project_details = project.get("properties")
    
    if not project_details or get_node_type(project_details.get("Type")) != "Project":
        raise HTTPException(status_code=404, detail="Project not configured")
    
    container = await db.get_node_by_id(container_id)
    container_details = container.get("properties")
    
    if not container:
        raise HTTPException(status_code=404, detail="Container not configured")
    
    # Check if container repository already exists
    project_repositories = json.loads(project_details.get("repositories", "{}"))
    if str(container_id) in project_repositories and not force_new:
        return {"repository": project_repositories[str(container_id)]}
    
    # Generate default repository name if not provided
    if not repository_name:
        repository_name = name_to_slug(
            f"{project_details.get('Title', project_details.get('Name'))}"
        )
        repository_name = f'{repository_name}-{project.get("id")}-{container.get("id")}'
    
    tenant_id = current_user.get("custom:tenant_id")
    config = scm_manager.get_configuration(tenant_id, scm_id=scm_id)
    if not config:
        raise HTTPException(status_code=404, detail="No SCM configuration found")

    if config.scm_type == SCMType.GITLAB:
        repository_metadata = await create_gitlab_repository(
            repository_name=repository_name,
            config=config,
            is_private=is_private
        )
    elif config.scm_type == SCMType.GITHUB:
        repository_metadata = await create_github_repository(
            repository_name=repository_name,
            config=config,
            is_private=is_private
        )
    else:
        raise HTTPException(status_code=400, detail="Unsupported SCM service")

    # Update project node with container repository details
    project_repositories[str(container_id)] = repository_metadata
    await db.update_node_by_id(project_id, {"repositories": json.dumps(project_repositories)})
    logger.info(f"Repository created for project {project_id} with container {container_id}")

    repo_branch_request: RepoBranchRequest = RepoBranchRequest(
        repo_name=f"{repository_metadata.get('organization')}/{repository_metadata.get('repositoryName')}",
        branch_name="kavia-main",
        repo_type="private",
        repo_id=repository_metadata.get("repositoryId"),
        associated=True
    )

    codebase_import_request: CodebaseImportRequest = CodebaseImportRequest(
        project_id=project_id,
        repositories=[repo_branch_request],
        encrypted_scm_id=repository_metadata.get("encrypted_scm_id")
    )
    
    background_tasks.add_task(import_codebase, codebase_import_request, False, current_user)
    return {"repository": repository_metadata}

@router.post("/link_repository/{project_id}/")
async def link_repository(
    project_id: int,
    container_id: int,
    repository_metadata: dict,
    db: NodeDB = Depends(get_node_db),
    current_user: dict = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    project = await db.get_node_by_id(project_id)
    project_details = project.get("properties")
    
    if not project_details or get_node_type(project_details.get("Type")) != "Project":
        raise HTTPException(status_code=404, detail="Project not configured")
    
    container = await db.get_node_by_id(container_id)
    if not container:
        raise HTTPException(status_code=404, detail="Container not configured")
    
    # Load existing repositories or initialize empty dict
    project_repositories = json.loads(project_details.get("repositories", "{}"))
    branch_name = container.get("properties", {}).get("branch_name", "kavia-main")
    repo_branch_request: RepoBranchRequest = RepoBranchRequest(
        repo_name=f"{repository_metadata.get('organization')}/{repository_metadata.get('repositoryName')}",
        branch_name=branch_name,
        repo_type="private",
        repo_id=str(repository_metadata.get("repositoryId")),
        associated=True
    )
    codebase_import_request: CodebaseImportRequest = CodebaseImportRequest(
        project_id=project_id,
        repositories=[repo_branch_request],
        encrypted_scm_id=repository_metadata.get("encrypted_scm_id")
    )
    # Update project node with new repository details, overwriting if exists
    project_repositories[str(container_id)] = repository_metadata
    await db.update_node_by_id(project_id, {"repositories": json.dumps(project_repositories)})
    logger.info(f"Importing codebase for project {project_id} with container {container_id}")
    logger.info(f"Codebase import request: {codebase_import_request} and branchRequest: {repo_branch_request}")
    background_tasks.add_task(import_codebase, codebase_import_request, False, current_user)
    
    return {"repository": repository_metadata}

@router.get("/get_repository/{project_id}/")
async def get_repository(project_id: int, container_id: int, db: NodeDB = Depends(get_node_db)):
    try:
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details or get_node_type(project_details.get("Type")) != "Project":
            raise HTTPException(status_code=404, detail="Project not configured")
        
        # Load the repositories JSON string into a dictionary
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        
        # Get repository metadata for the specific container
        repository_metadata = project_repositories.get(str(container_id))
        if not repository_metadata:
            return {"error": "No repository exists for the given container."}
        
        return {"repository": repository_metadata}
    except json.JSONDecodeError:
        return {"error": "Invalid repository data format"}
    except Exception as e:
        return {"error": f"No repository exists for the given project: {str(e)}"}

@router.get("/list_repositories/{scm_type}")
async def list_repositories(
    scm_type: SCMType,
    scm_id: Optional[str] = None,
    search: Optional[str] = None,  # Add search parameter
    organization: Optional[str] = None,
    visibility: Optional[str] = None,
    page: int = 1,
    per_page: int = 30,
    current_user: dict = Depends(get_current_user),
    db: NodeDB = Depends(get_node_db)
):
    try:
        tenant_id = current_user.get("custom:tenant_id")
        if not tenant_id:
            tenant_id = get_tenant_id()
        
        # Get SCM configurations
        if scm_id:
            configs = [scm_manager.get_configuration(tenant_id, scm_id)]
            if not configs[0]:
                raise HTTPException(status_code=404, detail=f"SCM configuration with ID {scm_id} not found")
        else:
            configs = scm_manager.get_configuration(tenant_id)
            configs = [config for config in configs if config.scm_type == scm_type]
            if not configs:
                raise HTTPException(status_code=404, detail=f"No {scm_type} configurations found")

        if not isinstance(configs, list):
            configs = [configs]

        organizations_repos = {}
        total_pages = 1  # Default value
        
        for config in configs:
            try:
                if config.scm_type == SCMType.GITHUB:
                    access_token = config.credentials.access_token
                    repositories_response = await get_github_repositories(access_token, page, per_page, search, organization, visibility)
                    repositories = repositories_response.get("repositories", [])
                    total_count = repositories_response.get("total_count", 0)
                    pagination = repositories_response.get("pagination", {})
                    formatted_repositories = []
                    for repo in repositories:
                        repo_info = {
                            "repositoryName": repo.get("name"),
                            "repositoryId": repo.get("id"),
                            "path": repo.get("full_name"),
                            "web_url": repo.get("html_url"),
                            "organization": repo.get("owner", {}).get("login") or repo.get("full_name").split("/")[0],
                            "description": repo.get("description"),
                            "default_branch": repo.get("default_branch"),
                            "visibility": repo.get("visibility"),
                            "ssh_url": repo.get("ssh_url"),
                            "http_url": repo.get("clone_url"),
                            "created_at": repo.get("created_at"),
                            "last_activity_at": repo.get("updated_at"),
                            "scm_type": "github",
                        }
                        formatted_repositories.append(repo_info)

                    return {
                        "status": "success",
                        "message": "Successfully retrieved repositories for SCMType.GITHUB",
                        "data": {
                            "repositories": formatted_repositories,
                            "total_repositories": total_count,
                            "pagination": pagination
                        }
                    }
                elif config.scm_type == SCMType.GITLAB:
                    try:
                        access_token = config.credentials.access_token
                        repositories_response = await get_gitlab_repositories(access_token, page, per_page, search, organization, visibility)
                        repositories = repositories_response.get("repositories", [])
                        total_count = repositories_response.get("total_count", 0)
                        pagination = repositories_response.get("pagination", {})
                        formatted_repositories = []
                        for repo in repositories:
                            repo_info = {
                                "repositoryName": repo.get("name"),
                                "repositoryId": repo.get("id"),
                                "path_with_namespace": repo.get("path_with_namespace"),
                                "path": repo.get("path"),
                                "web_url": repo.get("web_url"),
                                "organization": repo.get("namespace", {}).get("name") or repo.get("path_with_namespace", "").split("/")[0],
                                "description": repo.get("description"),
                                "default_branch": repo.get("default_branch"),
                                "visibility": repo.get("visibility"),
                                "ssh_url": repo.get("ssh_url_to_repo"),
                                "http_url": repo.get("http_url_to_repo"),
                                "created_at": repo.get("created_at"),
                                "last_activity_at": repo.get("last_activity_at"),
                                "scm_type": "gitlab",
                            }
                            formatted_repositories.append(repo_info)

                        return {
                            "status": "success",
                            "message": "Successfully retrieved repositories for SCMType.GITLAB",
                            "data": {
                                "repositories": formatted_repositories,
                                "total_repositories": total_count,
                                "pagination": pagination
                            }
                        }
                    except Exception as e:
                        print(f"Error processing SCM configuration: {str(e)}")
                        raise HTTPException(status_code=500, detail=str(e))
            except Exception as e:
                print(f"Error processing SCM configuration: {str(e)}")
                continue
        
    except Exception as e:
        print(f"Error in list_repositories: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/list_project_repositories/{project_id}/")
async def list_project_repositories(project_id: int, db: NodeDB = Depends(get_node_db)):
    try:
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details or get_node_type(project_details.get("Type")) != "Project":
            raise HTTPException(status_code=404, detail="Project not configured")
        
        # Load the repositories JSON string into a dictionary
        project_containers = await db.get_system_context_with_containers(project_id)
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        
        return {"repositories": project_repositories, "containers": project_containers}
    except json.JSONDecodeError:
        return {"error": "Invalid repository data format"}
    except Exception as e:
        return {"error": f"Error fetching project repositories: {str(e)}"}

@router.put("/update_repository/{project_id}/")
async def update_repository(
    project_id: int,
    request: RepositoryUpdateRequest,
    db: NodeDB = Depends(get_node_db)
):
    try:
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details or get_node_type(project_details.get("Type")) != "Project":
            raise HTTPException(status_code=404, detail="Project not configured")
        
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        repository_metadata = project_repositories.get(str(request.container_id))
        
        if not repository_metadata:
            return JSONResponse(
                status_code=404,
                content={"error": "No repository exists for the given container."}
            )
        
        service = repository_metadata.get('service', '').lower()
        old_name = repository_metadata.get('repositoryName')
        
        
        project_repositories[str(request.container_id)] = repository_metadata
        await db.update_node_by_id(
            project_id,
            {"repositories": json.dumps(project_repositories)}
        )
        
        return {"repository": repository_metadata}
        
    except Exception as e:
        print(e)
        return JSONResponse(
            status_code=500,
            content={"error": f"Error updating repository: {str(e)}"}
        )
        
@router.delete("/delete_repository/{project_id}/")
async def delete_repository(
    project_id: int, 
    container_id: int, 
    db: NodeDB = Depends(get_node_db)
):
    try:
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details or get_node_type(project_details.get("Type")) != "Project":
            raise HTTPException(status_code=404, detail="Project not configured")
        
        # Load the repositories JSON string into a dictionary
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        
        # Check if repository exists
        repository_metadata = project_repositories.get(str(container_id))
        if not repository_metadata:
            return JSONResponse(
                status_code=404, 
                content={"error": "No repository exists for the given container."}
            )
        
        # Delete from the actual service (CodeCommit or GitHub)
        service = repository_metadata.get('service', '').lower()
        repository_name = repository_metadata.get('repositoryName')
        

            
        del project_repositories[str(container_id)]
        
        # Update project node with updated repositories list
        await db.update_node_by_id(
            project_id, 
            {"repositories": json.dumps(project_repositories)}
        )
        
        return {"message": f"Repository {repository_name} successfully deleted"}
        
    except json.JSONDecodeError:
        return JSONResponse(
            status_code=400, 
            content={"error": "Invalid repository data format"}
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Error deleting repository: {str(e)}"}
        )

@router.get("/list_branches/{project_id}/")
async def list_branches(
    project_id: int, 
    container_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details:
            raise HTTPException(status_code=404, detail="Project not found")
            
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        repository_metadata = project_repositories.get(str(container_id))
        
        if not repository_metadata:
            raise HTTPException(status_code=404, detail="Repository not found")
            
        

        # Create boto3 Session with credentials
        session = boto3.Session(
        aws_access_key_id=os.environ.get("AWS_ACCESS_KEY_ID_MAIN"),
        aws_secret_access_key=os.environ.get("AWS_SECRET_ACCESS_KEY_MAIN"),
        region_name='us-east-1'
        )
        
        codecommit_client = session.client('codecommit')
        
        
        try:
            # Get repository details
            repo_response = codecommit_client.get_repository(
                repositoryName=repository_metadata['repositoryName']
            
            )
            repo_details = {
                'repositoryName': repo_response['repositoryMetadata']['repositoryName'],
                'repositoryId': repo_response['repositoryMetadata']['repositoryId'],
                'creationDate': repo_response['repositoryMetadata']['creationDate'],
                'lastModifiedDate': repo_response['repositoryMetadata']['lastModifiedDate'],
                'cloneUrlHttp': repo_response['repositoryMetadata']['cloneUrlHttp']
            }

            # Get branches
            branches_response = codecommit_client.list_branches(
                repositoryName=repository_metadata['repositoryName']
            )
            
            # Get deployment configuration if exists
            configured_branch = None
            try:
                deployment_node = await get_deployment_node(project_id, container_id, db)
                if deployment_node and 'deployment_config' in deployment_node['properties']:
                    deployment_config = json.loads(deployment_node['properties']['deployment_config'])
                    configured_branch = deployment_config.get('branch')
            except Exception as e:
                print(f"Could not get deployment configuration: {str(e)}")

            branch_details = []
            for branch_name in branches_response['branches']:
                # Skip branches that don't match configured branch if one is set
                # if configured_branch and branch_name != configured_branch:
                #     continue
                    
                branch_info = codecommit_client.get_branch(
                    repositoryName=repository_metadata['repositoryName'],
                    branchName=branch_name
                )
                
                commit_id = branch_info['branch']['commitId']
                
                commit_details = codecommit_client.get_commit(
                    repositoryName=repository_metadata['repositoryName'],
                    commitId=commit_id
                )
                
                branch_details.append({
                    'name': branch_name,
                    'lastCommitId': commit_id,
                    'lastCommitMessage': commit_details['commit']['message'],
                    'isConfigured': branch_name == configured_branch if configured_branch else False
                })

            response = {
                "repository": repo_details,
                "branches": branch_details,
                "configuredBranch": configured_branch
            }

            # If a specific branch was configured but not found, add a warning
            if configured_branch and not any(b['name'] == configured_branch for b in branch_details):
                response["warning"] = f"Configured branch '{configured_branch}' not found in repository"
                
            return response
            
        except Exception as e:
            raise HTTPException(
                status_code=500, 
                detail=f"Error retrieving repository details: {str(e)}"
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/all_branches/{project_id}/")
async def list_all_branches(
    project_id: int, 
    container_id: int,
    db: NodeDB = Depends(get_node_db),
    current_user: dict = Depends(get_current_user),
    page: int = 1,
    per_page: int = 30
):
    try:
        tenant_id = current_user.get("custom:tenant_id")
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details:
            raise HTTPException(status_code=404, detail="Project not found")
            
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        repository_metadata = project_repositories.get(str(container_id))
        
        if not repository_metadata:
            raise HTTPException(status_code=404, detail="Repository not found")
        access_token = None
        service = repository_metadata.get('service', '').lower()
        repository_name = repository_metadata.get('repositoryName')
        access_token_path = repository_metadata.get('access_token_path')
        if access_token_path == ACCESS_TOKEN_PATH.KAVIA_MANANGED.value:
            access_token = settings.GITHUB_ACCESS_TOKEN
        else:
            decrypted_scm_id = decrypt_string(repository_metadata.get('encrypted_scm_id'))
            config = scm_manager.get_configuration(tenant_id, scm_id=decrypted_scm_id)
            access_token = config.credentials.access_token
        branch_details = []
        
      
        if service == 'github':
            
            organization = repository_metadata.get('organization')
            try:
                response = await get_github_branches(repository_name, access_token, organization, page, per_page)
                return response
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"Error fetching GitHub branches: {str(e)}"
                )
                    

   
        elif service == 'gitlab':
            try:
                gl = gitlab.Gitlab(
                    config.api_url or 'https://gitlab.com',
                    oauth_token=config.credentials.access_token
                )
                gl.auth()
                
                project = gl.projects.get(repository_metadata['repositoryId'])
                
                repo_details = {
                    'repositoryName': project.name,
                    'repositoryId': str(project.id),
                    'creationDate': project.created_at,
                    'lastModifiedDate': project.last_activity_at,
                    'cloneUrlHttp': project.http_url_to_repo
                }

                branches = project.branches.list(all=True)
                branch_details = []
                
                for branch in branches:
                    commit = project.commits.get(branch.commit['id'])
                    branch_details.append({
                        'name': branch.name,
                        'lastCommitId': branch.commit['id'],
                        'lastCommitMessage': commit.message
                    })
                    
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"Error retrieving GitLab branches: {str(e)}"
                )

        else:
            raise HTTPException(
                status_code=400,
                detail="Unsupported repository service"
            )

        return {
            "service": service,
            "repositoryName": repository_name,
            "branches": branch_details
        }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/create_branch/{project_id}/")
async def create_branch(
    project_id: int,
    container_id: int,
    new_branch_name: str,
    source_branch: str = "kavia-main",
    db: NodeDB = Depends(get_node_db),
    current_user: dict = Depends(get_current_user)
):
    try:
        tenant_id = current_user.get("custom:tenant_id")
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details:
            raise HTTPException(status_code=404, detail="Project not found")
            
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        repository_metadata = project_repositories.get(str(container_id))
        
        if not repository_metadata:
            raise HTTPException(status_code=404, detail="Repository not found")
            
        service = repository_metadata.get('service', '').lower()
        repository_name = repository_metadata.get('repositoryName')
        access_token = None
        access_token_path = repository_metadata.get('access_token_path')
        
        # Get access token using the same pattern as all_branches
        if access_token_path == ACCESS_TOKEN_PATH.KAVIA_MANANGED.value:
            access_token = settings.GITHUB_ACCESS_TOKEN
        else:
            decrypted_scm_id = decrypt_string(repository_metadata.get('encrypted_scm_id'))
            config = scm_manager.get_configuration(tenant_id, scm_id=decrypted_scm_id)
            access_token = config.credentials.access_token
   
        if service == 'github':
            try:
                organization = repository_metadata.get('organization')
                
                # Use the same create_github_branch function that's used in create_github_repository
                response = await create_github_branch(
                    repository_name=repository_name,
                    access_token=access_token,
                    organization=organization,
                    source_branch=source_branch,
                    new_branch=new_branch_name
                )
                
                return {
                    "message": f"Branch '{new_branch_name}' created successfully from '{source_branch}'",
                    "branch_name": new_branch_name,
                    "source_branch": source_branch,
                    "service": service,
                    "repositoryName": repository_name,
                    "status": "success",
                    "response": response
                }
                
            except HTTPException as e:
                # Re-raise HTTP exceptions as they already have proper status codes
                raise e
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"Error creating GitHub branch: {str(e)}"
                )

        elif service == 'gitlab':
            try:
                # Get SCM configuration for GitLab
                decrypted_scm_id = decrypt_string(repository_metadata.get('encrypted_scm_id'))
                config = scm_manager.get_configuration(tenant_id, scm_id=decrypted_scm_id)
                
                gl = gitlab.Gitlab(
                    config.api_url or 'https://gitlab.com',
                    oauth_token=config.credentials.access_token
                )
                gl.auth()

                project = gl.projects.get(repository_metadata['repositoryId'])
                
                # Verify source branch exists
                try:
                    source_branch_obj = project.branches.get(source_branch)
                except:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Source branch '{source_branch}' does not exist in the repository"
                    )
                
                # Create new branch
                branch = project.branches.create({
                    'branch': new_branch_name,
                    'ref': source_branch
                })
                
                return {
                    "message": f"Branch '{new_branch_name}' created successfully from '{source_branch}'",
                    "branch_name": new_branch_name,
                    "source_branch": source_branch,
                    "commit_id": branch.commit['id'],
                    "service": service,
                    "repositoryName": repository_name,
                    "status": "success"
                }
            except HTTPException as e:
                raise e
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"Error creating GitLab branch: {str(e)}"
                )
        else:
            raise HTTPException(
                status_code=400,
                detail="Unsupported repository service"
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/change_default_branch/{project_id}/")
async def change_default_branch(
    project_id: int,
    container_id: int,
    new_default_branch: str,
    db: NodeDB = Depends(get_node_db)
):
    try:
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details:
            raise HTTPException(status_code=404, detail="Project not found")
            
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        repository_metadata = project_repositories.get(str(container_id))
        
        if not repository_metadata:
            raise HTTPException(status_code=404, detail="Repository not found")
            
        service = repository_metadata.get('service', '').lower()
        repository_name = repository_metadata.get('repositoryName')
                
        if service == 'github':
            github_client = get_github_client()
            try:
                org = github_client.get_organization("Kavia-ai")
                repo = org.get_repo(repository_name)
                
                # Verify branch exists
                try:
                    repo.get_branch(new_default_branch)
                except:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Branch '{new_default_branch}' does not exist in the repository"
                    )
                
                # Change default branch
                repo.edit(default_branch=new_default_branch)
                
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"Error changing GitHub default branch: {str(e)}"
                )
        else:
            raise HTTPException(
                status_code=400,
                detail="Unsupported repository service"
            )
            
        return {
            "message": f"Default branch successfully changed to {new_default_branch}",
            "service": service,
            "repositoryName": repository_name,
            "status": "success"
        }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 

@router.get("/repositories/{task_id}")
async def get_repositories(
    task_id: str,
    mongo_handler: MongoDBHandler = Depends(get_mongo_db)
) -> list[dict]:
    try:
        db = mongo_handler.db
        task = db[TASKS_COLLECTION_NAME].find_one({"_id": task_id})
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # Handle code-gen tasks
        if task_id.startswith("cg"):
            project_details = task.get("project_details")
            if not project_details:
                raise HTTPException(status_code=404, detail="Project details not found")
            
            project_details = json.loads(project_details)
            repository = project_details.get("current_repository")
            if not repository:
                raise HTTPException(status_code=404, detail="Repository not found")
                
            
            return [repository]
        
        # Handle code-maintenance tasks
        elif task_id.startswith("cm"):
            project_id = task.get("project_id")
            if not project_id:
                raise HTTPException(status_code=404, detail="Project ID not found")
                
            # Get repositories for the project
            project_repositories = db["project_repositories"].find_one({"project_id": project_id})
            if not project_repositories:
                raise HTTPException(status_code=404, detail="Project repositories not found")
                
            repositories = project_repositories.get("repositories", [])
            
            # Format each repository to match required structure
            formatted_repositories = [format_repository(repo) for repo in repositories]
            return formatted_repositories
        
        # Handle other types of tasks
        else:
            task_details = task.get("properties")
            if not task_details:
                raise HTTPException(status_code=404, detail="Task details not found")
            
            repositories = task_details.get("repositories")
            if not repositories:
                raise HTTPException(status_code=404, detail="Repositories not found")
            
            # Format each repository to match required structure
            formatted_repositories = [format_repository(repo) for repo in repositories]
            return formatted_repositories
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def format_repository(repo):
    """
    Formats repository data to match the required structure:
    {
        'service': 'github',
        'repositoryName': repo.name,
        'repositoryId': str(repo.id),
        'cloneUrlHttp': repo.clone_url,
        'cloneUrlSsh': repo.ssh_url,
        'organization': org if org else None,
        'encrypted_scm_id': config.encrypted_scm_id,
        'repositoryStatus': 'initialized'
    }
    """
    # Extract organization from repository name if available
    repo_name = repo.get("repository_name", "")
    org = None
    if "/" in repo_name:
        parts = repo_name.split("/")
        if len(parts) >= 2:
            org = parts[0]
            name = parts[1]
        else:
            name = repo_name
    else:
        name = repo_name
    
    # Map fields from the repository data to the required format
    return {
        "service": repo.get("service"),
        "repositoryName": name,
        "repositoryId": str(repo.get("repo_id")),
        "cloneUrlHttp": repo.get("git_url"),
        "cloneUrlSsh": repo.get("clone_url_ssh"),
        "organization": org,
        "encrypted_scm_id": repo.get("scm_id"),
        "repositoryStatus": repo.get("repositoryStatus", "initialized")
    }

class DownloadRepoRequestV2(BaseModel):
    scm_type: SCMType = Field(..., description="Type of SCM (github or gitlab)")
    scm_id: str = Field(..., description="SCM ID for authentication")
    owner: str = Field(..., description="Repository owner")
    repo: str = Field(..., description="Repository name")
    file_extension: str = Field("zip", description="File extension for download (zip or tar)")

@router.post("/download_repo/v2")
async def download_repository_v2(
    request: DownloadRepoRequestV2,
    branch: str = Query(..., description="Branch to download")
):
    try:
        # Get SCM configuration and access token using scm_id
        config = scm_manager.get_valid_configuration(request.scm_id)
        if not config:
            raise HTTPException(status_code=401, detail="Invalid SCM configuration")
        
        access_token = config.credentials.access_token
        if not access_token:
            raise HTTPException(status_code=401, detail="Invalid or expired SCM credentials")
        
        # Get the streamed response from the SCM provider
        response = download_repo(
            request.scm_type, 
            request.owner, 
            request.repo, 
            access_token, 
            request.file_extension,
            # branch=branch
        )
        
        # Create a streaming response to send to the client
        filename = f"{request.repo}-{branch}.{request.file_extension}"
        
        # Set the appropriate content type based on file extension
        content_type = "application/zip" if request.file_extension == "zip" else "application/x-tar"
        
        # Define a generator function to stream the content in chunks
        def stream_content():
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:  # filter out keep-alive new chunks
                    yield chunk
        
        # Return streaming response with the appropriate headers
        return StreamingResponse(
            stream_content(),
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error downloading repository: {str(e)}")


from typing import Any, Dict, Union, List, Optional
from pydantic import BaseModel, Field, validator
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
import asyncio
import json
from app.knowledge.redis_kg import getRedisKnowledge
from app.utils.kg_inspect.kg_tool import KgTools
from llm_wrapper.utils.base_tools import DynamicToolFactory, ToolRegistry
from llm_wrapper.core.llm_interface import LLMInterface
from app.utils.logs_utils import get_path
from code_generation_core_agent.agents.utilities import ModelNameSelector
from app.routes.code_query import generate_random_prefix

class RepositoryFieldUpdate(BaseModel):
    field_name: str = Field(..., min_length=1, max_length=100, description="Name of the field to update")
    field_value: Union[str, int, float, bool] = Field(..., description="Value to set for the field")
    
    @validator('field_name')
    def validate_field_name(cls, v):
        # Prevent injection attacks and ensure safe field names
        if not v.replace('_', '').replace('.', '').isalnum():
            raise ValueError('Field name must contain only alphanumeric characters, underscores, and dots')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "field_name": "repository_url",
                "field_value": "https://github.com/example/repo"
            }
        }

from typing import Any, Dict, Union, List, Optional
from pydantic import BaseModel, Field, validator
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
import asyncio
import json
from app.connection.establish_db_connection import get_mongo_db, NodeDB
from app.core.Settings import settings
from app.core.constants import REPOSITORIES_COLLECTION
from app.utils.auth_utils import get_current_user
import logging

logger = logging.getLogger(__name__)

class BranchManifestResponse(BaseModel):
    repository_id: str
    repository_name: str
    branch_name: str
    project_manifest: str
    latest_commit_hash: Optional[str] = None

class AllBranchManifestsResponse(BaseModel):
    project_id: int
    total_manifests: int
    manifests: List[BranchManifestResponse]

class ProjectManifestUpdateV2(BaseModel):
    project_manifest: str = Field(..., min_length=1, description="Project manifest content in YAML format")

class ProjectManifestUpdate(BaseModel):
    repository_id: str = Field(..., min_length=1, max_length=50, description="ID of the repository")
    branch_name: str = Field(..., min_length=1, max_length=100, description="Name of the branch")
    project_manifest: str = Field(..., min_length=1, description="Project manifest content in YAML format")
    
    @validator('repository_id')
    def validate_repository_id(cls, v):
        # Repository IDs are typically numeric but can be alphanumeric
        if not v.replace('-', '').replace('_', '').isalnum():
            raise ValueError('Repository ID contains invalid characters')
        return v
    
    @validator('branch_name')
    def validate_branch_name(cls, v):
        # Basic validation for branch name
        if not v.replace('-', '').replace('_', '').replace('.', '').replace('/', '').isalnum():
            raise ValueError('Branch name contains invalid characters')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "repository_id": "879276427",
                "branch_name": "main",
                "project_manifest": "overview:\n  project_name: example\n  description: Example project"
            }
        }

# Route to get all branch project manifests for a given project
@router.get("/get_all_branch_manifests/{project_id}/", response_model=AllBranchManifestsResponse)
async def get_all_branch_manifests(
    project_id: int,
    db: NodeDB = Depends(get_node_db)
):
    """
    Retrieve all branch project manifests for a given project ID.
    
    This endpoint efficiently fetches all project manifests from all branches
    across all repositories in a single database query.
    """
    try:
        # Use the correct MongoDB handler and collection name
        kg_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories'
        )
        
        # Use aggregation pipeline for efficient data retrieval
        pipeline = [
            {"$match": {"project_id": project_id}},
            {"$unwind": "$repositories"},
            {"$unwind": "$repositories.branches"},
            {"$match": {"repositories.branches.project_manifest": {"$exists": True, "$ne": None, "$ne": ""}}},
            {"$project": {
                "repository_id": "$repositories.repo_id",
                "repository_name": "$repositories.repository_name",
                "branch_name": "$repositories.branches.name",
                "project_manifest": "$repositories.branches.project_manifest",
                "latest_commit_hash": "$repositories.branches.latest_commit_hash"
            }}
        ]
        
        results = list(kg_handler.db['project_repositories'].aggregate(pipeline))
        
        if not results:
            # Check if project exists at all
            project_check = kg_handler.db['project_repositories'].find_one({"project_id": project_id})
            if not project_check:
                return JSONResponse(
                    status_code=404,
                    content={"error": f"No project found with ID {project_id}"}
                )
            else:
                return JSONResponse(
                    status_code=404,
                    content={"error": "No branches with project manifests found for this project"}
                )
        
        # Transform results into response format
        manifests = []
        for result in results:
            manifests.append(BranchManifestResponse(
                repository_id=result["repository_id"],
                repository_name=result["repository_name"],
                branch_name=result["branch_name"],
                project_manifest=result["project_manifest"],
                latest_commit_hash=result.get("latest_commit_hash")
            ))
        
        response = AllBranchManifestsResponse(
            project_id=project_id,
            total_manifests=len(manifests),
            manifests=manifests
        )
        
        return JSONResponse(
            status_code=200,
            content=response.dict()
        )
    
    except Exception as e:
        logger.error(f"Error retrieving branch manifests: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error retrieving branch manifests: {str(e)}"}
        )

# Route to get a specific branch project manifest
@router.get("/get_branch_manifest/{project_id}/")
async def get_branch_manifest(
    project_id: int,
    repository_id: str = Query(..., description="ID of the repository"),
    branch_name: str = Query(..., description="Name of the branch"),
    db: NodeDB = Depends(get_node_db)
):
    """
    Retrieve a specific branch project manifest for a given project, repository ID, and branch.
    """
    try:
        # Use the correct MongoDB handler and collection name
        kg_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories'
        )
        
        # Validate repository ID
        def validate_repo_id(repo_id: str) -> bool:
            # Repository IDs are typically numeric but can be alphanumeric
            return repo_id.replace('-', '').replace('_', '').isalnum()
        
        def validate_branch_name(name: str) -> bool:
            # Allow alphanumeric, hyphens, underscores, dots, and forward slashes
            import re
            return bool(re.match(r'^[a-zA-Z0-9\-_./]+$', name))
        
        if not validate_repo_id(repository_id):
            return JSONResponse(
                status_code=400,
                content={"error": "Repository ID contains invalid characters"}
            )
        
        if not validate_branch_name(branch_name):
            return JSONResponse(
                status_code=400,
                content={"error": "Branch name contains invalid characters"}
            )
        
        # Use aggregation pipeline for precise data retrieval
        pipeline = [
            {"$match": {"project_id": project_id}},
            {"$unwind": "$repositories"},
            {"$match": {"repositories.repo_id": repository_id}},
            {"$unwind": "$repositories.branches"},
            {"$match": {"repositories.branches.name": branch_name}},
            {"$project": {
                "repository_id": "$repositories.repo_id",
                "repository_name": "$repositories.repository_name",
                "branch_name": "$repositories.branches.name",
                "project_manifest": "$repositories.branches.project_manifest",
                "latest_commit_hash": "$repositories.branches.latest_commit_hash"
            }}
        ]
        
        result = list(kg_handler.db['project_repositories'].aggregate(pipeline))
        
        if not result:
            # Add debug information to help troubleshoot
            # First, check if project exists
            project_check = kg_handler.db['project_repositories'].find_one({"project_id": project_id})
            if not project_check:
                return JSONResponse(
                    status_code=404,
                    content={"error": f"No project found with ID {project_id}"}
                )
            
            # Check available repositories
            available_repos = []
            for repo in project_check.get('repositories', []):
                available_repos.append({
                    "repository_id": repo.get('repo_id'),
                    "repository_name": repo.get('repository_name'),
                    "branches": [branch.get('name') for branch in repo.get('branches', [])]
                })
            
            return JSONResponse(
                status_code=404,
                content={
                    "error": "No matching repository or branch found",
                    "requested_repository_id": repository_id,
                    "requested_branch": branch_name,
                    "available_repositories": available_repos
                }
            )
        
        branch_data = result[0]
        
        if not branch_data.get("project_manifest"):
            return JSONResponse(
                status_code=404,
                content={"error": "No project manifest found for the specified branch"}
            )
        
        return JSONResponse(
            status_code=200,
            content={
                "project_id": project_id,
                "repository_id": branch_data["repository_id"],
                "repository_name": branch_data["repository_name"],
                "branch_name": branch_data["branch_name"],
                "project_manifest": branch_data["project_manifest"],
                "latest_commit_hash": branch_data.get("latest_commit_hash")
            }
        )
    
    except Exception as e:
        logger.error(f"Error retrieving branch manifest: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error retrieving branch manifest: {str(e)}"}
        )

# Route to update project manifest for a specific branch
@router.put("/update_branch_manifest/{project_id}/")
async def update_branch_manifest(
    project_id: int,
    update_data: ProjectManifestUpdate,
    db: NodeDB = Depends(get_node_db)
):
    """
    Update the project manifest for a specific branch in a repository using repository ID.
    """
    try:
        # Use the correct MongoDB handler and collection name
        kg_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories'
        )
        
        # Use positional operator ($) for efficient nested array updates
        update_result = kg_handler.db['project_repositories'].update_one(
            {
                "project_id": project_id,
                "repositories.repo_id": update_data.repository_id,
                "repositories.branches.name": update_data.branch_name
            },
            {
                "$set": {
                    "repositories.$[repo].branches.$[branch].project_manifest": update_data.project_manifest
                }
            },
            array_filters=[
                {"repo.repo_id": update_data.repository_id},
                {"branch.name": update_data.branch_name}
            ]
        )
        
        if update_result.matched_count == 0:
            return JSONResponse(
                status_code=404,
                content={"error": "No matching project, repository, or branch found"}
            )
        
        if update_result.modified_count == 0:
            return JSONResponse(
                status_code=200,
                content={"message": "Project manifest was already up to date"}
            )
        
        return JSONResponse(
            status_code=200,
            content={
                "message": "Project manifest updated successfully",
                "project_id": project_id,
                "repository_id": update_data.repository_id,
                "branch_name": update_data.branch_name
            }
        )
    
    except Exception as e:
        logger.error(f"Error updating project manifest: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error updating project manifest: {str(e)}"}
        )

# Route to delete a project manifest for a specific branch
@router.delete("/delete_branch_manifest/{project_id}/")
async def delete_branch_manifest(
    project_id: int,
    repository_id: str = Query(..., description="ID of the repository"),
    branch_name: str = Query(..., description="Name of the branch"),
    db: NodeDB = Depends(get_node_db)
):
    """
    Delete (unset) the project manifest for a specific branch in a repository using repository ID.
    """
    try:
        # Use the correct MongoDB handler and collection name
        kg_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories'
        )
        
        # Validate inputs
        def validate_repo_id(repo_id: str) -> bool:
            return repo_id.replace('-', '').replace('_', '').isalnum()
        
        def validate_branch_name(name: str) -> bool:
            import re
            return bool(re.match(r'^[a-zA-Z0-9\-_./]+$', name))
        
        if not validate_repo_id(repository_id):
            return JSONResponse(
                status_code=400,
                content={"error": "Repository ID contains invalid characters"}
            )
        
        if not validate_branch_name(branch_name):
            return JSONResponse(
                status_code=400,
                content={"error": "Branch name contains invalid characters"}
            )
        
        # Use $unset to remove the project_manifest field
        update_result = kg_handler.db['project_repositories'].update_one(
            {
                "project_id": project_id,
                "repositories.repo_id": repository_id,
                "repositories.branches.name": branch_name
            },
            {
                "$unset": {
                    "repositories.$[repo].branches.$[branch].project_manifest": ""
                }
            },
            array_filters=[
                {"repo.repo_id": repository_id},
                {"branch.name": branch_name}
            ]
        )
        
        if update_result.matched_count == 0:
            return JSONResponse(
                status_code=404,
                content={"error": "No matching project, repository, or branch found"}
            )
        
        return JSONResponse(
            status_code=200,
            content={
                "message": "Project manifest deleted successfully",
                "project_id": project_id,
                "repository_id": repository_id,
                "branch_name": branch_name
            }
        )
    
    except Exception as e:
        logger.error(f"Error deleting project manifest: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error deleting project manifest: {str(e)}"}
        )

# Route to delete a project manifest for a specific branch
@router.delete("/delete_branch_manifest/{project_id}/")
async def delete_branch_manifest(
    project_id: int,
    repository_name: str = Query(..., description="Name of the repository"),
    branch_name: str = Query(..., description="Name of the branch"),
    db: NodeDB = Depends(get_node_db)
):
    """
    Delete (unset) the project manifest for a specific branch in a repository.
    """
    try:
        # Validate input parameters
        if not repository_name.replace('-', '').replace('_', '').replace('.', '').replace('/', '').isalnum():
            return JSONResponse(
                status_code=400,
                content={"error": "Repository name contains invalid characters"}
            )
        
        if not branch_name.replace('-', '').replace('_', '').replace('.', '').replace('/', '').isalnum():
            return JSONResponse(
                status_code=400,
                content={"error": "Branch name contains invalid characters"}
            )
        
        db = get_mongo_db().db
        
        # Use $unset to remove the project_manifest field
        update_result = db[REPOSITORIES_COLLECTION].update_one(
            {
                "project_id": project_id,
                "repositories.repository_name": repository_name,
                "repositories.branches.name": branch_name
            },
            {
                "$unset": {
                    "repositories.$[repo].branches.$[branch].project_manifest": ""
                }
            },
            array_filters=[
                {"repo.repository_name": repository_name},
                {"branch.name": branch_name}
            ]
        )
        
        if update_result.matched_count == 0:
            return JSONResponse(
                status_code=404,
                content={"error": "No matching project, repository, or branch found."}
            )
        
        return JSONResponse(
            status_code=200,
            content={
                "message": "Project manifest deleted successfully.",
                "project_id": project_id,
                "repository_name": repository_name,
                "branch_name": branch_name
            }
        )
    
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Error deleting project manifest: {str(e)}"}
        )

# Existing routes from your original code
@router.put("/update_repository_field/{project_id}/")
async def update_repository_field(
    project_id: int,
    update_data: RepositoryFieldUpdate,
    db: NodeDB = Depends(get_node_db)
):
    try:
        field_name = update_data.field_name
        field_value = update_data.field_value
        db = get_mongo_db().db
        update = db[REPOSITORIES_COLLECTION].update_one(
            {"project_id": project_id},
            {"$set": {field_name: field_value}}
        )
        if update.modified_count == 0:
            return JSONResponse(
                status_code=404,
                content={"error": "No repository found for the given project ID."}
            )
        return JSONResponse(
            status_code=200,
            content={"message": "Repository field updated successfully."}
        )

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Error updating repository field: {str(e)}"}
        )

@router.get("/get_repository_field/{project_id}/")
async def get_repository_field(
    project_id: int,
    field_name: str = Query(..., description="Name of the field to retrieve"),
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        # Validate field name to prevent injection attacks
        if not field_name.replace('_', '').replace('.', '').isalnum():
            return JSONResponse(
                status_code=400,
                content={"error": "Field name must contain only alphanumeric characters, underscores, and dots"}
            )

        db = get_mongo_db().db
        repository = db[REPOSITORIES_COLLECTION].find_one(
            {"project_id": project_id},
            {field_name: 1, "_id": 0}
        )

        if not repository:
            if field_name == 'project_manifest':
                #fallback to node_db
                try:
                    project = await node_db.get_node_by_id(project_id)
                    project_details = project.get("properties")
                    project_manifest = project_details.get("Manifest", "")
                    project_manifest = json_to_yaml(project_manifest)
                    return JSONResponse(
                        status_code=200,
                        content={
                            "project_id": project_id,
                            "field_name": field_name,
                            "field_value": project_manifest
                        }
                    )
                except Exception as e:
                    return JSONResponse(
                        status_code=404,
                        content={"error": f"Field '{field_name}' not found in repository record."}
                    )
            return JSONResponse(
                status_code=404,
                content={"error": "No repository found for the given project ID."}
            )

        if field_name not in repository:
            if field_name == 'project_manifest':
                #fallback to node_db
                try:
                    project = await node_db.get_node_by_id(project_id)
                    project_details = project.get("properties")
                    project_manifest = project_details.get("Manifest", "")
                    project_manifest = json_to_yaml(project_manifest)
                    return JSONResponse(
                        status_code=200,
                        content={
                            "project_id": project_id,
                            "field_name": field_name,
                            "field_value": project_manifest
                        }
                    )
                except Exception as e:
                    return JSONResponse(
                        status_code=404,
                        content={"error": f"Field '{field_name}' not found in repository record."}
                    )
            return JSONResponse(
                status_code=404,
                content={"error": f"Field '{field_name}' not found in repository record."}
            )

        return JSONResponse(
            status_code=200,
            content={
                "project_id": project_id,
                "field_name": field_name,
                "field_value": repository[field_name]
            }
        )

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Error retrieving repository field: {str(e)}"}
        )

@router.get("/get_repository_record/{project_id}/")
async def get_repository_record(
    project_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        db = get_mongo_db().db
        repository = db[REPOSITORIES_COLLECTION].find_one({"project_id": project_id})

        if not repository:
            return JSONResponse(
                status_code=404,
                content={"error": "No repository found for the given project ID."}
            )

        # Convert ObjectId to string for JSON serialization
        if "_id" in repository:
            repository["_id"] = str(repository["_id"])

        return JSONResponse(
            status_code=200,
            content={"repository": repository}
        )

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Error retrieving repository record: {str(e)}"}
        )
 
@router.get("/get_project_manifest/{project_id}/")
async def get_project_manifest(
    project_id: int,
    container_ids: Optional[List[str]] = Query(None, description="List of container IDs to include in the manifest"),
    all_repositories: bool = Query(False, description="Include all repositories in the manifest"),
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        project = await node_db.get_node_by_id(project_id)
        project_details = project.get("properties")
        project_manifest = project_details.get("Manifest", "")
        
        print("Project Manifest:", project_manifest)
        if not project_manifest:
            #pass TODO: Handle case where manifest is empty
            try:
                project_manifest = await get_project_manifest_for_generation(project_id, container_ids, node_db=node_db)
            except Exception as e:
                import traceback
                traceback.print_exc()
                print(f"Error generating project manifest: {str(e)}")
            
        if isinstance(project_manifest, str):
            project_manifest = yaml_to_json(project_manifest)
            project_manifest = json.dumps(project_manifest, indent=2)
            
        project_manifest = json_to_yaml(project_manifest)
        
        return JSONResponse(
            status_code=200,
            content={
                "project_id": project_id,
                "field_name": "project_manifest",
                "field_value": project_manifest
            }
        )
        
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Error retrieving project manifest: {str(e)}"}
        )
        
from typing import Dict, List, Any, Optional
import json
import copy

def deep_merge_dicts(existing: Dict[str, Any], updates: Dict[str, Any]) -> Dict[str, Any]:
    """
    Perform a deep merge of dictionaries, updating only changed values.
    
    Args:
        existing: The existing dictionary
        updates: The updates to apply
        
    Returns:
        Merged dictionary with selective updates
    """
    result = copy.deepcopy(existing)
    
    for key, value in updates.items():
        if key not in result:
            result[key] = value
        elif isinstance(value, dict) and isinstance(result[key], dict):
            result[key] = deep_merge_dicts(result[key], value)
        elif isinstance(value, list) and isinstance(result[key], list):
            # Handle list merging based on context
            if key == "containers":
                result[key] = merge_containers(result[key], value)
            else:
                result[key] = value  # Replace list entirely for non-container lists
        else:
            # Only update if values are different
            if result[key] != value:
                result[key] = value
                
    return result

def merge_containers(existing_containers: List[Dict[str, Any]], 
                    updated_containers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Merge container lists by container_name, updating only changed containers.
    
    Args:
        existing_containers: List of existing containers
        updated_containers: List of container updates
        
    Returns:
        Merged container list
    """
    # Create lookup maps for efficient access
    existing_map = {container.get("container_name"): container 
                   for container in existing_containers}
    updated_map = {container.get("container_name"): container 
                  for container in updated_containers}
    
    result = []
    
    # Process all container names (existing + new)
    all_container_names = set(existing_map.keys()) | set(updated_map.keys())
    
    for container_name in all_container_names:
        if container_name in updated_map:
            if container_name in existing_map:
                # Merge existing container with updates
                merged_container = deep_merge_dicts(
                    existing_map[container_name], 
                    updated_map[container_name]
                )
                result.append(merged_container)
            else:
                # New container
                result.append(updated_map[container_name])
        else:
            # Keep existing container unchanged
            result.append(existing_map[container_name])
    
    return result

def has_meaningful_changes(existing: Dict[str, Any], updates: Dict[str, Any]) -> bool:
    """
    Check if there are meaningful changes between existing and updated data.
    
    Args:
        existing: Existing manifest data
        updates: Updated manifest data
        
    Returns:
        True if there are changes, False otherwise
    """
    def normalize_for_comparison(data):
        """Normalize data for comparison (handle None, empty strings, etc.)"""
        if isinstance(data, dict):
            return {k: normalize_for_comparison(v) for k, v in data.items() 
                   if v is not None and v != ""}
        elif isinstance(data, list):
            return [normalize_for_comparison(item) for item in data]
        return data
    
    normalized_existing = normalize_for_comparison(existing)
    normalized_updates = normalize_for_comparison(updates)
    
    return normalized_existing != normalized_updates

@router.put("/update_project_manifest/{project_id}/")
async def update_project_manifest(
    project_id: int,
    manifest_data: ProjectManifestUpdateV2,
    node_db: NodeDB = Depends(get_node_db)
):
    """
    Update project manifest with selective field updates.
    Only modified fields are updated, preserving existing data.
    """
    try:
        # Get existing project and manifest
        project = await node_db.get_node_by_id(project_id)
        if not project:
            return JSONResponse(
                status_code=404,
                content={"error": f"Project with ID {project_id} not found"}
            )
        
        project_details = project.get("properties", {})
        existing_manifest_str = project_details.get("Manifest", "{}")
        
        # Parse existing manifest
        try:
            existing_manifest = json.loads(existing_manifest_str) if existing_manifest_str else {}
        except json.JSONDecodeError:
            # If existing manifest is YAML, convert it first
            existing_manifest = yaml_to_json(existing_manifest_str)
        
        # Parse incoming manifest
        incoming_manifest = yaml_to_json(manifest_data.project_manifest)
        
        # Check if there are meaningful changes
        if not has_meaningful_changes(existing_manifest, incoming_manifest):
            return JSONResponse(
                status_code=200,
                content={
                    "message": "No changes detected, manifest not updated",
                    "project_id": project_id
                }
            )
        
        # Perform selective merge
        merged_manifest = deep_merge_dicts(existing_manifest, incoming_manifest)
        
        # Update the project manifest in the database
        await node_db.update_node_by_id(
            project_id,
            {"Manifest": json.dumps(merged_manifest, indent=2)}
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "message": "Project manifest updated successfully",
                "project_id": project_id,
                "updated_fields": list(incoming_manifest.keys())
            }
        )
        
    except ValueError as ve:
        return JSONResponse(
            status_code=400,
            content={"error": f"Invalid manifest format: {str(ve)}"}
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Error updating project manifest: {str(e)}"}
        )

# Optional: Add a PATCH endpoint for explicit partial updates
@router.patch("/update_project_manifest/{project_id}/")
async def patch_project_manifest(
    project_id: int,
    manifest_data: ProjectManifestUpdateV2,
    node_db: NodeDB = Depends(get_node_db)
):
    """
    Explicit PATCH endpoint for partial manifest updates.
    Alias for the selective update functionality.
    """
    return await update_project_manifest(project_id, manifest_data, node_db)


@router.get("/re-construct_manifest/{project_id}/")
async def reconstruct_project_manifest(
    project_id: int,
    container_ids: Optional[List[str]] = Query(None, description="List of container IDs to include in the manifest"),
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        # Get the project manifest from the database
        project = await node_db.get_node_by_id(project_id)
        
        if not project:
            return JSONResponse(
                status_code=404,
                content={"error": f"Project with ID {project_id} not found"}
            )
         
        # Reconstruct the manifest based on provided container IDs
        reconstructed_manifest = await get_project_manifest_for_generation(
            project_id, container_ids, node_db=node_db
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "project_id": project_id,
                "reconstructed_manifest": reconstructed_manifest
            }
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Error reconstructing project manifest: {str(e)}"}
        )
           
@router.get("/generate_manifest/{project_id}/")
async def generate_project_manifest(
    project_id: int,
    current_user=Depends(get_current_user)
):
    """
    Generate a project manifest by analyzing repository codebase with SSE streaming.

    Args:
        project_id: The project ID to analyze
        current_user: Current authenticated user

    Returns:
        StreamingResponse: SSE stream with manifest generation progress and final YAML manifest
    """
    try:
        user_id = current_user.get("cognito:username")

        # Get repository information
        mongo_db = get_mongo_db().db[REPOSITORIES_COLLECTION]

        repository = mongo_db.find_one({"project_id": project_id})
        if not repository:
            raise HTTPException(status_code=404, detail="Repository not found")

        # Generate session ID for knowledge retrieval (will be updated with build_ids later)
        session_id_prefix = f"manifest-{generate_random_prefix()}"

        async def stream_manifest_generation():
            try:
                # Send initial status
                yield f"data: {json.dumps({'status': 'starting', 'message': 'Initializing manifest generation...'})}\n\n"

                # Check if knowledge session exists
                yield f"data: {json.dumps({'status': 'progress', 'message': 'Checking knowledge session...'})}\n\n"
           

                # Extract build_ids from repository branches
                build_ids = []
                repositories = repository.get('repositories', [])

                for repo in repositories:
                    branches = repo.get('branches', [])
                    for branch in branches:
                        builds = branch.get('builds', {})
                        build_id = builds.get('build_id')
                        if build_id:
                            build_ids.append(build_id)

                if not build_ids:
                    yield f"data: {json.dumps({'status': 'error', 'message': 'No build IDs found in repository. Please ensure the repository has been built.'})}\n\n"
                    return

                yield f"data: {json.dumps({'status': 'progress', 'message': f'Found {len(build_ids)} build(s). Checking knowledge session...'})}\n\n"

                # Generate session_id with build_ids (similar to code_query.py pattern)
                sorted_build_ids = sorted(build_ids)
                build_ids_string = "-".join(map(str, sorted_build_ids))
                session_id = f"{session_id_prefix}-{build_ids_string}"

                # Try to get existing knowledge session with build_ids list
                knowledge_session = getRedisKnowledge(id=sorted_build_ids, verbose=True)

                if not knowledge_session:
                    yield f"data: {json.dumps({'status': 'error', 'message': 'No knowledge session found for this repository. Please build the knowledge base first.'})}\n\n"
                    return

                yield f"data: {json.dumps({'status': 'progress', 'message': 'Knowledge session found. Setting up analysis tools...'})}\n\n"

                # Setup KG tools for codebase analysis
                general_registry = ToolRegistry()
                general_registry.register_tool("KgTools", KgTools)
                general_factory = DynamicToolFactory(general_registry)
                general_exec_agent = general_factory.create_dynamic_tool(["KgTools"])

                # Initialize KG tool with session
                kg_tool = general_exec_agent(
                    base_path=repository.get('local_path', '/tmp'),
                    logger=None,
                    user_id=session_id
                )

                yield f"data: {json.dumps({'status': 'progress', 'message': 'Analysis tools ready. Starting codebase analysis...'})}\n\n"

                # Setup LLM interface
                llm = LLMInterface(
                    str(get_path()),
                    'knowledge',
                    user_id,
                    int(project_id),
                    'manifest_generation'
                )

                # Model selection
                model_selector = ModelNameSelector("gpt-4.1")
                selected_model = model_selector.get_selected_model()

               

                # Prepare messages for LLM
                messages = [
                    {
                        "role": "system",
                        "content": manifest_prompt
                    },
                    {
                        "role": "user",
                        "content": f"Please analyze the codebase for project ID {project_id} and generate a comprehensive project manifest in YAML format."
                    }
                ]

                yield f"data: {json.dumps({'status': 'progress', 'message': 'Starting LLM analysis...'})}\n\n"

                # Stream LLM response
                response = await llm.llm_interaction_wrapper(
                    messages=messages,
                    user_prompt=None,
                    system_prompt=None,
                    model=selected_model,
                    stream=True,
                    response_format={"type": "text"},
                    function_schemas=kg_tool.function_schemas,
                    function_executor=kg_tool.function_executor
                )

                manifest_content = ""
                async for chunk in response:
                    if chunk:
                        manifest_content += chunk
                        # Send progress updates
                        yield f"data: {json.dumps({'status': 'streaming', 'content': chunk, 'partial_manifest': manifest_content})}\n\n"

                from app.utils.yaml_extractor import process_manifest_response
                # Use the utility function to extract and validate YAML
                final_response = process_manifest_response(
                    manifest_content=manifest_content,
                    project_id=project_id,
                    validate_structure=True
                )

                # Send the final processed response
                yield f"data: {json.dumps(final_response)}\n\n"

                # If successful, also send a separate clean YAML response
                if final_response['status'] == 'complete':
                    yield f"data: {json.dumps({'status': 'yaml_ready', 'message': 'Clean YAML manifest ready for download', 'yaml_content': final_response['manifest']['content']})}\n\n"
                
            except Exception as e:
                logger.error(f"Error in manifest generation: {str(e)}")
                yield f"data: {json.dumps({'status': 'error', 'message': f'Error generating manifest: {str(e)}'})}\n\n"

        return StreamingResponse(
            stream_manifest_generation(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
                "X-Accel-Buffering": "no"
            }
        )

    except Exception as e:
        logger.error(f"Error setting up manifest generation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to setup manifest generation: {str(e)}")