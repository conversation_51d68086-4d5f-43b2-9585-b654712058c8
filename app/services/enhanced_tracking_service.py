# app/services/enhanced_tracking_service.py
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Optional
import asyncio
from app.connection.establish_db_connection import get_mongo_db, MongoDBHandler
from ..models.tracking_model import APITrackingLog, DailyTenantStats
from app.services.geolocation_service import geolocation_service
from app.telemetry.logger_config import get_logger
import os
from app.core.Settings import settings
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
MONGO_URI = settings.MONGO_CONNECTION_URI
logger = get_logger(__name__)

class EnhancedTrackingService:
    def __init__(self):
        self.db_handler = None
        self.collection_name = "request_tracker"
    
    def _get_root_database_name(self) -> str:
        """Extract root database name from current database name following your pattern"""
        return KAVIA_ROOT_DB_NAME
            
    
    async def initialize(self):
        """Initialize the service with database connection"""
        try:         
            # Get root database name following your pattern
            self.root_db_name = self._get_root_database_name()
            
            # Initialize MongoDB handler for tracking
            self.db_handler = MongoDBHandler(
                mongo_connection_uri=MONGO_URI,  # Using existing connection
                db_name=self.root_db_name,
                collection_name=self.collection_name
            )
            
            # Set the root database
            self.db_handler.change_db(self.root_db_name)
            self.db_handler.change_collection(self.collection_name)
            
            logger.info(f"✅ Enhanced tracking service initialized")
            logger.info(f"📊 Root DB: {self.root_db_name}")
            logger.info(f"📝 Collection: {self.collection_name}")
            
        except Exception as e:
            logger.error(f"❌ Error initializing tracking service: {str(e)}")
            raise
    
    async def store_tracking_data(self, log_data: APITrackingLog):
        """Store API tracking data with location information"""
        try:
            # Initialize if not already done
            if not self.db_handler:
                await self.initialize()
            
            # Get location information (only for non-localhost IPs)
            if not self._is_localhost_ip(log_data.client_ip):
                location_data = await geolocation_service.get_location(log_data.client_ip)
                if location_data:
                    log_data.country = location_data.get("country")
                    log_data.region = location_data.get("region") 
                    log_data.city = location_data.get("city")
                    log_data.latitude = location_data.get("latitude")
                    log_data.longitude = location_data.get("longitude")
                    log_data.timezone_name = location_data.get("timezone_name")
                    log_data.isp = location_data.get("isp")
            else:
                # Set default values for localhost
                # Set default values for localhost
               log_data.country = "Local"
               log_data.region = "Local"
               log_data.city = "Local"
           
            # Get the database connection following your pattern
            mongo_client = get_mongo_db()
            root_db = mongo_client.client[self.root_db_name]
            
            # Store main log using your MongoDB handler
            log_dict = log_data.dict()
            
            # Log the exact database and collection being used
            logger.info(f"💾 Storing tracking data:")
            # logger.info(f"   Current DB Context: {current_db_name}")
            logger.info(f"   Root DB: {self.root_db_name}")
            logger.info(f"   Collection: {self.collection_name}")
            logger.info(f"   Document: Tenant={log_data.tenant_id}, User={log_data.user_email}, Endpoint={log_data.method} {log_data.endpoint}")
            
            await self.db_handler.insert(log_dict, root_db)
            
            logger.info(f"✅ Successfully stored tracking data to {self.root_db_name}.{self.collection_name}")
            
            # Update aggregations asynchronously (optional, but skip errors)
            try:
                asyncio.create_task(self._update_daily_stats_safe(log_data, root_db))
            except Exception as e:
                logger.warning(f"⚠️  Daily stats update failed (non-critical): {str(e)}")
            
        except Exception as e:
            logger.error(f"❌ Error storing tracking data: {str(e)}")
            logger.error(f"❌ Target was: DB={getattr(self, 'root_db_name', 'unknown')}, Collection={self.collection_name}")
    
    def _is_localhost_ip(self, ip: str) -> bool:
        """Check if IP is localhost"""
        localhost_ips = ["127.0.0.1", "::1", "localhost", "0.0.0.0"]
        return ip in localhost_ips or ip.startswith("192.168.") or ip.startswith("10.") or ip.startswith("172.")
    
    async def _update_daily_stats_safe(self, log_data: APITrackingLog, root_db):
        """Safely update daily tenant statistics"""
        try:
            stats_collection = "daily_tenant_stats"
            
            # Create daily stats handler
            stats_handler = MongoDBHandler(
                mongo_connection_uri=None,
                db_name=self.root_db_name,
                collection_name=stats_collection
            )
            stats_handler.change_db(self.root_db_name)
            stats_handler.change_collection(stats_collection)
            
            # Update/create daily stats
            stats_filter = {
                "tenant_id": log_data.tenant_id,
                "date": log_data.date_partition
            }
            
            # Prepare update data
            update_data = {
                "tenant_id": log_data.tenant_id,
                "date": log_data.date_partition,
                "updated_at": datetime.now(timezone.utc)
            }
            
            # Use upsert to create or update
            await stats_handler.update_one(stats_filter, update_data, upsert=True, db=root_db)
            
        except Exception as e:
            logger.warning(f"Error updating daily stats (non-critical): {str(e)}")
    
    async def get_dashboard_data(self, tenant_id: str, days: int = 7) -> Dict:
        """Get comprehensive dashboard data for a tenant"""
        try:
            # Initialize if not already done
            if not self.db_handler:
                await self.initialize()
            
            # Get root database following your pattern
            mongo_client = get_mongo_db()
            
            # Get root database name
            root_db_name = self._get_root_database_name()
            root_db = mongo_client.client[root_db_name]
            
            logger.info(f"📊 Getting dashboard data:")
            # logger.info(f"   Current DB Context: {current_db_context}")
            logger.info(f"   Root DB: {root_db_name}")
            logger.info(f"   Collection: {self.collection_name}")
            logger.info(f"   Tenant: {tenant_id}")
            
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days)
            
            # Get data using simple operations
            summary_data = await self._get_summary_metrics_simple(tenant_id, start_date, end_date, root_db)
            daily_stats = await self._get_daily_stats_simple(tenant_id, start_date, end_date, root_db)
            hourly_trends = await self._get_hourly_trends_simple(tenant_id, start_date, end_date, root_db)
            top_endpoints = await self._get_top_endpoints_simple(tenant_id, start_date, end_date, root_db)
            top_users = await self._get_top_users_simple(tenant_id, start_date, end_date, root_db)
            status_dist = await self._get_status_distribution_simple(tenant_id, start_date, end_date, root_db)
            geographic_data = await self._get_geographic_data_simple(tenant_id, start_date, end_date, root_db)
            
            logger.info(f"✅ Retrieved dashboard data: {summary_data.get('total_requests', 0)} requests found")
            
            return {
                "tenant_id": tenant_id,
                "period": {
                    "days": days,
                    "start_date": start_date.strftime("%Y-%m-%d"),
                    "end_date": end_date.strftime("%Y-%m-%d")
                },
                "summary": summary_data,
                "daily_stats": daily_stats,
                "hourly_trends": hourly_trends,
                "top_endpoints": top_endpoints,
                "top_users": top_users,
                "status_distribution": status_dist,
                "geographic_data": geographic_data
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting dashboard data: {str(e)}")
            return self._get_empty_dashboard_data(tenant_id, days)
    
    async def _get_summary_metrics_simple(self, tenant_id: str, start_date: datetime, end_date: datetime, db) -> Dict:
        """Get summary metrics using MongoDB aggregation pipeline"""
        try:
            collection = db[self.collection_name]
            
            pipeline = [
                {
                    "$match": {
                        "tenant_id": tenant_id,
                        "created_at": {"$gte": start_date, "$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "total_requests": {"$sum": 1},
                        "unique_users": {"$addToSet": "$user_id"},
                        "total_response_time": {"$sum": "$duration_ms"},
                        "total_errors": {
                            "$sum": {
                                "$cond": [{"$gte": ["$status_code", 400]}, 1, 0]
                            }
                        }
                    }
                },
                {
                    "$project": {
                        "total_requests": 1,
                        "unique_users": {"$size": "$unique_users"},
                        "avg_response_time": {
                            "$cond": [
                                {"$gt": ["$total_requests", 0]},
                                {"$divide": ["$total_response_time", "$total_requests"]},
                                0
                            ]
                        },
                        "total_errors": 1,
                        "error_rate": {
                            "$cond": [
                                {"$gt": ["$total_requests", 0]},
                                {"$multiply": [{"$divide": ["$total_errors", "$total_requests"]}, 100]},
                                0
                            ]
                        }
                    }
                }
            ]
            
            result = list(collection.aggregate(pipeline))
            
            if not result:
                return {
                    "total_requests": 0,
                    "unique_users": 0,
                    "avg_response_time": 0,
                    "total_errors": 0,
                    "error_rate": 0
                }
            
            data = result[0]
            return {
                "total_requests": data.get("total_requests", 0),
                "unique_users": data.get("unique_users", 0),
                "avg_response_time": round(data.get("avg_response_time", 0), 2),
                "total_errors": data.get("total_errors", 0),
                "error_rate": round(data.get("error_rate", 0), 2)
            }
            
        except Exception as e:
            logger.error(f"Error getting summary metrics: {str(e)}")
            return {
                "total_requests": 0,
                "unique_users": 0,
                "avg_response_time": 0,
                "total_errors": 0,
                "error_rate": 0
            }

    async def _get_daily_stats_simple(self, tenant_id: str, start_date: datetime, end_date: datetime, db) -> List[Dict]:
        """Get daily statistics using MongoDB aggregation pipeline"""
        try:
            collection = db[self.collection_name]
            
            pipeline = [
                {
                    "$match": {
                        "tenant_id": tenant_id,
                        "created_at": {"$gte": start_date, "$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": "$date_partition",
                        "requests": {"$sum": 1},
                        "unique_users": {"$addToSet": "$user_id"},
                        "total_response_time": {"$sum": "$duration_ms"},
                        "errors": {
                            "$sum": {
                                "$cond": [{"$gte": ["$status_code", 400]}, 1, 0]
                            }
                        }
                    }
                },
                {
                    "$project": {
                        "date": "$_id",
                        "requests": 1,
                        "unique_users": {"$size": "$unique_users"},
                        "avg_response_time": {
                            "$cond": [
                                {"$gt": ["$requests", 0]},
                                {"$divide": ["$total_response_time", "$requests"]},
                                0
                            ]
                        },
                        "errors": 1,
                        "error_rate": {
                            "$cond": [
                                {"$gt": ["$requests", 0]},
                                {"$multiply": [{"$divide": ["$errors", "$requests"]}, 100]},
                                0
                            ]
                        }
                    }
                },
                {"$sort": {"date": 1}}
            ]
            
            result = list(collection.aggregate(pipeline))
            
            # Format the results
            formatted_result = []
            for item in result:
                formatted_result.append({
                    "date": item.get("date"),
                    "requests": item.get("requests", 0),
                    "unique_users": item.get("unique_users", 0),
                    "avg_response_time": round(item.get("avg_response_time", 0), 2),
                    "errors": item.get("errors", 0),
                    "error_rate": round(item.get("error_rate", 0), 2)
                })
            
            return formatted_result
            
        except Exception as e:
            logger.error(f"Error getting daily stats: {str(e)}")
            return []

    async def _get_hourly_trends_simple(self, tenant_id: str, start_date: datetime, end_date: datetime, db) -> List[Dict]:
        """Get hourly trends using MongoDB aggregation pipeline"""
        try:
            collection = db[self.collection_name]
            
            pipeline = [
                {
                    "$match": {
                        "tenant_id": tenant_id,
                        "created_at": {"$gte": start_date, "$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": "$hour_partition",
                        "requests": {"$sum": 1}
                    }
                }
            ]
            
            result = list(collection.aggregate(pipeline))
            
            # Create hourly data with all 24 hours
            hourly_data = {item["_id"]: item["requests"] for item in result}
            
            formatted_result = []
            for hour in range(24):
                formatted_result.append({
                    "hour": f"{hour:02d}:00",
                    "requests": hourly_data.get(hour, 0)
                })
            
            return formatted_result
            
        except Exception as e:
            logger.error(f"Error getting hourly trends: {str(e)}")
            return [{"hour": f"{h:02d}:00", "requests": 0} for h in range(24)]

    async def _get_top_endpoints_simple(self, tenant_id: str, start_date: datetime, end_date: datetime, db) -> List[Dict]:
        """Get top endpoints using MongoDB aggregation pipeline"""
        try:
            collection = db[self.collection_name]
            
            pipeline = [
                {
                    "$match": {
                        "tenant_id": tenant_id,
                        "created_at": {"$gte": start_date, "$lte": end_date}
                    }
                },
                {
                    "$addFields": {
                        "endpoint_key": {"$concat": ["$method", " ", "$endpoint"]}
                    }
                },
                {
                    "$group": {
                        "_id": "$endpoint_key",
                        "requests": {"$sum": 1},
                        "total_response_time": {"$sum": "$duration_ms"},
                        "errors": {
                            "$sum": {
                                "$cond": [{"$gte": ["$status_code", 400]}, 1, 0]
                            }
                        }
                    }
                },
                {
                    "$project": {
                        "endpoint": "$_id",
                        "requests": 1,
                        "avg_response_time": {
                            "$cond": [
                                {"$gt": ["$requests", 0]},
                                {"$divide": ["$total_response_time", "$requests"]},
                                0
                            ]
                        },
                        "error_rate": {
                            "$cond": [
                                {"$gt": ["$requests", 0]},
                                {"$multiply": [{"$divide": ["$errors", "$requests"]}, 100]},
                                0
                            ]
                        }
                    }
                },
                {"$sort": {"requests": -1}},
                {"$limit": 10}
            ]
            
            result = list(collection.aggregate(pipeline))
            
            # Format the results
            formatted_result = []
            for item in result:
                formatted_result.append({
                    "endpoint": item.get("endpoint"),
                    "requests": item.get("requests", 0),
                    "avg_response_time": round(item.get("avg_response_time", 0), 2),
                    "error_rate": round(item.get("error_rate", 0), 2)
                })
            
            return formatted_result
            
        except Exception as e:
            logger.error(f"Error getting top endpoints: {str(e)}")
            return []

    async def _get_top_users_simple(self, tenant_id: str, start_date: datetime, end_date: datetime, db) -> List[Dict]:
        """Get top users using MongoDB aggregation pipeline"""
        try:
            collection = db[self.collection_name]
            
            pipeline = [
                {
                    "$match": {
                        "tenant_id": tenant_id,
                        "created_at": {"$gte": start_date, "$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": "$user_id",
                        "requests": {"$sum": 1},
                        "user_email": {"$first": "$user_email"},
                        "username": {"$first": "$username"},
                        "user_role": {"$first": "$user_role"}
                    }
                },
                {"$sort": {"requests": -1}},
                {"$limit": 10}
            ]
            
            result = list(collection.aggregate(pipeline))
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting top users: {str(e)}")
            return []

    async def _get_status_distribution_simple(self, tenant_id: str, start_date: datetime, end_date: datetime, db) -> Dict:
        """Get status distribution using MongoDB aggregation pipeline"""
        try:
            collection = db[self.collection_name]
            
            pipeline = [
                {
                    "$match": {
                        "tenant_id": tenant_id,
                        "created_at": {"$gte": start_date, "$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": {"$toString": "$status_code"},
                        "count": {"$sum": 1}
                    }
                }
            ]
            
            result = list(collection.aggregate(pipeline))
            
            return {item["_id"]: item["count"] for item in result}
            
        except Exception as e:
            logger.error(f"Error getting status distribution: {str(e)}")
            return {}

    async def _get_geographic_data_simple(self, tenant_id: str, start_date: datetime, end_date: datetime, db) -> List[Dict]:
        """Get geographic data using MongoDB aggregation pipeline"""
        try:
            collection = db[self.collection_name]
            
            pipeline = [
                {
                    "$match": {
                        "tenant_id": tenant_id,
                        "created_at": {"$gte": start_date, "$lte": end_date},
                        "country": {"$nin": ["Unknown", "Local", None]}
                    }
                },
                {
                    "$group": {
                        "_id": {
                            "country": "$country",
                            "region": "$region"
                        },
                        "requests": {"$sum": 1}
                    }
                },
                {"$sort": {"requests": -1}},
                {"$limit": 20}
            ]
            
            result = list(collection.aggregate(pipeline))
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting geographic data: {str(e)}")
            return []
    
    def _get_empty_dashboard_data(self, tenant_id: str, days: int) -> Dict:
        """Return empty dashboard data structure"""
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=days)
        
        return {
            "tenant_id": tenant_id,
            "period": {
                "days": days,
                "start_date": start_date.strftime("%Y-%m-%d"),
                "end_date": end_date.strftime("%Y-%m-%d")
            },
            "summary": {
                "total_requests": 0,
                "unique_users": 0,
                "avg_response_time": 0,
                "total_errors": 0,
                "error_rate": 0
            },
            "daily_stats": [],
            "hourly_trends": [{"hour": f"{h:02d}:00", "requests": 0} for h in range(24)],
            "top_endpoints": [],
            "top_users": [],
            "status_distribution": {},
            "geographic_data": []
        }
    
    async def get_all_tenants(self, current_db_context: str = None) -> List[Dict]:
        """Get all tenants with basic stats"""
        try:
            # Initialize if not already done
            if not self.db_handler:
                await self.initialize()
            
            # Get root database following your pattern
            mongo_client = get_mongo_db()
            
            root_db_name = self._get_root_database_name()
            root_db = mongo_client.client[root_db_name]
            
            collection = root_db[self.collection_name]
            
            # Get all documents
            docs = list(collection.find())
            
            logger.info(f"📊 Found {len(docs)} total tracking documents across all tenants")
            
            # Group by tenant
            tenant_data = {}
            for doc in docs:
                tenant_id = doc.get("tenant_id", "unknown")
                if tenant_id not in tenant_data:
                    tenant_data[tenant_id] = {
                        "total_requests": 0,
                        "users": set(),
                        "response_times": [],
                        "errors": 0,
                        "tenant_name": doc.get("tenant_name", tenant_id)
                    }
                
                tenant_data[tenant_id]["total_requests"] += 1
                tenant_data[tenant_id]["users"].add(doc.get("user_id", ""))
                tenant_data[tenant_id]["response_times"].append(doc.get("duration_ms", 0))
                if doc.get("status_code", 200) >= 400:
                    tenant_data[tenant_id]["errors"] += 1
            
            # Convert to list
            result = []
            for tenant_id, data in tenant_data.items():
                avg_response_time = sum(data["response_times"]) / len(data["response_times"]) if data["response_times"] else 0
                error_rate = (data["errors"] / data["total_requests"]) * 100 if data["total_requests"] > 0 else 0
                
                result.append({
                    "id": tenant_id,
                    "name": data["tenant_name"],
                    "total_requests": data["total_requests"],
                    "unique_users": len(data["users"]),
                    "avg_response_time": round(avg_response_time, 2),
                    "error_count": data["errors"],
                    "error_rate": round(error_rate, 2)
                })
            
            logger.info(f"✅ Retrieved {len(result)} tenants with stats")
            return sorted(result, key=lambda x: x["total_requests"], reverse=True)
            
        except Exception as e:
            logger.error(f"Error  all tenants: {str(e)}")
            return []

    # Global service instance
enhanced_tracking_service = EnhancedTrackingService()