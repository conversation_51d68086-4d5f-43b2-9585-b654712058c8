# services/supabase_service.py
import httpx
import secrets
import base64
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from urllib.parse import urlencode
import os
from app.core.Settings import settings

class SupabaseService:
    def __init__(self):
        self.client_id = settings.SUPABASE_OAUTH_CLIENT_ID
        self.client_secret = settings.SUPABASE_OAUTH_CLIENT_SECRET
        self.redirect_uri = settings.SUPABASE_OAUTH_REDIRECT_URI
        self.authorize_url = settings.SUPABASE_OAUTH_AUTHORIZE_URL
        self.token_url = settings.SUPABASE_OAUTH_TOKEN_URL
        self.api_url = "https://api.supabase.com/v1"
        
        # Validate required environment variables
        if not all([self.client_id, self.client_secret, self.redirect_uri, 
                   self.authorize_url, self.token_url]):
            raise ValueError("Missing required Supabase OAuth environment variables")
    
    def generate_pkce_challenge(self) -> tuple[str, str]:
        """Generate PKCE code verifier and challenge for security"""
        code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode('utf-8')).digest()
        ).decode('utf-8').rstrip('=')
        return code_verifier, code_challenge
    
    def get_authorization_url(self, user_id: str, project_id: str) -> tuple[str, str, str]:
        """Generate OAuth authorization URL with PKCE"""
        try:
            # Fix: Proper debug logging
            print(f"Generating OAuth URL for user_id: {user_id}, project_id: {project_id}")
            
            # Generate state with proper format
            state = f"{user_id}:{project_id}:{secrets.token_urlsafe(16)}"
            code_verifier, code_challenge = self.generate_pkce_challenge()
            
            params = {
                'client_id': self.client_id,
                'redirect_uri': self.redirect_uri,
                'response_type': 'code',
                'state': state,
                'code_challenge': code_challenge,
                'code_challenge_method': 'S256',
                'scope': 'projects:read projects:write organizations:read'
            }
            
            auth_url = f"{self.authorize_url}?{urlencode(params)}"
            
            print(f"Generated auth URL successfully for state: {state}")
            return auth_url, state, code_verifier
            
        except Exception as e:
            print(f"Error generating authorization URL: {str(e)}")
            raise ValueError(f"Failed to generate authorization URL: {str(e)}")
    
    async def exchange_code_for_tokens(self, code: str, code_verifier: str) -> Dict:
        """Exchange authorization code for access and refresh tokens"""
        try:
            # Prepare basic auth header
            auth_header = base64.b64encode(
                f"{self.client_id}:{self.client_secret}".encode()
            ).decode()
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.token_url,
                    headers={
                        'Authorization': f'Basic {auth_header}',
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    data={
                        'grant_type': 'authorization_code',
                        'code': code,
                        'redirect_uri': self.redirect_uri,
                        'code_verifier': code_verifier
                    }
                )
                
                print(f"[DEBUG] Token exchange response status: {response.status_code}")
                print(f"[DEBUG] Token exchange response: {response.text}")
                
                # Fix: Accept both 200 and 201 status codes as success
                if response.status_code in [200, 201]:
                    tokens = response.json()
                    print(f"[SUCCESS] Token exchange successful: {tokens}")
                    return tokens
                else:
                    error_msg = f"Token exchange failed with status {response.status_code}: {response.text}"
                    print(f"[ERROR] {error_msg}")
                    return {"error": error_msg}
                    
        except Exception as e:
            error_msg = f"Token exchange error: {str(e)}"
            print(f"[ERROR] {error_msg}")
            return {"error": error_msg}
    async def refresh_access_token(self, refresh_token: str) -> Dict:
        """Refresh expired access token"""
        try:
            auth_header = base64.b64encode(
                f"{self.client_id}:{self.client_secret}".encode()
            ).decode()
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.token_url,
                    headers={
                        'Authorization': f'Basic {auth_header}',
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    data={
                        'grant_type': 'refresh_token',
                        'refresh_token': refresh_token
                    }
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    return {"error": f"Token refresh failed: {response.text}"}
                    
        except Exception as e:
            return {"error": f"Token refresh error: {str(e)}"}
    
    async def get_user_organizations(self, access_token: str) -> List[Dict]:
        """Get user's Supabase organizations"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.api_url}/organizations",
                    headers={"Authorization": f"Bearer {access_token}"}
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    return {"error": f"Failed to get organizations: {response.text}"}
                    
        except Exception as e:
            return {"error": f"Organizations fetch error: {str(e)}"}
    
    async def get_organization_projects(self, access_token: str, org_id: str) -> List[Dict]:
        """Get projects in an organization"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.api_url}/organizations/{org_id}/projects",
                    headers={"Authorization": f"Bearer {access_token}"}
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    return {"error": f"Failed to get projects: {response.text}"}
                    
        except Exception as e:
            return {"error": f"Projects fetch error: {str(e)}"}
    
    async def get_project_details(self, access_token: str, project_ref: str) -> Dict:
        """Get specific project details including API keys"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.api_url}/projects/{project_ref}",
                    headers={"Authorization": f"Bearer {access_token}"}
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    return {"error": f"Failed to get project details: {response.text}"}
                    
        except Exception as e:
            return {"error": f"Project details fetch error: {str(e)}"}

supabase_service = SupabaseService()