# app/services/notification_service.py

from typing import List, Dict, Any, Optional
import boto3
from app.repository.mongodb.device_token_repository import DeviceTokenRepository
from app.repository.mongodb.notification_repository import NotificationRepository
from app.models.notification_model import NotificationModel
from app.connection.tenant_middleware import get_tenant_id
from app.connection.establish_db_connection import get_mongo_db_v1
from app.repository.mongodb.client import get_db
from app.core.Settings import settings

class NotificationService:
    def __init__(self):
        self.pinpoint = boto3.client('pinpoint')
        self.APP_ID = "26b49d923b944e0b88edb97911484485"

    async def send_notification(self, notification_data: NotificationModel) -> bool:
        """
        Send notification and store it in database
        """
        try:
            # 1. Store in MongoDB
            notification_repo: NotificationRepository = await get_mongo_db_v1("notification")
            device_token_repo: DeviceTokenRepository = await get_mongo_db_v1("device_token")
            tenant_id = get_tenant_id()
            db = await get_db(f"{settings.MONGO_DB_NAME}_{tenant_id}")

            stored = await notification_repo.create_notification(notification_data, db)
            if not stored:
                print("Failed to store notification in database")
                return False

            # 2. Get user's device tokens
            tokens = await device_token_repo.get_user_tokens(
                user_id=notification_data.receiver_id,
                tenant_id=tenant_id,
                db=db
            )

            if not tokens:
                # Return True because notification was stored in DB even if no devices to push to
                return True

            # 3. Send push notification
            message_requests = {
                token: {'ChannelType': 'GCM'}
                for token in tokens
            }

            # Prepare notification content
            notification_content = {
                'Title': self._get_notification_title(notification_data),
                'Body': notification_data.data.message,
                'Data': {
                    'type': notification_data.type,
                    'action': notification_data.action,
                    'link': notification_data.data.link,
                }
            }

            response = self.pinpoint.send_messages(
                ApplicationId=self.APP_ID,
                MessageRequest={
                    'Addresses': message_requests,
                    'MessageConfiguration': {
                        'GCMMessage': {
                            'Action': 'OPEN_APP',
                            'Title': notification_content['Title'],
                            'Body': notification_content['Body'],
                            'SilentPush': False,
                            'Data': notification_content['Data']
                        }
                    }
                }
            )

            # 4. Handle invalid tokens
            await self._handle_pinpoint_response(response, device_token_repo, db, tenant_id)

            return True

        except Exception as e:
            print(f"Error sending notification: {e}")
            return False

    def _get_notification_title(self, notification: NotificationModel) -> str:
        """Generate appropriate title based on notification type"""
        titles = {
            "discussion": "Discussion Update",
            "task": "Task Update",
            "project": "Project Update",
            "approval": "Approval Request",
            "code_generation": "Code Generation"
        }
        return titles.get(notification.type, "New Notification")

    async def _handle_pinpoint_response(
        self,
        response: Dict[str, Any],
        device_token_repo: DeviceTokenRepository,
        db,
        tenant_id: str
    ) -> None:
        """Handle Pinpoint response and invalid tokens"""
        results = response.get('MessageResponse', {}).get('Result', {})
        print("pinpoint response", results)
        for token, result in results.items():
            if result.get('StatusCode') != 200:
                await device_token_repo.deactivate_token(
                    token=token,
                    tenant_id=tenant_id,
                    db=db
                )