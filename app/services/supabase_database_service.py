# Add these methods to your existing database service
from app.utils.hash import encrypt_data, decrypt_data
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, List
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
from app.connection.establish_db_connection import get_mongo_db,get_node_db

class SupabaseDatabaseService:
    def __init__(self):
        # Follow the same pattern as SessionTrackerService
        self.mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        
        # Add Supabase collections
        self.supabase_credentials_collection = "supabase_user_credentials"
        self.oauth_states_collection = "oauth_states"
        print(f"[DEBUG] SupabaseDatabaseService initialized with db: {KAVIA_ROOT_DB_NAME}")
    
    async def store_oauth_state(self, state: str, code_verifier: str, user_id: str, project_id: str):
        """Temporarily store OAuth state and code verifier"""
        try:
            document = {
                "state": state,
                "code_verifier": encrypt_data(code_verifier),
                "user_id": user_id,
                "project_id": project_id,
                "created_at": datetime.utcnow(),
                "expires_at": datetime.utcnow() + timedelta(minutes=10)  # 10 minute expiry
            }
            
            print(f"[DEBUG] Storing OAuth state for user: {user_id}, project: {project_id}")
            
            # Use the same pattern as your reference code
            result = self.mongo_db.db[self.oauth_states_collection].insert_one(document)
            
            print(f"[SUCCESS] OAuth state stored with ID: {result.inserted_id}")
            return {"success": True, "inserted_id": str(result.inserted_id)}
            
        except Exception as e:
            print(f"[ERROR] Error storing OAuth state: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}
    
    async def get_oauth_state(self, state: str) -> Optional[Dict]:
        """Retrieve and delete OAuth state"""
        try:
            print(f"[DEBUG] Retrieving OAuth state: {state}")
            
            # Use find_one_and_delete like MongoDB operations
            doc = self.mongo_db.db[self.oauth_states_collection].find_one_and_delete({
                "state": state,
                "expires_at": {"$gt": datetime.utcnow()}
            })
            
            if doc:
                print(f"[SUCCESS] OAuth state found and deleted")
                doc["code_verifier"] = decrypt_data(doc["code_verifier"])
                return doc
            else:
                print(f"[DEBUG] OAuth state not found or expired")
                return None
                
        except Exception as e:
            print(f"[ERROR] Error getting OAuth state: {e}")
            return None
    
    async def store_user_supabase_tokens(self, user_id: str, project_id: str, tokens: Dict):
        """Store encrypted Supabase tokens for user and project"""
        try:
            print(f"[DEBUG] Storing Supabase tokens for user: {user_id}, project: {project_id}")
            
            encrypted_tokens = {
                "access_token": encrypt_data(tokens["access_token"]),
                "refresh_token": encrypt_data(tokens.get("refresh_token", "")) if tokens.get("refresh_token") else None,
                "expires_at": datetime.utcnow() + timedelta(seconds=tokens.get("expires_in", 3600)),
                "token_type": tokens.get("token_type", "Bearer"),
                "connected_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            # Store in separate collection with user_id and project_id using upsert
            result = self.mongo_db.db[self.supabase_credentials_collection].update_one(
                {
                    "user_id": user_id,
                    "project_id": project_id
                },
                {
                    "$set": {
                        "user_id": user_id,
                        "project_id": project_id,
                        "is_connected": True,
                        **encrypted_tokens
                    }
                },
                upsert=True
            )
            
            print(f"[SUCCESS] Supabase tokens stored - matched: {result.matched_count}, modified: {result.modified_count}, upserted: {result.upserted_id}")
            return {"success": True, "upserted": result.upserted_id is not None}
            
        except Exception as e:
            print(f"[ERROR] Error storing Supabase tokens: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}
    
    async def get_user_supabase_tokens(self, user_id: str, project_id: str) -> Optional[Dict]:
        """Get decrypted Supabase tokens for specific user and project"""
        try:
            print(f"[DEBUG] Getting Supabase tokens for user: {user_id}, project: {project_id}")
            
            credential = self.mongo_db.db[self.supabase_credentials_collection].find_one({
                "user_id": user_id,
                "project_id": str(project_id),
                "is_connected": True
            })
            
            if not credential:
                print(f"[DEBUG] No Supabase credentials found for user: {user_id}, project: {project_id}")
                return None
            
            print(f"[SUCCESS] Supabase credentials found and decrypted")
            return {
                "access_token": decrypt_data(credential["access_token"]),
                "refresh_token": decrypt_data(credential["refresh_token"]) if credential.get("refresh_token") else None,
                "expires_at": credential["expires_at"],
                "token_type": credential.get("token_type", "Bearer"),
                "connected_at": credential.get("connected_at"),
                "updated_at": credential.get("updated_at")
            }
            
        except Exception as e:
            print(f"[ERROR] Error getting Supabase tokens: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def get_user_supabase_tokens_global(self, user_id: str) -> Optional[Dict]:
        """Get the most recent Supabase tokens for user (across all projects)"""
        try:
            print(f"[DEBUG] Getting global Supabase tokens for user: {user_id}")
            
            # Get the most recently updated credential for this user
            credential = self.mongo_db.db[self.supabase_credentials_collection].find_one(
                {
                    "user_id": user_id,
                    "is_connected": True
                },
                sort=[("updated_at", -1)]  # Get most recent
            )
            
            if not credential:
                print(f"[DEBUG] No global Supabase credentials found for user: {user_id}")
                return None
            
            print(f"[SUCCESS] Global Supabase credentials found for project: {credential.get('project_id')}")
            return {
                "access_token": decrypt_data(credential["access_token"]),
                "refresh_token": decrypt_data(credential["refresh_token"]) if credential.get("refresh_token") else None,
                "expires_at": credential["expires_at"],
                "token_type": credential.get("token_type", "Bearer"),
                "connected_at": credential.get("connected_at"),
                "updated_at": credential.get("updated_at"),
                "project_id": credential.get("project_id")
            }
            
        except Exception as e:
            print(f"[ERROR] Error getting global Supabase tokens: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def update_user_supabase_tokens(self, user_id: str, project_id: str, tokens: Dict):
        """Update existing Supabase tokens after refresh"""
        try:
            print(f"[DEBUG] Updating Supabase tokens for user: {user_id}, project: {project_id}")
            
            encrypted_tokens = {
                "access_token": encrypt_data(tokens["access_token"]),
                "expires_at": datetime.utcnow() + timedelta(seconds=tokens.get("expires_in", 3600)),
                "updated_at": datetime.utcnow()
            }
            
            # Update refresh token if provided
            if tokens.get("refresh_token"):
                encrypted_tokens["refresh_token"] = encrypt_data(tokens["refresh_token"])
            
            result = self.mongo_db.db[self.supabase_credentials_collection].update_one(
                {
                    "user_id": user_id,
                    "project_id": project_id
                },
                {"$set": encrypted_tokens}
            )
            
            print(f"[SUCCESS] Supabase tokens updated - matched: {result.matched_count}, modified: {result.modified_count}")
            return {"success": True, "modified": result.modified_count > 0}
            
        except Exception as e:
            print(f"[ERROR] Error updating Supabase tokens: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}
    
    async def store_project_supabase_config(self, project_id: str, supabase_config: Dict):
        """Store Supabase configuration for project"""
        try:
            print(f"[DEBUG] Storing Supabase config for project: {project_id}")
            
            node_db = get_node_db()
            result = await node_db.update_node_by_id(
                project_id,
                {
                    "supabase_config": json.dumps(supabase_config),
                    "database_type": "supabase",
                    "supabase_configured": True,
                    "supabase_project_ref": supabase_config.get("project_ref"),
                    "supabase_project_name": supabase_config.get("project_name"),
                    "configured_at": datetime.utcnow().isoformat()
                }
            )
            
            print(f"[SUCCESS] Supabase config stored for project: {project_id}")
            return {"success": True, "result": result}
            
        except Exception as e:
            print(f"[ERROR] Error storing Supabase config: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}
    
    async def get_user_projects_with_supabase(self, user_id: str) -> List[Dict]:
        """Get all projects where user has Supabase connected"""
        try:
            print(f"[DEBUG] Getting projects with Supabase for user: {user_id}")
            
            credentials = list(self.mongo_db.db[self.supabase_credentials_collection].find({
                "user_id": user_id,
                "is_connected": True
            }))
            
            result = [
                {
                    "project_id": cred["project_id"],
                    "connected_at": cred.get("connected_at"),
                    "updated_at": cred.get("updated_at")
                }
                for cred in credentials
            ]
            
            print(f"[SUCCESS] Found {len(result)} projects with Supabase for user: {user_id}")
            return result
            
        except Exception as e:
            print(f"[ERROR] Error getting user projects with Supabase: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    async def disconnect_user_supabase(self, user_id: str, project_id: str):
        """Disconnect Supabase for specific user and project"""
        try:
            print(f"[DEBUG] Disconnecting Supabase for user: {user_id}, project: {project_id}")
            
            result = self.mongo_db.db[self.supabase_credentials_collection].update_one(
                {
                    "user_id": user_id,
                    "project_id": project_id
                },
                {
                    "$set": {
                        "is_connected": False,
                        "disconnected_at": datetime.utcnow()
                    }
                }
            )
            
            print(f"[SUCCESS] Supabase disconnected - matched: {result.matched_count}, modified: {result.modified_count}")
            return {"success": True, "modified": result.modified_count > 0}
            
        except Exception as e:
            print(f"[ERROR] Error disconnecting Supabase: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}
    
    async def cleanup_expired_oauth_states(self):
        """Clean up expired OAuth states (run this periodically)"""
        try:
            print(f"[DEBUG] Cleaning up expired OAuth states")
            
            result = self.mongo_db.db[self.oauth_states_collection].delete_many({
                "expires_at": {"$lt": datetime.utcnow()}
            })
            
            print(f"[SUCCESS] Cleaned up {result.deleted_count} expired OAuth states")
            return {"success": True, "deleted_count": result.deleted_count}
            
        except Exception as e:
            print(f"[ERROR] Error cleaning up OAuth states: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}
    
    async def get_project_supabase_config(self, project_id: str) -> Optional[Dict]:
        """Get Supabase configuration for a project"""
        try:
            print(f"[DEBUG] Getting Supabase config for project: {project_id}")
            
            node_db = get_node_db()
            project = await node_db.get_node_by_id(project_id)
            
            if project and project.get("supabase_config"):
                try:
                    config = json.loads(project["supabase_config"])
                    print(f"[SUCCESS] Supabase config found for project: {project_id}")
                    return config
                except json.JSONDecodeError:
                    print(f"[ERROR] Invalid JSON in supabase_config for project: {project_id}")
                    return None
            else:
                print(f"[DEBUG] No Supabase config found for project: {project_id}")
                return None
                
        except Exception as e:
            print(f"[ERROR] Error getting Supabase config: {e}")
            import traceback
            traceback.print_exc()
            return None

# Global instance - following the same pattern as SessionTrackerService
supabase_db_service = None

def get_supabase_db_service():
    global supabase_db_service
    if supabase_db_service is None:
        supabase_db_service = SupabaseDatabaseService()
    return supabase_db_service