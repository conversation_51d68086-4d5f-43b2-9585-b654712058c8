from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging
from datadog_api_client import ApiClient, Configuration
from datadog_api_client.v2.api.logs_api import LogsApi
import os

logger = logging.getLogger(__name__)

@dataclass
class DiscussionTiming:
    discussion_id: str
    node_type: str
    discussion_type: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[timedelta] = None

    def to_dict(self) -> dict:
        """Convert to dictionary format"""
        return {
            "discussion_id": self.discussion_id,
            "node_type": self.node_type,
            "discussion_type": self.discussion_type,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration_seconds": self.duration.total_seconds() if self.duration else None
        }

@dataclass
class TimingSummary:
    total_discussions: int
    total_duration: timedelta
    by_node_type: Dict[str, Dict]
    by_discussion_type: Dict[str, Dict]
    discussions: List[DiscussionTiming]
    task_id: Optional[str] = None

    def to_datadog_format(self) -> dict:
        """Convert summary to Datadog log format"""
        duration_seconds = int(self.total_duration.total_seconds())
        
        return {
            "ddsource": "kavia_ai",
            "service": "auto_config",
            "ddtags": f"task_id:{self.task_id}" if self.task_id else "",
            "hostname": os.getenv('HOSTNAME', 'backend-api'),
            "status": "info",
            "task_id": self.task_id,
            "total_discussions": self.total_discussions,
            "total_duration_seconds": duration_seconds,
            # Additional properties from the class
            "by_node_type": self.by_node_type,
            "by_discussion_type": self.by_discussion_type,
            "discussions": [d.to_dict() for d in self.discussions],
            # Metrics for Datadog graphs
            "metrics": {
                "discussions.count": self.total_discussions,
                "duration.seconds": duration_seconds,
                # Add metrics per node type
                **{f"discussions.by_node_type.{node_type}": data.get('count', 0) 
                   for node_type, data in self.by_node_type.items()},
                # Add metrics per discussion type
                **{f"discussions.by_type.{disc_type}": data.get('count', 0)
                   for disc_type, data in self.by_discussion_type.items()}
            },
            "message": f"Task {self.task_id} completed with {self.total_discussions} discussions in {duration_seconds} seconds"
        }

    def send_to_datadog(self) -> bool:
        """Send the timing summary to Datadog"""
        try:
            api_key = os.getenv('DATADOG_API_KEY')
            app_key = os.getenv('DATADOG_APP_KEY')
            
            if not api_key or not app_key:
                logger.error("Missing Datadog API keys")
                return False
            
            configuration = Configuration()
            configuration.api_key['apiKeyAuth'] = api_key
            configuration.api_key['appKeyAuth'] = app_key
            configuration.server_variables["site"] = os.getenv('DATADOG_SERVER', 'us5.datadoghq.com')
            
            with ApiClient(configuration) as api_client:
                logs_api = LogsApi(api_client)
                log_entry = self.to_datadog_format()
                
                logger.info(f"Sending discussion timing data to Datadog: {log_entry}")
                response = logs_api.submit_log(body=[log_entry])
                logger.info("Successfully sent discussion timing data to Datadog")
                return True
                
        except Exception as e:
            logger.error(f"Failed to send discussion timing data to Datadog: {str(e)}")
            if hasattr(e, 'response'):
                logger.error(f"Response content: {e.response.text if hasattr(e.response, 'text') else 'No response text'}")
            return False