from datetime import datetime
import json

class TimingReportFormatter:
    @staticmethod
    def format_duration(duration) -> str:
        """Format duration into readable string"""
        if isinstance(duration, str):
            return duration
        
        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    @staticmethod
    def format_summary(summary) -> str:
        """Format timing summary into key-value pairs for easier parsing"""
        output = []
        timestamp = datetime.now().strftime("%Y-%m-%d")
        
        # Start with FinalDiscussionTimingReport marker
        output.append("FinalDiscussionTimingReport")
        
        # Add main metrics
        if isinstance(summary, dict):
            output.append(f"total_discussions={summary.get('total_discussions', 0)}")
            output.append(f"total_duration={TimingReportFormatter.format_duration(summary.get('total_duration', 0))}")
            output.append(f"date={timestamp}")
            
            # Add node type metrics
            for node_type, data in summary.get('by_node_type', {}).items():
                if data['count'] > 0:
                    prefix = f"node_type.{node_type.lower()}"
                    output.append(f"{prefix}.count={data['count']}")
                    output.append(f"{prefix}.total_duration={TimingReportFormatter.format_duration(data['total_duration'])}")
                    avg_duration = data['total_duration'] / data['count']
                    output.append(f"{prefix}.avg_duration={TimingReportFormatter.format_duration(avg_duration)}")
            
            # Add discussion type metrics
            for disc_type, data in summary.get('by_discussion_type', {}).items():
                if data['count'] > 0:
                    prefix = f"discussion_type.{disc_type.lower()}"
                    output.append(f"{prefix}.count={data['count']}")
                    output.append(f"{prefix}.total_duration={TimingReportFormatter.format_duration(data['total_duration'])}")
                    avg_duration = data['total_duration'] / data['count']
                    output.append(f"{prefix}.avg_duration={TimingReportFormatter.format_duration(avg_duration)}")
        else:
            # Handle TimingSummary object
            output.append(f"total_discussions={summary.total_discussions}")
            output.append(f"total_duration={TimingReportFormatter.format_duration(summary.total_duration)}")
            output.append(f"date={timestamp}")
            
            # Add node type metrics
            for node_type, data in summary.by_node_type.items():
                if data['count'] > 0:
                    prefix = f"node_type.{node_type.lower()}"
                    output.append(f"{prefix}.count={data['count']}")
                    output.append(f"{prefix}.total_duration={TimingReportFormatter.format_duration(data['total_duration'])}")
                    avg_duration = data['total_duration'] / data['count']
                    output.append(f"{prefix}.avg_duration={TimingReportFormatter.format_duration(avg_duration)}")
            
            # Add discussion type metrics
            for disc_type, data in summary.by_discussion_type.items():
                if data['count'] > 0:
                    prefix = f"discussion_type.{disc_type.lower()}"
                    output.append(f"{prefix}.count={data['count']}")
                    output.append(f"{prefix}.total_duration={TimingReportFormatter.format_duration(data['total_duration'])}")
                    avg_duration = data['total_duration'] / data['count']
                    output.append(f"{prefix}.avg_duration={TimingReportFormatter.format_duration(avg_duration)}")
        
        return " ".join(output)
    
    @staticmethod
    def format_summary_as_json(summary, task_id=None) -> str:
        """Format timing summary as a JSON string with only total discussions, total duration, date, and task_id"""
        timestamp = datetime.now().strftime("%Y-%m-%d")
        
        json_data = {
            "FinalDiscussionTimingReport": True,
            "total_discussions": 0,
            "total_duration": "00:00:00",
            "date": timestamp
        }
        
        # Add task_id if provided - try to convert to int if possible
        if task_id:
            try:
                # If task_id could be an integer, convert it
                json_data["task_id"] = int(task_id)
            except (ValueError, TypeError):
                # Otherwise keep as string or whatever type it is
                json_data["task_id"] = task_id
        
        # Populate values based on summary object type
        if isinstance(summary, dict):
            json_data["total_discussions"] = summary.get('total_discussions', 0)
            json_data["total_duration"] = TimingReportFormatter.format_duration(summary.get('total_duration', 0))
        else:
            # Handle TimingSummary object
            json_data["total_discussions"] = summary.total_discussions
            json_data["total_duration"] = TimingReportFormatter.format_duration(summary.total_duration)
        
        return json.dumps(json_data)