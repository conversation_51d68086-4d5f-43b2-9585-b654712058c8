name: Kavia AI
version: 1.0
description: <PERSON><PERSON> AI is an intelligent productivity assistant that helps with content generation, document analysis, and contextual automation. Designed for writers, marketers, researchers, and developers.

capabilities:
  - Text generation
  - Text summarization
  - Rewriting and paraphrasing
  - Tone adjustment (e.g., professional, casual)
  - File upload and document analysis
  - Multi-file contextual Q&A
  - Chat with memory
  - Structured data extraction
  - Exporting output to PDF, Markdown, or share link
  - Integration with third-party tools

input_formats:
  - Plain text
  - PDF
  - DOCX
  - TXT
  - Markdown
  - HTML

output_formats:
  - Plain text
  - Markdown
  - PDF
  - Shareable link

use_cases:
  - Generate blogs, emails, or ads
  - Rewrite or summarize content
  - Extract key insights from research papers
  - Generate executive summaries
  - Explain or document code
  - Ask questions across multiple documents

authentication:
  - Google OAuth
  - GitHub OAuth
  - Email + password

pricing:
  free:
    prompts_per_day: 20
    features: basic chat, file upload, summarization
  pro:
    price: $15/month
    features: unlimited prompts, advanced document support, integrations
  enterprise:
    price: contact
    features: custom limits, white-labeling, SSO, audit logs

security:
  - End-to-end encryption
  - Temporary file processing (unless saved)
  - No user data used for training
  - Enterprise-grade controls available

integrations:
  - Google Docs
  - Notion (beta)
  - Slack
  - Zapier

support:
  knowledge_base: https://help.kavia.ai
  email: <EMAIL>
  live_chat: available 9:00 AM – 7:00 PM IST
  community: https://slack.kavia.ai

faq:
  - Can Kavia AI read multiple documents? Yes
  - Is data stored permanently? Only if saved
  - Does Kavia train on user data? No
  - What formats are supported? PDF, DOCX, TXT, Markdown, HTML

changelog:
  - Added support for multi-file Q&A
  - Introduced Notion and Zapier integration (beta)
  - Enabled smart memory across chats

license: Proprietary

homepage: https://kavia.ai
