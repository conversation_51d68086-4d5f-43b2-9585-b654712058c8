#!/usr/bin/env python3
import os
import subprocess
import tempfile
import shutil
import xml.etree.ElementTree as ET
import logging
from typing import List, Dict, Optional, Tuple
import git
from github import Github
import requests
import re

class RepoManager:
    """
    A class to manage repository operations similar to Google's repo tool.
    This class can parse manifest files and clone repositories accordingly.
    """
    
    def __init__(self, manifest_url: str, destination: str = "/app/data", 
                 branch: str = "master", log_level: int = logging.INFO):
        """
        Initialize the RepoManager with manifest URL and destination.
        
        Args:
            manifest_url: URL of the manifest repository
            destination: Path where repositories will be cloned
            branch: Branch of the manifest repository to use
            log_level: Logging level
        """
        self.manifest_url = manifest_url
        self.destination = os.path.abspath(destination)
        self.branch = branch
        self.temp_dir = None
        
        # Setup logging
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger("RepoManager")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit, cleanup temporary directory"""
        self._cleanup()
    
    def _cleanup(self):
        """Clean up temporary directory if exists"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            self.logger.debug(f"Cleaning up temporary directory: {self.temp_dir}")
            shutil.rmtree(self.temp_dir)
            self.temp_dir = None

    def is_commit_hash(self, revision):
        return bool(re.fullmatch(r"[0-9a-f]{40}", revision))

    
    def get_repo_id(self, repo_name, github_token = ''):
        try:
            # Extract owner and repo name
            url_parts = repo_name.strip().split('/')
            owner = url_parts[-2]
            repo_name = url_parts[-1].replace('.git', '')
            
            # Initialize Github with or without token
            g = Github(github_token) if github_token else Github()
            
            # Get repository and latest commit
            print(f"{owner}/{repo_name}")
            repo = g.get_repo(f"{owner}/{repo_name}")
            
            # Extract repo ID
            if(repo.id):
                return repo.id
            else:
                raise Exception("Couldn't find repository ID.")

        except Exception as e:
            print(f"Error: {e}")
            return None
        
    def get_head_branch_from_commit_hash(self, repo_name, commit_hash, github_token=''):
        """
            Returns a list of branches where the given commit is the HEAD.
            repo_full_name: str — like "WebPlatformForEmbedded/meta-wpe"
            commit_hash: str — 40-char SHA-1 hash
            github_token: str — optional GitHub token
        """
        try:
             # Extract owner and repo name
            url_parts = repo_name.strip().split('/')
            owner = url_parts[-2]
            repo_name = url_parts[-1].replace('.git', '')
            
            # Initialize api url
            api_url = f"https://api.github.com/repos/{owner}/{repo_name}/commits/{commit_hash}/branches-where-head"

            # Optional authentication
            headers = {}
            if github_token:
                headers['Authorization'] = f'token {github_token}'
            
             # API call
            response = requests.get(api_url, headers=headers)

            if response.status_code == 200:
                data = response.json()
                # If the list is empty, return "main" instead
                if not data:
                    return "main"
                # Return the first branch name
                return [branch['name'] for branch in data][0]
            elif response.status_code == 404 or response.status_code == 422:
                return "kavia-main"  # Commit not HEAD of any branch or not found so passing kavia-main
            else:
                raise Exception(f"GitHub API error: {response.status_code} - {response.text}")
            
        except Exception as e:
            print(f"Error: {e}")
            return None
    
    def _clone_manifest_repo(self) -> str:
        """
        Clone the manifest repository to a temporary directory.
        
        Returns:
            The path to the cloned manifest repository
        """
        self.temp_dir = tempfile.mkdtemp(prefix="repo_manager_")
        self.logger.info(f"Cloning manifest repository to {self.temp_dir}")
        
        try:
            git.Repo.clone_from(self.manifest_url, self.temp_dir, branch=self.branch)
            self.logger.debug("Manifest repository cloned successfully")
            return self.temp_dir
        except git.GitCommandError as e:
            self.logger.error(f"Failed to clone manifest repository: {e}")
            self._cleanup()
            raise
    
    def _parse_manifest(self, manifest_path: str, github_token='') -> List[Dict[str, str]]:
        """
        Parse the manifest XML file and extract repository information.
        
        Args:
            manifest_path: Path to the manifest XML file
            
        Returns:
            List of dictionaries with repository information
        """
        if(not manifest_path.endswith(".xml")):
            manifest_file = os.path.join(manifest_path, "default.xml")
        else:
            manifest_file = manifest_path

        if not os.path.exists(manifest_file):
            raise FileNotFoundError(f"Manifest file not found: {manifest_file}")
        
        self.logger.info(f"Parsing manifest file: {manifest_file}")
        
        try:
            tree = ET.parse(manifest_file)
            root = tree.getroot()
            
            # Extract remote information
            remotes = {}
            for remote in root.findall("remote"):
                name = remote.get("name")
                fetch = remote.get("fetch")
                remotes[name] = fetch
            
            # Get default attributes
            default = root.find("default")
            default_remote = default.get("remote") if default is not None else None
            default_revision = default.get("revision") if default is not None else "kavia-main"
            
            # Extract project information
            repositories = []
            for project in root.findall("project"):
                name = project.get("name")
                path = project.get("path", name)
                remote_name = project.get("remote", default_remote)
                revision = project.get("revision", default_revision)
                
                '''if remote_name not in remotes:
                    self.logger.warning(f"Remote '{remote_name}' not defined for project '{name}'")
                    continue'''
                
                if remote_name == None:
                    self.logger.warning(f"Remote not defined for project '{name}'")
                    continue
                
                remote_url = remotes.get(remote_name, None)

                self.logger.warning(f"Remote url: {remote_url}")

                if remote_url or "/" in name:
                    if remote_name.lower() == "github":
                        if "/" in name:  #a repo name having a "/" means that both owner and repo are in the name itself
                            remote_url = f"**************:{name.split('/')[0]}/"
                            name = name.split('/')[1]
                    else: 
                        self.logger.warning(f"Couldn't generate a github fetch url for project '{name}'")
                else:
                    self.logger.warning(f"No valid url could be found for project '{name}'")
                    continue

                # Handle URL format (with or without .git suffix)
                if not remote_url.endswith('/'):
                    remote_url += '/'
                
                # Combine remote fetch URL with project name
                if remote_url.startswith('git@'):
                    # SSH format
                    repo_url = f"{remote_url}{name}"
                    repo_name = remote_url.split(':')[1] + path if len(remote_url.split(':')) > 1 else remote_url
                    repo_id = self.get_repo_id(repo_name, github_token)
                else:
                    # HTTPS format
                    repo_url = f"{remote_url}{name}"
                    repo_name = remote_url.split('.com/')[1] + path if len(remote_url.split('.com/')) > 1 else remote_url
                    repo_id = self.get_repo_id(repo_name, github_token)

                if self.is_commit_hash(revision):
                    branch_name = self.get_head_branch_from_commit_hash(repo_name, revision, github_token)
                else:
                    branch_name = revision

                if not branch_name:
                    self.logger.warning(f"No branch found for the commit hash '{revision}'")
                    continue
                
                repositories.append({
                    "name": repo_name,
                    "path": path,
                    "url": repo_url,
                    "revision": branch_name,
                    "id": str(repo_id)
                })
            
            return repositories
        except ET.ParseError as e:
            self.logger.error(f"Failed to parse manifest file: {e}")
            raise
        except e:
            self.logger.error(f"Failed to parse manifest file: {e}")
    
    def _clone_repository(self, repo_info: Dict[str, str]) -> bool:
        """
        Clone a single repository.
        
        Args:
            repo_info: Dictionary with repository information
            
        Returns:
            True if successful, False otherwise
        """
        repo_url = repo_info["url"]
        repo_path = os.path.join(self.destination, repo_info["path"])
        revision = repo_info["revision"]
        
        self.logger.info(f"Cloning repository: {repo_url} to {repo_path}")
        
        if os.path.exists(repo_path):
            self.logger.warning(f"Repository path already exists: {repo_path}")
            return False
        
        # Create parent directory if it doesn't exist
        os.makedirs(os.path.dirname(repo_path), exist_ok=True)
        
        try:
            repo = git.Repo.clone_from(repo_url, repo_path)
            if revision != "main" and revision != "master":
                repo.git.checkout(revision)
            self.logger.debug(f"Repository {repo_url} cloned successfully")
            return True
        except git.GitCommandError as e:
            self.logger.error(f"Failed to clone repository {repo_url}: {e}")
            return False
    
    def sync(self) -> Tuple[int, int]:
        """
        Sync repositories based on the manifest.
        
        Returns:
            Tuple of (successful_clones, failed_clones)
        """
        # Create destination directory if it doesn't exist
        os.makedirs(self.destination, exist_ok=True)
        
        try:
            manifest_path = self._clone_manifest_repo()
            repositories = self._parse_manifest(manifest_path)
            
            self.logger.info(f"Found {len(repositories)} repositories in manifest")
            
            successful = 0
            failed = 0
            
            for repo_info in repositories:
                if self._clone_repository(repo_info):
                    successful += 1
                else:
                    failed += 1
            
            self.logger.info(f"Sync completed: {successful} successful, {failed} failed")
            return successful, failed
        
        finally:
            self._cleanup()

    @staticmethod
    def run_command(cmd: List[str], cwd: Optional[str] = None) -> Tuple[int, str, str]:
        """
        Run a shell command and return the result.
        
        Args:
            cmd: Command to run as a list of strings
            cwd: Current working directory for the command
            
        Returns:
            Tuple of (return_code, stdout, stderr)
        """
        process = subprocess.Popen(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            cwd=cwd,
            text=True
        )
        stdout, stderr = process.communicate()
        return process.returncode, stdout, stderr


# Example usage
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Repository Manager")
    parser.add_argument("manifest_url", help="URL of the manifest repository")
    parser.add_argument("--destination", "-d", default="data", help="Destination directory")
    parser.add_argument("--branch", "-b", default="master", help="Branch of the manifest repository")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    log_level = logging.DEBUG if args.verbose else logging.INFO
    
    with RepoManager(args.manifest_url, args.destination, args.branch, log_level) as manager:
        successful, failed = manager.sync()
        
        if failed > 0:
            print(f"Warning: {failed} repositories failed to clone.")
            exit(1)
        else:
            print(f"Successfully cloned {successful} repositories.")
            exit(0)