from kubernetes import client, config
from kubernetes.client.rest import ApiException
import time
import re


def create_project_for_dev(project_id):
    
    env_name = 'dev'
    action = 'create'

    # Try loading in-cluster config first
    try:
        try:
            config.load_incluster_config()
        except config.ConfigException:
            # Fall back to local kubeconfig
            config.load_kube_config()
    except Exception as e:
        raise
    
    # Create Kubernetes API client
    batch_v1 = client.BatchV1Api()
    core_v1 = client.CoreV1Api()

    # Define the job name and namespace
    job_name = f"codegen-{project_id}-{env_name}-{action}"
    namespace = "duploservices-kavia-dev"
    previous_create_job = f"codegen-{project_id}-{env_name}-create"
    hostname = f"vscode-internal-{project_id}.{env_name}.cloud.kavia.ai"
    
    # Function to extract pod ID from logs
    def extract_pod_id_from_logs(logs):
        # Pattern to match pod ID in format like "5824-dev-7d54b64dff-q6sjv"
        pod_pattern = r"Using pod: (\d+)-dev-[a-z0-9]+-[a-z0-9]+"
        match = re.search(pod_pattern, logs)
        
        if match:
            return match.group(1)  # Return just the numeric ID part
        return None

    # Function to check if a job exists
    def check_job_exists(job_name, namespace):
        try:
            job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
            return True, job
        except ApiException as e:
            if e.status == 404:
                return False, None
            raise e

    # Function to get logs from a job
    def get_job_logs(job_name, namespace):
        try:
            # Get pods associated with the job
            pods = core_v1.list_namespaced_pod(
                namespace=namespace,
                label_selector=f"job-name={job_name}"
            )
            
            code_gen_pod_id = None
            job_logs = ""
            
            # Get logs from each pod and extract pod ID
            for pod in pods.items:
                pod_name = pod.metadata.name
                try:
                    logs = core_v1.read_namespaced_pod_log(
                        name=pod_name,
                        namespace=namespace
                    )
                    job_logs += logs
                    print(f"Logs from pod {pod_name}:")
                    print(logs)
                    
                    # Extract the pod ID from logs
                    temp_id = extract_pod_id_from_logs(logs)
                    if temp_id:
                        code_gen_pod_id = temp_id
                        print(f"Extracted code_gen_pod_id = {code_gen_pod_id}")
                    
                except Exception as e:
                    print(f"Error retrieving logs for pod {pod_name}: {e}")
            
            return code_gen_pod_id, job_logs
        except ApiException as e:
            print(f"Error getting job logs: {e}")
            return None, ""

    # Function to wait for job completion
    def wait_for_job_completion(job_name, namespace):
        while True:
            job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
            if job.status.succeeded:
                code_gen_pod_id, _ = get_job_logs(job_name, namespace)
                return code_gen_pod_id
            time.sleep(5)

    # Define the command based on the action
    if action == "create":
        servicename = f"internal-{project_id}-{env_name}"
        kubectl_command = f"""
        echo "Creating deployment for {job_name}..."
        #sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; s/{{{{ENV_NAME}}}}/{env_name}/g" /app/codegenservice-deployment.yaml > /tmp/codegenservice-{project_id}-{env_name}.yaml
        #sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
        # s/{{{{ENV_NAME}}}}/{env_name}/g; \
        # s/{{{{hostname}}}}/{hostname}/g; \
        # s/{{{{servicename}}}}/{servicename}/g" \
        # /app/ingress/ingress-service.yaml > /tmp/ingress-patch-{project_id}-{env_name}.yaml
        #sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
        #     s/{{{{ENV_NAME}}}}/{env_name}/g" \
        #     /app/pvc/codegenservice-pvc.yaml > /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
        #sleep 99999
        #kubectl apply -f /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
        #kubectl apply -f /tmp/codegenservice-{project_id}-{env_name}.yaml
        #kubectl apply -f /tmp/ingress-patch-{project_id}-{env_name}.yaml
        #!/bin/sh
        echo " Starting ingress update script..."
        CONFIG_FILE="/tmp/custom_${{PROJECT_ID}}.conf"
        POD_NAME="nginx-c96774b8d-d98nb"
        echo $NAMESPACE
        echo " Environment: $ENV_NAME"
        echo " Finding oldest pod with label 'pod-status=available'..."
        POD_APP=`kubectl get pods -n "$NAMESPACE" -l pod-status=available \\
        --sort-by=.metadata.creationTimestamp \\
        -o jsonpath="{{.items[0].metadata.name}} {{.items[0].metadata.labels.app}}"`
        POD=`echo "$POD_APP" | awk '{{print $1}}'`
        APP=`echo "$POD_APP" | awk '{{print $2}}'`
        echo " Using pod: $POD"
        echo " App label: $APP"
        SERVER_NAME="vscode-internal-${{PROJECT_ID}}-${{ENV_NAME}}.dev-vscode.cloud.kavia.ai"
        PROXY_port1="http://internal-${{APP}}:3000"
        PROXY="http://internal-${{APP}}:8080"
        echo " SERVER_NAME to be added: $SERVER_NAME"
        echo " PROXY to be routed: $PROXY"
        sed "s|{{{{SERVER_NAME}}}}|${{SERVER_NAME}}|g; s|{{{{PROXY}}}}|${{PROXY}}|g; s|{{{{PROXY_port1}}}}|${{PROXY_port1}}|g" /app/nginx/nginx > "${{CONFIG_FILE}}"
        echo " Created local config: $CONFIG_FILE"

        DEST_FILE="/etc/nginx/conf.d/$(basename "$CONFIG_FILE")"
        kubectl cp "$CONFIG_FILE" "$NAMESPACE/$POD_NAME:$DEST_FILE"
        echo " Copied config into pod: $POD_NAME"
        kubectl exec -n "$NAMESPACE" "$POD_NAME" -- nginx -s reload
        echo " Reloaded nginx in pod: $POD_NAME"
        echo " Labeling pod '$POD' as used..."
        kubectl label pod "$POD" -n "$NAMESPACE" pod-status=used --overwrite
        """
    elif action == "delete":
        kubectl_command = f"""
        echo "Deleting deployment for {job_name}..."
        sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; s/{{{{ENV_NAME}}}}/{env_name}/g" /app/codegenservice-deployment.yaml > /tmp/codegenservice-{project_id}-{env_name}.yaml
        sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
        s/{{{{ENV_NAME}}}}/{env_name}/g; \
        s/{{{{hostname}}}}/{hostname}/g; \
        s/{{{{servicename}}}}/internal-{project_id}-{env_name}/g" \
        /app/ingress/ingress-service.yaml > /tmp/ingress-patch-{project_id}-{env_name}.yaml
        sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
            s/{{{{ENV_NAME}}}}/{env_name}/g" \
            /app/pvc/codegenservice-pvc.yaml > /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
        #sleep 99999
        kubectl delete -f /tmp/codegenservice-{project_id}-{env_name}.yaml --ignore-not-found=true 
        kubectl delete -f /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml --ignore-not-found=true
        kubectl delete -f /tmp/ingress-patch-{project_id}-{env_name}.yaml -n {namespace} --ignore-not-found=true
        echo "Deleting previous job: {previous_create_job}..."
        kubectl delete job {previous_create_job} -n {namespace} --ignore-not-found=true
        echo "Cleanup completed!"
        """

    # Define the job manifest
    job_manifest = {
        "apiVersion": "batch/v1",
        "kind": "Job",
        "metadata": {
            "name": job_name,
            "namespace": namespace,
            "labels": {
                "app": job_name,
                "owner": "duploservices",
                "tenantname": namespace,
            },
        },
        "spec": {
            "parallelism": 1,
            "completions": 1,
            "backoffLimit": 6,
            "template": {
                "metadata": {
                    "labels": {
                        "app": job_name,
                        "owner": "duploservices",
                        "tenantname": namespace,
                    },
                },
                "spec": {
                    "serviceAccountName": "duploservices-kavia-dev-edit-user",
                    "restartPolicy": "Never",
                    "containers": [
                        {
                            "name": "duplocloudcodegen",
                            "image": "bitnami/kubectl:latest",
                            "command": ["/bin/sh", "-c"],
                            "args": [kubectl_command],
                            "env": [
                                {"name": "ACTION", "value": action},
                                {"name": "PROJECT_ID", "value": project_id},
                                {"name": "ENV_NAME", "value": env_name},
                                {"name": "NAMESPACE", "value": namespace},
                            ],
                            "volumeMounts": [
                                {"name": "codegenservicedeployment4", "mountPath": "/app"},
                                {"name": "ingressservice1", "mountPath": "/app/ingress"},
                                {"name": "codegenpvc", "mountPath": "/app/pvc"},
                                {"name": "nginx", "mountPath": "/app/nginx"},
                            ],
                        }
                    ],
                    "volumes": [
                        {
                            "name": "codegenservicedeployment4",
                            "configMap": {"name": "codegenservicedeployment4"},
                        },
                        {
                            "name": "ingressservice1",
                            "configMap": {"name": "ingressservice1"},
                        },
                        {
                            "name": "codegenpvc",
                            "configMap": {"name": "codegenpvc"},
                        },
                        {
                            "name": "nginx",
                            "configMap": {"name": "nginx"},
                        },
                    ],
                },
            },
        },
    }

    # Check if the job already exists
    job_exists, existing_job = check_job_exists(job_name, namespace)
    
    if job_exists:
        print(f"Job {job_name} already exists")
        
        # If job is already completed, get logs and extract pod ID
        if existing_job.status.succeeded:
            print(f"Job {job_name} is already completed")
            code_gen_pod_id, _ = get_job_logs(job_name, namespace)
            
            if action == "create":
                print(f"Ingress hostname: {hostname}")
                
            return code_gen_pod_id
        else:
            # Job exists but not completed yet, wait for it
            print(f"Job {job_name} exists but not completed, waiting...")
            code_gen_pod_id = wait_for_job_completion(job_name, namespace)
            
            if action == "create":
                print(f"Ingress hostname: {hostname}")
                
            return code_gen_pod_id
    
    # Create the job if it doesn't exist
    try:
        response = batch_v1.create_namespaced_job(namespace=namespace, body=job_manifest)
        print(f"Job created: {response.metadata.name}")
        code_gen_id = wait_for_job_completion(job_name, namespace)
        
        if action == "create":
            print(f"Ingress hostname: {hostname}")

        return code_gen_id
    
    except ApiException as e:
        # If job is created by another process while we're checking
        if e.status == 409:  # Conflict error - already exists
            print(f"Job {job_name} was created by another process, waiting for completion...")
            code_gen_id = wait_for_job_completion(job_name, namespace)
            
            if action == "create":
                print(f"Ingress hostname: {hostname}")
                
            return code_gen_id
        else:
            print(f"An error occurred: {e}")
            raise