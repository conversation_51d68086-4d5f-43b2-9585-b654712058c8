import base64
import http.cookiejar
import urllib.request
from urllib.parse import urljoin

def encode_to_base64(input_string):
    byte_string = input_string.encode('utf-8')
    # Encode the bytes to base64
    base64_bytes = base64.b64encode(byte_string)
    # Convert base64 bytes back to string
    base64_string = base64_bytes.decode('utf-8')

    return base64_string


def get_cookie_and_url(auth_url):
    resp = {}
    # Create a CookieJar object to hold the cookies
    cookie_jar = http.cookiejar.CookieJar()

    # Create a custom HTTPRedirectHandler to capture the final URL after redirection
    class CustomRedirectHandler(urllib.request.HTTPRedirectHandler):
        def http_error_302(self, req, fp, code, msg, headers):
            self.redirected_url = headers['Location']
            return super().http_error_302(req, fp, code, msg, headers)

    # Instantiate the custom redirect handler
    redirect_handler = CustomRedirectHandler()

    # Create an opener to handle cookies and redirects
    opener = urllib.request.build_opener(
        urllib.request.HTTPCookieProcessor(cookie_jar),
        redirect_handler
    )

    # Make the request to the URL
    response = opener.open(auth_url)

    # Print the final redirected URL
    final_relative_url = response.geturl()
    if hasattr(redirect_handler, 'redirected_url'):
        final_relative_url = redirect_handler.redirected_url

    # Combine base URL with the final relative URL
    final_full_url = urljoin(auth_url, final_relative_url)
    resp["final_url"] = final_full_url
    cookies = {}

    if not cookie_jar:
        print("No cookies were set by the URL.")
    else:
        for cookie in cookie_jar:
            cookies[cookie.name] = cookie.value
    resp["cookies"] = cookies
    return resp

