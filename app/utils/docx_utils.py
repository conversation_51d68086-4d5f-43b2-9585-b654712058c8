"""
DOCX utilities for converting various data structures to Word documents.
"""

import io
import json
import re
from io import BytesIO
from typing import Any, Dict, List, Union
from urllib.parse import quote

import docx
import requests
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.shared import Inches

class DocxGenerator:
    """
    A utility class for generating DOCX documents from various data structures.
    """

    IGNORED_KEYS = {"created_by", "created_at", "configuration_state", "is_active"}

    def __init__(self):
        """Initialize the DocxGenerator."""
        self.document = None

    def create_new_document(self) -> docx.Document:
        """Create and return a new document instance."""
        self.document = docx.Document()
        return self.document

    def add_styled_heading(self, text: str, level: int = 1) -> None:
        """Add a styled heading to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        heading = self.document.add_heading(text, level=level)
        heading.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT

    def format_key_label(self, key: str) -> str:
        """
        Convert camelCase, PascalCase, or snake_case into human-readable format.
        E.g., "ArchitecturePattern" → "Architecture Pattern"
        """
        key = key.replace("_", " ")  # snake_case to space
        key = re.sub(r"(?<!^)(?=[A-Z])", " ", key)  # split camelCase or PascalCase
        return key.strip().title()

    def json_to_docx(self, data: Union[Dict, List, Any], level: int = 0) -> None:
        """
        Recursively convert JSON data to DOCX format.

        Args:
            data: The data to convert (dict, list, or primitive)
            level: Current nesting level for headings
        """
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        if isinstance(data, dict):
            self._process_dict(data, level)
        elif isinstance(data, list):
            self._process_list(data, level)
        else:
            self.document.add_paragraph(str(data), style="BodyText")

    def _process_dict(self, data: Dict, level: int) -> None:
        """Process dictionary data."""
        for key, value in data.items():
            if key in self.IGNORED_KEYS:
                continue

            display_key = self.format_key_label(key)

            if isinstance(value, (dict, list)):
                self.add_styled_heading(display_key, level=level + 1)
                self.json_to_docx(value, level + 1)
            else:
                self._add_key_value_paragraph(display_key, value)

    def _process_list(self, data: List, level: int) -> None:
        """Process list data."""
        for item in data:
            if isinstance(item, (dict, list)):
                self.json_to_docx(item, level + 1)
            else:
                self.document.add_paragraph(str(item), style="List Bullet")

    def _add_key_value_paragraph(self, key: str, value: Any) -> None:
        """Add a key-value pair as a paragraph."""
        paragraph = self.document.add_paragraph()
        run = paragraph.add_run(f"{key}: ")
        run.bold = True

        if isinstance(value, str) and "\n" in value:
            for line in value.split("\n"):
                if line.strip():
                    self.document.add_paragraph(line.strip(), style="List Bullet")
        else:
            paragraph.add_run(str(value))

    def add_field(
        self,
        label: str,
        value: Any,
        default: str = "N/A",
        style: str = "BodyText",
        bullet: bool = False,
    ) -> None:
        """
        Add a labeled field to the document.

        Args:
            label: The field label
            value: The field value
            default: Default value if value is None
            style: Paragraph style to use
            bullet: Whether to format list values as bullet points
        """
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        if value is None:
            value = default

        if bullet and isinstance(value, list):
            for item in value:
                self.document.add_paragraph(f"• {item}", style="List Bullet")
        else:
            para = self.document.add_paragraph()
            run = para.add_run(f"{label}: ")
            run.bold = True
            para.add_run(str(value))

    def add_requirement_root_to_docx(
        self, requirement_data: Dict, add_page_break: bool = False
    ) -> None:
        """Add requirement root data to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        if add_page_break:
            self.document.add_page_break()
        self.add_styled_heading("Requirement Test Case", level=1)

        self.add_field("Project Name", requirement_data.get("ProjectName"))
        self.add_field("Requirement Root", requirement_data.get("RequirementRootName"))

        # Epic section
        self.add_styled_heading("Epic", level=2)
        self.add_field("ID", requirement_data.get("EpicId"))
        self.add_field("Title", requirement_data.get("Epic"))
        self.add_field("Priority", requirement_data.get("EpicPriority"))
        self.add_field("Description", requirement_data.get("EpicDescription"))

        # User Story section
        self.add_styled_heading("User Story", level=2)
        self.add_field("ID", requirement_data.get("UserStoryId"))
        self.add_field("Title", requirement_data.get("UserStory"))
        self.add_field("Priority", requirement_data.get("UserStoryPriority"))
        self.add_field("Description", requirement_data.get("UserStoryDescription"))
        self.add_field(
            "Acceptance Criteria", requirement_data.get("UserStoryAcceptanceCriteria")
        )
        self.add_field("Story Points", requirement_data.get("UserStoryStoryPoints"))

        # Test Case section
        self.add_styled_heading("Test Case", level=2)
        self.add_field("Name", requirement_data.get("TestName"))
        self.add_field("Priority", requirement_data.get("TestPriority"))
        self.add_field("Category", requirement_data.get("TestCategory"))
        self.add_field("Type", requirement_data.get("TestType"))
        self.add_field("Level", requirement_data.get("TestLevel"))
        self.add_field("Description", requirement_data.get("TestDescription"))
        self.add_field("Pre-Conditions", requirement_data.get("PreConditions"))
        self.add_field("Expected Results", requirement_data.get("ExpectedResults"))
        self.add_field(
            "Acceptance Criteria", requirement_data.get("AcceptanceCriteria")
        )
        self.add_field(
            "Automated", "Yes" if requirement_data.get("TestAutomated") else "No"
        )

        self.add_field("Steps", requirement_data.get("Steps"), bullet=True)
        self.add_field("Tags", requirement_data.get("Tags"), bullet=True)

    def add_architectural_requirement_to_docx(
        self, data: Dict, add_page_break: bool = False
    ) -> None:
        """Add architectural requirement data to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        if add_page_break:
            self.document.add_page_break()
        self.add_styled_heading(data.get("Title", "Architectural Requirement"), level=1)

        self.add_field("Type", data.get("Type"))
        self.add_field("Change Reason", data.get("change_reason"))
        self.add_field("Configuration State", data.get("configuration_state"))

        # Description section
        self.add_styled_heading("Description", level=2)
        self.document.add_paragraph(data.get("Description", ""), style="BodyText")

        # Functional Requirements
        self.add_styled_heading("Functional Requirements", level=2)
        functional_req = data.get("functional_requirements", "")
        for line in functional_req.split("\n"):
            if line.strip():
                self.document.add_paragraph(line.strip(), style="List Number")

        # Architectural Requirements
        self.add_styled_heading("Architectural Requirements", level=2)
        arch_req = data.get("architectural_requirements", "")
        for line in arch_req.split("\n"):
            if line.strip():
                self.document.add_paragraph(line.strip(), style="List Number")

        # User Inputs (parsed JSON string)
        self.add_styled_heading("User Inputs", level=2)
        self._add_user_inputs_section(data.get("user_inputs", "{}"))

        # Change Log (parsed JSON)
        self.add_styled_heading("Change Log", level=2)
        self._add_change_log_section(data.get("change_log", "[]"))

        # Details
        self.add_styled_heading("Details", level=2)
        self.document.add_paragraph(data.get("Details", "N/A"))

    def _add_user_inputs_section(self, user_inputs_json: str) -> None:
        """Add user inputs section from JSON string."""
        try:
            ui_data = json.loads(user_inputs_json)
            for k, v in ui_data.items():
                label = k.replace("_", " ").title()
                label = label.replace("Analyzed", "Analyzed").replace(
                    "Created", "Created"
                )
                self.document.add_paragraph(f"{label}: {v}", style="List Bullet")
        except (json.JSONDecodeError, Exception):
            self.document.add_paragraph("User input data not available or invalid.")

    def _add_change_log_section(self, change_log_json: str) -> None:
        """Add change log section from JSON string."""
        try:
            log_data = json.loads(change_log_json)
            for entry in log_data:
                desc = entry.get("change", "No description")
                ts = entry.get("timestamp", "Unknown time")
                self.document.add_paragraph(f"- {desc} ({ts})", style="List Bullet")
        except (json.JSONDecodeError, Exception):
            self.document.add_paragraph("Change log not available or invalid.")

    def save_to_stream(self, title: str = "document") -> io.BytesIO:
        """
        Save the document to an in-memory stream and return it.

        Args:
            title: Title for the document filename

        Returns:
            BytesIO stream containing the saved document
        """
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        file_stream = io.BytesIO()
        self.document.save(file_stream)
        file_stream.seek(0)
        return file_stream

    def generate_content_disposition_header(self, title: str) -> str:
        """
        Generate a proper Content-Disposition header for file download.

        Args:
            title: The base title for the filename

        Returns:
            Content-Disposition header string
        """
        # Sanitize and encode the filename for the Content-Disposition header
        # Fallback for older browsers (ASCII only)
        ascii_filename = re.sub(r"[^a-zA-Z0-9._-]", "_", title)
        ascii_filename = f"{ascii_filename}_details.docx"

        # Modern approach using RFC 6266 (UTF-8)
        utf8_filename = quote(f"{title}_details.docx")

        return f"attachment; filename=\"{ascii_filename}\"; filename*=UTF-8''{utf8_filename}"

    def generate_project_document(self, node_data: Dict) -> None:
        """Generate a document for a project node."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        title = node_data.get("properties", {}).get("Title", "Node Details")
        self.add_styled_heading(title, level=0)

        # Recursively add all properties to the document
        self.json_to_docx(node_data.get("properties", {}))

    def generate_requirements_document(self, requirements: List[Dict]) -> None:
        """Generate a document for multiple requirements."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        for i, test_case in enumerate(requirements):
            # Add page break for all requirements except the first one
            add_page_break = i > 0
            self.add_requirement_root_to_docx(test_case, add_page_break=add_page_break)

    def add_system_context_to_docx(self, data: Dict) -> None:
        """Add system context node data to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        self.add_styled_heading(data.get("Title", "System Context"), level=1)
        self.add_field("Type", data.get("Type"))
        self.add_field("Change Reason", data.get("change_reason"))
        self.add_field("Configuration State", data.get("overview_config_state"))
        self.add_field("Users", data.get("Users"))
        self.add_field("External Systems", data.get("ExternalSystems"))

        self.add_styled_heading("Description", level=2)
        self.document.add_paragraph(data.get("Description", ""), style="BodyText")

        self.add_styled_heading("Details", level=2)
        self.document.add_paragraph(data.get("Details", ""), style="BodyText")

        self.add_styled_heading("User Inputs", level=2)
        self._add_user_inputs_section(data.get("user_inputs", "{}"))

        self.add_styled_heading("Change Log", level=2)
        self._add_change_log_section(data.get("change_log", "[]"))

        # Add Mermaid diagram as code if exists
        diagram = data.get("SystemContextDiagram", "")
        if diagram:
            self.add_mermaid_diagram(diagram, title="System Context Diagram")

    def add_container_to_docx(self, data: Dict, add_page_break: bool = False) -> None:
        """Add container node data to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        if add_page_break:
            self.document.add_page_break()

        self.add_styled_heading(data.get("Title", "Container"), level=1)

        self.add_field("Type", data.get("Type"))
        self.add_field("Container Type", data.get("ContainerType"))
        self.add_field("Description", data.get("Description"))
        self.add_field("Platform", data.get("Selected_Platform"))
        self.add_field("Technology Stack", data.get("Selected_Tech_Stack"))
        self.add_field("Container Category", data.get("ContainerCategory"))
        self.add_field("Repository Name", data.get("Repository_Name"))
        self.add_field(
            "Implemented Requirements", data.get("ImplementedRequirementIDs")
        )
        self.add_field("User Interactions", data.get("UserInteractions"))
        self.add_field(
            "External System Interactions", data.get("ExternalSystemInteractions")
        )
        self.add_field("Has Database", "Yes" if data.get("HasDatabase") else "No")
        self.add_field("Configuration State", data.get("configuration_state"))
        self.add_field("Change Reason", data.get("change_reason"))

        self.add_styled_heading("User Inputs", level=2)
        self._add_user_inputs_section(data.get("user_inputs", "{}"))

        self.add_styled_heading("Change Log", level=2)
        self._add_change_log_section(data.get("change_log", "[]"))

        # Add container diagram as raw Mermaid syntax
        diagram = data.get("ContainerDiagram", "")
        if diagram:
            self.add_styled_heading("Container Diagram (Mermaid)", level=2)
            self.document.add_paragraph(
                "Mermaid diagram syntax (not rendered):", style="BodyText"
            )
            self.document.add_paragraph(diagram, style="BodyText")

    def add_component_to_docx(self, data: Dict, add_page_break: bool = False) -> None:
        """Add component details to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        if add_page_break:
            self.document.add_page_break()

        self.add_styled_heading(data.get("Title", "Component"), level=2)

        self.add_field("ID", data.get("ID"))
        self.add_field("Type", data.get("Type"))
        self.add_field("Description", data.get("Description"))
        self.add_field("Change Reason", data.get("change_reason"))
        self.add_field("Configuration State", data.get("design_detail_state"))

        # Optional Sections (Markdown-ish)
        if data.get("Design_Details"):
            self.add_styled_heading("Design Details", level=3)
            for para in data["Design_Details"].split("\n\n"):
                self.document.add_paragraph(para.strip(), style="BodyText")

        if data.get("Functionality"):
            self.add_styled_heading("Functionality", level=3)
            for para in data["Functionality"].split("\n\n"):
                self.document.add_paragraph(para.strip(), style="BodyText")

        # User Inputs
        self.add_styled_heading("User Inputs", level=3)
        self._add_user_inputs_section(data.get("user_inputs", "{}"))

        # Change Log
        self.add_styled_heading("Change Log", level=3)
        try:
            raw_log = data.get("change_log", "{}")
            parsed = json.loads(raw_log)
            for entry in parsed.get("changes", []):
                self.document.add_paragraph(
                    f"- {entry.get('description')} ({entry.get('date')})",
                    style="List Bullet",
                )
        except Exception:
            self.document.add_paragraph(
                "Invalid or missing change log.", style="BodyText"
            )

    def add_design_node_to_docx(self, data: Dict) -> None:
        """Add design node with associated diagrams and algorithms to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        props = data.get("properties", {})
        self.add_styled_heading(props.get("Title", "Design"), level=2)

        self.add_field("Type", props.get("Type"))
        self.add_field("Description", props.get("Description"))
        self.add_field("Behavior Description", props.get("BehaviorDescription"))
        self.add_field(
            "Component Interactions", props.get("ComponentInteractionsDescription")
        )
        self.add_field("Change Needed", str(props.get("changes_needed")))
        self.add_field("Behavior Config State", props.get("behavior_config_state"))
        self.add_field(
            "Component Interactions State",
            props.get("component_interactions_config_state"),
        )
        self.add_field("Class Diagram State", props.get("class_diagrams_config_state"))

        self.add_styled_heading("Class Diagram Description", level=3)
        self.document.add_paragraph(
            props.get("ClassDiagramDescription", ""), style="BodyText"
        )

        # ClassDiagram block
        for cd in data.get("ClassDiagram", []):
            self.add_styled_heading(cd.get("Title", "Class Diagram"), level=3)
            diagram = cd.get("Diagram", "")
            if diagram:
                self.add_mermaid_diagram(diagram, title="System Context Diagram")

        # Sequence diagrams
        for sd in data.get("Sequence", []):
            self.add_styled_heading(sd.get("Title", "Sequence Diagram"), level=3)
            self.document.add_paragraph(sd.get("Description", ""), style="BodyText")
            diagram = cd.get("Diagram", "")
            if diagram:
                self.add_mermaid_diagram(diagram, title="System Context Diagram")

        # State diagrams
        for sd in data.get("StateDiagram", []):
            self.add_styled_heading(sd.get("Title", "State Diagram"), level=3)
            diagram = cd.get("Diagram", "")
            if diagram:
                self.add_mermaid_diagram(diagram, title="System Context Diagram")

        # Algorithms
        for algo in data.get("Algorithm", []):
            self.add_styled_heading(algo.get("Title", "Algorithm"), level=3)
            self.document.add_paragraph(algo.get("Details", ""), style="BodyText")

    def add_interface_to_docx(self, data: Dict, add_page_break: bool = True) -> None:
        """Add an interface node to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        if add_page_break:
            self.document.add_page_break()

        self.add_styled_heading(data.get("Title", "Interface"), level=1)
        self.add_field("Type", data.get("Type"))
        self.add_styled_heading("Description", level=2)
        self.document.add_paragraph(data.get("Description", ""), style="BodyText")

    def add_sad_documentation_to_docx(self, doc: Dict, sections: List[Dict]) -> None:
        """Add SAD documentation and its sections to the document."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        self.document.add_page_break()
        self.add_styled_heading(
            doc.get("Title", "Software Architecture Document"), level=1
        )
        self.add_field("Type", doc.get("DocumentationType", "SAD"))
        self.add_field("Version", doc.get("Version", "N/A"))
        self.add_styled_heading("Description", level=2)
        self.document.add_paragraph(doc.get("Description", ""), style="BodyText")

        self.add_styled_heading("Full Content", level=2)
        self.document.add_paragraph(doc.get("Content", ""), style="BodyText")

        # Add each section
        for section in sorted(sections, key=lambda x: x.get("Order", 0)):
            self.document.add_page_break()
            self.add_styled_heading(section.get("Title", "Section"), level=2)
            self.add_field("Section Type", section.get("SectionType"))
            self.add_field("Version", section.get("Version"))
            self.add_field("Description", section.get("Description"))

            # Check if the section contains Mermaid code and replace it with images
            content = section.get("Content", "")
            mermaid_code_blocks = self.extract_mermaid_code(content)

            for mermaid_code in mermaid_code_blocks:
                self.add_mermaid_diagram(mermaid_code)

            self.add_styled_heading("Section Content", level=3)
            # self.document.add_paragraph(section.get("Content", ""), style="BodyText")
            # Remove the Mermaid code from the content before adding it to the document
            content_without_mermaid = self.remove_mermaid_code(content)
            self.document.add_paragraph(content_without_mermaid, style="BodyText")

    def extract_mermaid_code(self, content: str) -> list:
        """Extract all Mermaid code blocks from the content."""
        mermaid_pattern = r"```mermaid(.*?)```"
        mermaid_code_blocks = re.findall(mermaid_pattern, content, re.DOTALL)
        return mermaid_code_blocks

    def remove_mermaid_code(self, content: str) -> str:
        """Remove all Mermaid code blocks from the content."""
        mermaid_pattern = r"```mermaid(.*?)```"
        content_without_mermaid = re.sub(mermaid_pattern, "", content, flags=re.DOTALL)
        return content_without_mermaid

    def add_documentation_to_docx(
        self, doc: Dict, sections: List[Dict], doc_type: str = "SAD"
    ) -> None:
        """Generic handler for SAD, PRD, and other documentation exports."""
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        self.document.add_page_break()
        title = doc.get("Title", f"{doc_type} Documentation")
        self.add_styled_heading(title, level=1)

        self.add_field("Type", doc.get("DocumentationType", doc_type))
        self.add_field("Version", doc.get("Version", "N/A"))
        self.add_styled_heading("Description", level=2)
        self.document.add_paragraph(doc.get("Description", ""), style="BodyText")

        if doc.get("Content"):
            
            # Check if the section contains Mermaid code and replace it with images
            content = section.get("Content", "")
            mermaid_code_blocks = self.extract_mermaid_code(content)

            for mermaid_code in mermaid_code_blocks:
                self.add_mermaid_diagram(mermaid_code)

            self.add_styled_heading("Full Content", level=2)
            # self.document.add_paragraph(doc["Content"], style="BodyText")
            # Remove the Mermaid code from the content before adding it to the document
            content_without_mermaid = self.remove_mermaid_code(content)
            self.document.add_paragraph(content_without_mermaid, style="BodyText")

        for section in sorted(sections, key=lambda x: x.get("Order", 0)):
            self.document.add_page_break()
            self.add_styled_heading(section.get("Title", "Section"), level=2)
            self.add_field("Section Type", section.get("SectionType"))
            self.add_field("Version", section.get("Version"))
            self.add_field("Description", section.get("Description"))

            if section.get("Content"):
                self.add_styled_heading("Section Content", level=3)
                self.document.add_paragraph(section["Content"], style="BodyText")

    def add_test_cases_section(self, test_cases: List[Dict]) -> None:
        if not self.document:
            raise ValueError(
                "Document not initialized. Call create_new_document() first."
            )

        # 🛑 Only add a page break if the document already has content
        if self.document.paragraphs:
            self.document.add_page_break()

        self.add_styled_heading("Test Cases", level=1)

        for test in test_cases:
            props = test.get("properties", {})

            self.add_styled_heading(props.get("Title", "Unnamed Test Case"), level=2)
            self.add_field("Type", props.get("Type"))
            self.add_field("Test Level", props.get("TestLevel"))
            self.add_field("Category", props.get("Category"))
            self.add_field("Environment", props.get("TestEnvironment"))
            self.add_field("Can Be Automated", str(props.get("CanBeAutomated", False)))
            self.add_field("Description", props.get("Description"))

            if props.get("TestProcedure"):
                self.add_styled_heading("Test Procedure", level=3)
                self.document.add_paragraph(props["TestProcedure"], style="BodyText")

            if props.get("ExpectedResults"):
                self.add_styled_heading("Expected Results", level=3)
                self.document.add_paragraph(props["ExpectedResults"], style="BodyText")

            if props.get("MeasurementMetrics"):
                self.add_styled_heading("Measurement Metrics", level=3)
                for metric in props["MeasurementMetrics"]:
                    self.document.add_paragraph(f"• {metric}", style="List Bullet")

    def add_mermaid_diagram(self, mermaid_code: str, title: str = "Diagram") -> None:
        if not self.document:
            raise ValueError("Document not initialized.")

        self.add_styled_heading(title, level=2)

        try:
            # Send Mermaid code to Kroki.io API
            # Use POST method with JSON payload (more reliable)
            url = "https://kroki.io/mermaid/png"
            headers = {"Content-Type": "text/plain"}

            response = requests.post(url, data=mermaid_code, headers=headers)

            if response.status_code == 200:
                image_stream = BytesIO(response.content)
                self.document.add_picture(image_stream, width=Inches(6))
            else:
                raise Exception(
                    f"Kroki Error: {response.status_code} - {response.text}"
                )

        except Exception as e:
            self.document.add_paragraph(
                f"❌ Failed to render Mermaid diagram: {str(e)}", style="BodyText"
            )
            self.document.add_paragraph("Diagram syntax:", style="BodyText")
            self.document.add_paragraph(mermaid_code, style="BodyText")
