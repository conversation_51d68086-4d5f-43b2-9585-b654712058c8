
from app.connection.tenant_middleware import get_tenant_id
from app.models.user_model import ModuleConfig
from app.core.Settings import settings
from app.models.notification_model import NotificationModel
from datetime import datetime
import json
from app.connection.establish_db_connection import get_mongo_db_v1
from app.repository.mongodb.user_repository import UserRepository
from app.repository.mongodb.notification_repository import NotificationRepository
from app.repository.mongodb.client import get_db
from app.repository.mongodb.repositories import LLMCostsRepository, CodeGenTasksRepository, ConfigRepository
from typing import Optional
from app.services.notification_service import NotificationService

def get_initials_from_name(name: str):
    if name == "":
        return "KA" # Default initials
    name_parts = name.split()
    initials = ""
    for part in name_parts:
        initials += part[0].upper()
    return initials
    
async def get_module_configuration_for_user(module_name: str, user_id: str, db = None):
    """Get the module configuration for a user."""
    try:
        config_repo: ConfigRepository = await get_mongo_db_v1("config")
        if not db:
            db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        return await config_repo.get_module_configuration(module_name, user_id, db)
    except Exception as e:
        print("Error getting module configuration: ", e)
        return {}
    
async def track_project_usage(user_id: str, project_id: int, project_name: str):
    """Tracks project usage in MongoDB."""
    try:
        user_repo: UserRepository = await get_mongo_db_v1("user")
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        await user_repo.track_project_usage(user_id=user_id, project_id=project_id, db=db, project_name=project_name)

    except Exception as e:
        print(f"Error tracking project usage: {str(e)}")

async def get_recent_project_usage(user_id: str):
    """Gets recent project usage from MongoDB."""
    try:
        user_repo: UserRepository = await get_mongo_db_v1("user")
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        return await user_repo.get_recent_project_usage(user_id=user_id, db = db)

    except Exception as e:
        print(f"Error getting recent project usage: {str(e)}")
        return []

async def delete_project_usage(project_id: int):
    """Deletes project usage records."""
    try:
        user_repo: UserRepository = await get_mongo_db_v1("user")
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        return await user_repo.delete_project_usage(project_id=project_id, db = db)

    except Exception as e:
        print(f"Error deleting project usage: {str(e)}")
        return False
    
async def send_notification(notification_data: NotificationModel):
    """Sends a notification using the NotificationService"""
    try:
        notification_service = NotificationService()
        return await notification_service.send_notification(notification_data)
    except Exception as e:
        print(f"Error sending notification: {str(e)}")
        return False

async def delete_notifications(project_id: int):
    """Deletes notifications for a project."""
    try:
        notification_repo: NotificationRepository = await get_mongo_db_v1("notification")
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        return await notification_repo.delete_project_notifications(project_id, db)
    except Exception as e:
        print(f"Error deleting notifications: {str(e)}")
        return False
        

# In your get_llm_costs function
async def get_llm_costs(user_id: Optional[str] = None):
    """Retrieve LLM costs for a given user_id."""
    try:
        llm_costs_repo: LLMCostsRepository = await get_mongo_db_v1("llm_costs")
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        return await llm_costs_repo.get_llm_costs(user_id, db)
    except Exception as e:
        print(f"Error in get_llm_costs: {str(e)}")
        return None
    

async def find_code_gen_tasks(project_id: int, user_id: str):
    """Find code generation tasks and their associated credits."""
    try:
        code_gen_repo: CodeGenTasksRepository = await get_mongo_db_v1("code_gen_tasks")
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        return await code_gen_repo.find_code_gen_tasks_credits(project_id, user_id, db)
    except Exception as e:
        print(f"Error in find_code_gen_tasks: {str(e)}")
        return 0
    
def filter_document_by_month(doc: dict, month: int, year: int) -> dict:
    """
    Filter a single document's costs by month and year
    """
    print(f"Filtering for month: {month}, year: {year}")
    if not doc:
        return None
    
    # If month is 0 (All Months), return the complete document
    if month == 0:
        return doc
        
    filtered_data = doc.copy()
    filtered_projects = []

    for project in doc.get('projects', []):
        filtered_agents = []
        for agent in project.get('agents', []):
            filtered_costs_by_date = {}
            filtered_tokens_by_date = {}
            
            
            for date, cost in agent.get('costs_by_date', {}).items():
                try:
                    date_obj = datetime.strptime(date, '%Y-%m-%d')
                    if date_obj.month == month and date_obj.year == year:
                        filtered_costs_by_date[date] = cost
                        filtered_tokens_by_date[date] = agent['tokens_by_date'][date]
                except ValueError as e:
                    print(f"Error parsing date {date}: {e}")
                    continue

            if filtered_costs_by_date:
                filtered_agent = {
                    'agent_name': agent['agent_name'],
                    'total_cost': sum_costs([cost for cost in filtered_costs_by_date.values()]),
                    'costs_by_date': filtered_costs_by_date,
                    'tokens_by_date': filtered_tokens_by_date
                }
                filtered_agents.append(filtered_agent)

        if filtered_agents:
            filtered_project = {
                'project_id': project['project_id'],
                'project_cost': sum_costs([agent['total_cost'] for agent in filtered_agents]),
                'agents': filtered_agents
            }
            filtered_projects.append(filtered_project)

    if filtered_projects:
        filtered_data['projects'] = filtered_projects
        filtered_data['user_cost'] = sum_costs([project['project_cost'] for project in filtered_projects])
        return filtered_data
        
    print("No data found after filtering")
    return None

def sum_costs(costs) -> str:
    """
    Sum cost strings and return formatted string
    """
    total = sum(float(cost.replace('$', '')) for cost in costs)
    return f"${total:.6f}"