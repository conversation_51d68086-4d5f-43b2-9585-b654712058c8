import json
import os
import tempfile
import logging
from datetime import datetime

def save_messages_to_file(discussion_id: int, messages: list) -> str:
    """
    Save discussion messages to a JSON file in temp directory.
    
    Args:
        discussion_id: The ID of the discussion
        messages: List of messages to save
    
    Returns:
        str: Path to the saved file
    """
    try:
        # Get system temp directory
        temp_dir = tempfile.gettempdir()
        
        # Create a timestamp for unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create filename with timestamp
        filename = f"discussion_{discussion_id}_{timestamp}.json"
        
        # Full path
        file_path = os.path.join(temp_dir, filename)
        
        # Format the data with proper indentation
        formatted_json = json.dumps(messages, indent=4, ensure_ascii=False)
        
        # Write to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(formatted_json)
        
        print(f"File saved successfully at: {file_path}")
        return file_path
        
    except Exception as e:
        logging.error(f"Error saving messages to file: {str(e)}")
        return None

# Example usage in your main code:
# await node_db.update_node_by_id(int(discussion_id), {'Discussion': json.dumps(messages)})
# file_path = save_messages_to_file(int(discussion_id), messages)