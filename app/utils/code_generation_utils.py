import os
from app.core.file_watch import FileWatch
from app.connection.tenant_middleware import get_tenant_id
import json
from datetime import datetime
from enum import Enum
import tempfile
from code_generation_core_agent.agents.project_welcome_page import  ContainerType
from app.utils.datetime_utils import generate_timestamp


diffPath = '.diff'
input_arguments = os.environ.get("input_arguments", "{}")
input_arguments = json.loads(input_arguments)

def custom_asdict_factory(data):
    def convert(obj):
        if isinstance(obj, Enum):
            return obj.value
        if isinstance(obj, datetime):
            return obj.isoformat()
        return obj

    return {k: convert(v) for k, v in data}

def get_efs_manifest_path(project_id: str):
    if os.environ.get('LOCAL_DEBUG'):
        base_dir = f'/tmp/kavia/{get_tenant_id()}/{project_id}/manifest'  
        
    else:
        base_dir = f'/app/data/{get_tenant_id()}/{project_id}/manifest'
        
    os.makedirs(base_dir, exist_ok=True)
    return base_dir
        

def get_codegeneration_path(agent_name = "CodeGeneration", task_id=input_arguments.get("task_id")):
    if os.environ.get('LOCAL_DEBUG'):
        if(agent_name=="DocumentCreation"):
            base_dir = f"/tmp/kavia/workspace/{task_id}"
        else:
            base_dir = '/tmp/kavia/workspace/code-generation'
    else :
        if(agent_name=="DocumentCreation"):
            base_dir = f"/home/<USER>/workspace/{task_id}"
        else:
            base_dir = f'/home/<USER>/workspace/code-generation'
    
    os.makedirs(base_dir, exist_ok=True)
    return base_dir

def get_logs_path(task_id: str):
    if os.environ.get('LOCAL_DEBUG'):
        return f'/tmp/kavia/workspace'
    else :
        return f'/tmp/kavia/logs/{task_id}'
    
def initialize_filewatch_dir():
    if os.listdir(get_codegeneration_path()) == []:
        print("No projects in the code generation path.")
        return
    base_dir = os.path.join(get_codegeneration_path() , os.listdir(get_codegeneration_path())[0])
    print(f"Initializing filewatch for {base_dir}")
    filewatch = FileWatch(base_dir=base_dir, diffs_dir=diffPath)
    return filewatch


def get_container_type(container):
    container_type = container.get('platform', '')
    print("Container -->", container)
    print("Container type --> ", container_type)
    if container.get("framework","").casefold() in ['android', 'kotlin', 'flutter']:
        return ContainerType.MOBILE.value
    if container_type == 'web':
        return ContainerType.FRONTEND.value
    elif container_type == 'backend':
        return ContainerType.BACKEND.value
    elif container_type == 'database':
        return ContainerType.DATABASE.value
    elif container_type == 'mobile':
        return ContainerType.MOBILE.value
    return ContainerType.FRONTEND.value


def convert_manifest_to_yaml_string(project_schema):
    """Convert ProjectSchema to YAML string using existing save method"""
    try:
        # Create a temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as temp_file:
            temp_path = temp_file.name
        
        try:
            # Use existing save_to_manifest method
            project_schema.save_to_manifest(temp_path)
            
            # Read the YAML content
            with open(temp_path, 'r', encoding='utf-8') as f:
                yaml_content = f.read()
            
            return yaml_content
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)
                
    except Exception as e:
        print(f"Error converting manifest to YAML: {str(e)}")
        raise
    
def load_project_schema_from_yaml_string(yaml_string):
    """Load ProjectSchema from YAML string (for when you have access to ProjectSchema)"""
    from code_generation_core_agent.agents.project_welcome_page import ProjectSchema
    try:
        # Create a temporary file with the YAML content
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as temp_file:
            temp_file.write(yaml_string)
            temp_path = temp_file.name
        
        try:
            # Use existing load_from_file method
            project_schema = ProjectSchema.load_from_file(temp_path)
            return project_schema
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)
                
    except Exception as e:
        print(f"Error loading ProjectSchema from YAML: {str(e)}")
        raise
    
import yaml

def yaml_to_json(yaml_string: str)-> dict:
    try:
        return yaml.safe_load(yaml_string)
    except yaml.YAMLError as e:
        raise ValueError(f"Invalid YAML: {e}")

def json_to_yaml(json_string: str) -> str:
    try:
        return yaml.dump(json.loads(json_string), default_flow_style=False, indent=2)
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON: {e}")


def import_docs_to_manifest_path(folder_name: str, file_name: str, content: str,
                                agent_name: str = "CodeGeneration", task_id: str = None) -> dict:
    """
    Import documentation files into the project manifest path.
    Creates a docs folder structure within the manifest workspace.

    Args:
        folder_name (str): Name of the documentation folder (e.g., 'api-docs', 'user-guides')
        file_name (str): Name of the file to create (with extension)
        content (str): Content to write to the file
        agent_name (str): Agent name for path resolution ('CodeGeneration', 'CodeMaintenance', 'DocumentCreation')
        task_id (str): Task ID for path resolution (optional, uses global if not provided)

    Returns:
        dict: Result containing status, file_path, and any errors
    """
    try:
        # Get the appropriate task_id
        if task_id is None:
            task_id = input_arguments.get("task_id")

        # Get the base code generation path
        base_path = get_codegeneration_path(agent_name=agent_name, task_id=task_id)

        # Create docs folder structure
        docs_base_path = os.path.join(base_path, "docs")
        folder_path = os.path.join(docs_base_path, folder_name)

        # Ensure the directory exists
        os.makedirs(folder_path, exist_ok=True)

        # Create the full file path
        file_path = os.path.join(folder_path, file_name)

        # Write the content to the file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        # Log the operation
        print(f"[{generate_timestamp()}] Documentation file created: {file_path}")

        return {
            "status": "success",
            "message": f"Documentation file '{file_name}' created successfully in '{folder_name}' folder",
            "file_path": file_path,
            "folder_path": folder_path,
            "docs_base_path": docs_base_path,
            "agent_name": agent_name,
            "task_id": task_id
        }

    except Exception as e:
        error_msg = f"Error importing documentation file: {str(e)}"
        print(f"[{generate_timestamp()}] {error_msg}")
        return {
            "status": "error",
            "message": error_msg,
            "file_path": None,
            "error": str(e)
        }


async def restore_docs_from_mongodb(task_id: str, agent_name: str = "CodeGeneration") -> dict:
    """
    Restore documentation from MongoDB using the get_task_documentation function.
    Creates documents in the kavia-docs folder based on task_id.

    Args:
        task_id (str): The task ID to retrieve documentation for
        agent_name (str): Agent name for path resolution

    Returns:
        dict: Result containing restored files and their paths
    """
    try:
        from app.routes.docs_query import get_task_documentation

        print(f"[{generate_timestamp()}] Restoring docs from MongoDB for task: {task_id}")

        # Get documentation from MongoDB using the existing function
        doc_response = await get_task_documentation(task_id)

        if doc_response.status == "no_documentation_found" or not doc_response.docs:
            return {
                "status": "no_documents",
                "message": f"No documents found in MongoDB for task {task_id}",
                "task_id": task_id,
                "restored_files": []
            }

        # Get the base path for kavia-docs
        base_path = get_codegeneration_path(agent_name=agent_name, task_id=task_id)
        kavia_docs_path = os.path.join(base_path, "kavia-docs")

        # Create kavia-docs directory only if documents exist
        os.makedirs(kavia_docs_path, exist_ok=True)

        restored_files = []
        errors = []

        for i, doc in enumerate(doc_response.docs):
            try:
                content = doc.get("content", "")
                file_path = doc.get("file_path", "")
                operation = doc.get("operation", "")
                timestamp = doc.get("timestamp", "")

                # Generate a meaningful filename
                if file_path:
                    filename = os.path.basename(file_path)
                    if not filename:
                        filename = f"document_{i+1}.md"
                else:
                    filename = f"document_{i+1}.md"

                # Ensure the filename has an appropriate extension
                if not filename.endswith(('.md', '.txt', '.yaml', '.json', '.py', '.js', '.html', '.css')):
                    filename += '.md'

                # Create the full path in kavia-docs
                full_file_path = os.path.join(kavia_docs_path, filename)

                # Add metadata header to the content
                metadata_header = f"""<!--
MongoDB Document Metadata:
- Original File Path: {file_path}
- Operation: {operation}
- Timestamp: {timestamp}
- Restored At: {generate_timestamp()}
- Task ID: {task_id}
-->

"""

                # Write the file with metadata header
                with open(full_file_path, 'w', encoding='utf-8') as f:
                    f.write(metadata_header + content)

                restored_files.append({
                    "filename": filename,
                    "path": full_file_path,
                    "original_file_path": file_path,
                    "operation": operation,
                    "timestamp": timestamp,
                    "size": len(content)
                })

                print(f"[{generate_timestamp()}] Restored document to kavia-docs: {filename}")

            except Exception as e:
                error_msg = f"Error restoring document {i+1}: {str(e)}"
                errors.append(error_msg)
                print(f"[{generate_timestamp()}] {error_msg}")

        return {
            "status": "success",
            "message": f"Successfully restored {len(restored_files)} documents to kavia-docs",
            "task_id": task_id,
            "kavia_docs_path": kavia_docs_path,
            "restored_files": restored_files,
            "total_documents": len(doc_response.docs),
            "successful_restores": len(restored_files),
            "errors": errors
        }

    except Exception as e:
        error_msg = f"Error restoring documents from MongoDB: {str(e)}"
        print(f"[{generate_timestamp()}] {error_msg}")
        return {
            "status": "error",
            "message": error_msg,
            "error": str(e)
        }





async def restore_docs_during_resume(task_id: str, agent_name: str = "CodeGeneration") -> dict:
    """
    Restore documents from MongoDB during resume operations.
    Creates kavia-docs folder only if documents exist in MongoDB.

    Args:
        task_id (str): The task ID for the resume operation
        agent_name (str): Agent name for path resolution

    Returns:
        dict: Result containing restore operation status
    """
    try:
        print(f"[{generate_timestamp()}] Starting document restore for resume: {task_id}")

        # Restore MongoDB documents to kavia-docs using get_task_documentation
        restore_result = await restore_docs_from_mongodb(task_id, agent_name)

        if restore_result["status"] == "success":
            print(f"[{generate_timestamp()}] Successfully restored {restore_result['successful_restores']} documents to kavia-docs")
        elif restore_result["status"] == "no_documents":
            print(f"[{generate_timestamp()}] No documents found in MongoDB for task {task_id}, skipping kavia-docs creation")
        else:
            print(f"[{generate_timestamp()}] Document restore failed: {restore_result['message']}")

        return restore_result

    except Exception as e:
        error_msg = f"Error during document restore: {str(e)}"
        print(f"[{generate_timestamp()}] {error_msg}")
        return {
            "status": "error",
            "message": error_msg,
            "error": str(e),
            "task_id": task_id,
            "agent_name": agent_name
        }





def get_kavia_docs_structure(task_id: str, agent_name: str = "CodeGeneration") -> dict:
    """
    Get the current structure of the kavia-docs folder.
    Useful for debugging and verification.

    Args:
        task_id (str): The task ID
        agent_name (str): Agent name for path resolution

    Returns:
        dict: Structure information of kavia-docs folder
    """
    try:
        base_path = get_codegeneration_path(agent_name=agent_name, task_id=task_id)
        kavia_docs_path = os.path.join(base_path, "kavia-docs")

        if not os.path.exists(kavia_docs_path):
            return {
                "status": "not_found",
                "message": "kavia-docs folder does not exist",
                "path": kavia_docs_path,
                "files": []
            }

        files = []
        total_size = 0

        for root, _, filenames in os.walk(kavia_docs_path):
            for filename in filenames:
                file_path = os.path.join(root, filename)
                relative_path = os.path.relpath(file_path, kavia_docs_path)

                try:
                    stat = os.stat(file_path)
                    file_size = stat.st_size
                    modified_time = datetime.fromtimestamp(stat.st_mtime).isoformat()

                    files.append({
                        "name": filename,
                        "relative_path": relative_path,
                        "full_path": file_path,
                        "size": file_size,
                        "modified": modified_time
                    })

                    total_size += file_size

                except Exception as e:
                    print(f"Error getting file stats for {file_path}: {e}")

        return {
            "status": "success",
            "message": f"Found {len(files)} files in kavia-docs",
            "path": kavia_docs_path,
            "files": files,
            "total_files": len(files),
            "total_size": total_size,
            "task_id": task_id,
            "agent_name": agent_name
        }

    except Exception as e:
        error_msg = f"Error getting kavia-docs structure: {str(e)}"
        print(f"[{generate_timestamp()}] {error_msg}")
        return {
            "status": "error",
            "message": error_msg,
            "error": str(e)
        }