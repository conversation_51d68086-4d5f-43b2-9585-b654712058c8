
import json
import requests
import re
from typing import Dict, List, Any

def extract_file_key(figma_link: str) -> str:
    match = re.search(r"file/([a-zA-Z0-9]+)", figma_link)
    return match.group(1) if match else None

def extract_frame_data(data: Dict[str, Any]) -> List[Dict[str, Any]]:
    frames = []
    def traverse(node):
        if node.get("type") == "FRAME":
            frames.append({
                "id": node["id"],
                "name": node["name"],
                "type": node["type"]
            })
        for child in node.get("children", []):
            traverse(child)

    traverse(data["document"])
    return frames

def get_figma_file_data(figma_link: str, access_token: str) -> Dict[str, Any]:
    """
    Extract Figma file data including frames and their images
    
    Args:
        figma_link (str): Figma file URL
        access_token (str): Figma access token
    
    Returns:
        Dict containing frames with images, file key, and document data
    """
    # Extract file key from link
    file_key = extract_file_key(figma_link)
    if not file_key:
        raise ValueError("Invalid Figma link")
    # Get file data
    url = f"https://api.figma.com/v1/files/{file_key}"
    headers = {"X-Figma-Token": access_token}

    response = requests.get(url, headers=headers, timeout=300)
    response.raise_for_status()
    data = response.json()

    # Extract frames
    frames = extract_frame_data(data)
    frame_ids = [frame["id"] for frame in frames]

    # Get frame images
    image_url = f"https://api.figma.com/v1/images/{file_key}"
    params = {"ids": ",".join(frame_ids), "scale": 1, "format": "png"}
    img_response = requests.get(image_url, headers=headers, params=params, timeout=300)
    img_response.raise_for_status()
    image_urls = img_response.json()["images"]

    # Combine frames with their image URLs
    frames_with_images = [
        {**frame, "imageUrl": image_urls.get(frame["id"])}
        for frame in frames
    ]

    return {
        "frames": frames_with_images,
        "fileKey": file_key,
        "document": data["document"]
    }

# Example usage:
if __name__ == "__main__":
    # Replace with actual values
    FIGMA_ACCESS_TOKEN = "*********************************************"
    figma_link = "https://www.figma.com/design/xXwtsA7MzI0wUHUpYiMQQr/Dashboard-(Community)?node-id=0-1&t=dStBTrhDwAq6AJWU-1" 

    try:
        result = get_figma_file_data(figma_link, FIGMA_ACCESS_TOKEN)
        #print("Frames:", len(result["frames"]))
        #print("File key:", result["fileKey"])
        fcomponents = { 'figma_components' : [result] }
        with open('components.json', 'w') as f:
             json.dump(fcomponents, f, indent=2)

    except Exception as e:
        print(f"Error: {e}")