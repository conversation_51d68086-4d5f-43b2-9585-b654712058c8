from datetime import datetime
from app.core.websocket.client import WebSocketClient
from app.models.code_generation_model import Message
from app.core.constants import TASKS_COLLECTION_NAME 
from app.core.git_llm import GitLLM
from app.utils.code_generation_utils import get_logs_path
from app.utils.datetime_utils import generate_timestamp
def send_agent_message_custom(ws_client:WebSocketClient, task_id, message:str, db):
    """Helper function to send agent messages through WebSocket and store in DB"""
    try:
        message_obj = Message(
            content=str(message), 
            sender="AI", 
            timestamp=generate_timestamp()
        )
        
        if ws_client is not None:
            print("Sending message: ",message_obj.to_dict() )
            ws_client.send_message("message_added", message_obj.to_dict())
            
        try:
            if db is not None:
                db[TASKS_COLLECTION_NAME].update_one(
                    {"_id": task_id},
                    {"$push": {"messages": message_obj.to_dict()}}
                )
        except:
            pass
    except Exception as e:
        print(f"Exception sending message: {e}")
        pass

def send_git_message( content: str, task_id:str, repository_metadata:dict, base_path:str, ws_client:WebSocketClient, db ):
    """Helper method to send messages through websocket and update DB"""
    # Truncate content if it exceeds 2500 characters
    try:
        if len(content) > 2500:
            content = content[:2497] + "..."
        logs_path = get_logs_path(task_id)
        git_llm = GitLLM(task_id=task_id, logs_path=logs_path)
        content = git_llm.remove_sensitive_patterns(content)
        message_obj = Message(
            content=content, 
            sender="Git", 
            timestamp=generate_timestamp()
        )
        
        # Set additional attributes after initialization
        message_obj.parent_id = repository_metadata.get('repositoryId')
        message_obj.metadata = {
            "repositoryId": repository_metadata.get('repositoryId'),
            "repositoryName": repository_metadata.get('repositoryName'),
            "repositoryPath": base_path
        }
        
        if ws_client is not None:
            ws_client.send_message("git_command_output", message_obj.to_dict())

        if db is not None:
            # Update with limit using $slice
            db[TASKS_COLLECTION_NAME].update_one(
                {"_id": task_id},
                {
                    "$push": {
                        f"git_commands_outputs.{repository_metadata.get('repositoryId')}": {
                            "$each": [message_obj.to_dict()],
                            "$slice": -25  # Keep only the latest messages
                        }
                    }
                }
            )
    except Exception as e:
        print("exception sending message", e)
        pass