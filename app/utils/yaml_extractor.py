import re
import yaml
import json
from typing import Optional, Dict, Any, Tuple
import logging

logger = logging.getLogger(__name__)

class YAMLExtractor:
    """Utility class for extracting and validating YAML content from manifest responses."""
    
    # Regex patterns for different YAML block formats
    YAML_PATTERNS = [
        r'```project_manifest\.yaml\n(.*?)```',
        r'```yaml\n(.*?)```',
        r'```\n(overview:.*?)```',
        r'```project_manifest\.yml\n(.*?)```',
        r'```yml\n(.*?)```'
    ]
    
    @staticmethod
    def extract_yaml_from_manifest(manifest_content: str) -> Optional[str]:
        """
        Extract YAML content from manifest response.
        
        Args:
            manifest_content: The full manifest content from LLM response
            
        Returns:
            Clean YAML string or None if extraction fails
        """
        if not manifest_content:
            return None
        
        # Try each pattern to find YAML content
        for pattern in YAMLExtractor.YAML_PATTERNS:
            match = re.search(pattern, manifest_content, re.DOTALL | re.IGNORECASE)
            if match:
                yaml_content = match.group(1).strip()
                
                # Validate extracted YAML
                if YAMLExtractor.is_valid_yaml(yaml_content):
                    return yaml_content
                    
        # Fallback: Look for YAML-like content without code blocks
        return YAMLExtractor._extract_yaml_fallback(manifest_content)
    
    @staticmethod
    def _extract_yaml_fallback(content: str) -> Optional[str]:
        """
        Fallback method to extract YAML content when code blocks are missing.
        
        Args:
            content: The manifest content
            
        Returns:
            YAML string or None
        """
        lines = content.split('\n')
        yaml_start = -1
        yaml_end = -1
        
        # Find lines that start with 'overview:' or similar YAML root keys
        for i, line in enumerate(lines):
            if line.strip().startswith('overview:'):
                yaml_start = i
                break
        
        if yaml_start == -1:
            return None
        
        # Find the end of YAML content
        for i in range(yaml_start + 1, len(lines)):
            line = lines[i].strip()
            if line and not (line.startswith(' ') or line.startswith('-') or ':' in line):
                yaml_end = i
                break
        
        if yaml_end == -1:
            yaml_end = len(lines)
        
        yaml_content = '\n'.join(lines[yaml_start:yaml_end])
        
        return yaml_content if YAMLExtractor.is_valid_yaml(yaml_content) else None
    
    @staticmethod
    def is_valid_yaml(yaml_content: str) -> bool:
        """
        Validate if the content is valid YAML.
        
        Args:
            yaml_content: YAML string to validate
            
        Returns:
            True if valid YAML, False otherwise
        """
        try:
            yaml.safe_load(yaml_content)
            return True
        except yaml.YAMLError:
            return False
    
    @staticmethod
    def validate_manifest_structure(yaml_content: str) -> Tuple[bool, Optional[str]]:
        """
        Validate if the YAML has the expected manifest structure.
        
        Args:
            yaml_content: YAML string to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            data = yaml.safe_load(yaml_content)
            
            # Check required top-level keys
            if not isinstance(data, dict):
                return False, "YAML must be a dictionary"
            
            if 'overview' not in data:
                return False, "Missing 'overview' section"
            
            if 'containers' not in data:
                return False, "Missing 'containers' section"
            
            # Validate overview structure
            overview = data['overview']
            required_overview_keys = ['project_name', 'description']
            
            for key in required_overview_keys:
                if key not in overview:
                    return False, f"Missing '{key}' in overview section"
            
            # Validate containers structure
            containers = data['containers']
            if not isinstance(containers, list):
                return False, "Containers must be a list"
            
            if not containers:
                return False, "At least one container is required"
            
            # Validate each container
            required_container_keys = ['container_name', 'description', 'container_type']
            
            for i, container in enumerate(containers):
                if not isinstance(container, dict):
                    return False, f"Container {i} must be a dictionary"
                
                for key in required_container_keys:
                    if key not in container:
                        return False, f"Missing '{key}' in container {i}"
            
            return True, None
            
        except yaml.YAMLError as e:
            return False, f"Invalid YAML syntax: {str(e)}"
        except Exception as e:
            return False, f"Validation error: {str(e)}"

class ManifestResponseFormatter:
    """Format and structure the final manifest response."""
    
    @staticmethod
    def format_final_response(
        yaml_content: str,
        project_id: int,
        include_metadata: bool = True
    ) -> Dict[str, Any]:
        """
        Format the final manifest response.
        
        Args:
            yaml_content: Clean YAML content
            project_id: Project ID
            include_metadata: Whether to include metadata
            
        Returns:
            Formatted response dictionary
        """
        response = {
            'status': 'complete',
            'message': 'Manifest generation completed successfully',
            'project_id': project_id,
            'manifest': {
                'format': 'yaml',
                'content': yaml_content
            }
        }
        
        if include_metadata:
            # Parse YAML to extract metadata
            try:
                parsed_yaml = yaml.safe_load(yaml_content)
                response['metadata'] = {
                    'project_name': parsed_yaml.get('overview', {}).get('project_name', 'Unknown'),
                    'container_count': len(parsed_yaml.get('containers', [])),
                    'third_party_services': parsed_yaml.get('overview', {}).get('third_party_services', []),
                    'frameworks': [
                        container.get('framework', 'Unknown') 
                        for container in parsed_yaml.get('containers', [])
                    ]
                }
            except Exception as e:
                logger.warning(f"Failed to extract metadata: {str(e)}")
                response['metadata'] = {'extraction_error': str(e)}
        
        return response

# Main utility function for integration
def process_manifest_response(
    manifest_content: str,
    project_id: int,
    validate_structure: bool = True
) -> Dict[str, Any]:
    """
    Process and extract YAML from manifest response.
    
    Args:
        manifest_content: Full manifest content from LLM
        project_id: Project ID
        validate_structure: Whether to validate manifest structure
        
    Returns:
        Formatted response with extracted YAML
    """
    # Extract YAML content
    yaml_content = YAMLExtractor.extract_yaml_from_manifest(manifest_content)
    
    if not yaml_content:
        return {
            'status': 'error',
            'message': 'Failed to extract YAML content from manifest',
            'project_id': project_id
        }
    
    # Validate structure if requested
    if validate_structure:
        is_valid, error_msg = YAMLExtractor.validate_manifest_structure(yaml_content)
        if not is_valid:
            return {
                'status': 'error',
                'message': f'Invalid manifest structure: {error_msg}',
                'project_id': project_id,
                'partial_yaml': yaml_content
            }
    
    # Format final response
    return ManifestResponseFormatter.format_final_response(
        yaml_content, 
        project_id, 
        include_metadata=True
    )

# Integration function for the SSE endpoint
def create_final_sse_response(manifest_content: str, project_id: int) -> str:
    """
    Create the final SSE response with extracted YAML.
    
    Args:
        manifest_content: Full manifest content
        project_id: Project ID
        
    Returns:
        SSE formatted response string
    """
    response_data = process_manifest_response(manifest_content, project_id)
    return f"data: {json.dumps(response_data)}\n\n"