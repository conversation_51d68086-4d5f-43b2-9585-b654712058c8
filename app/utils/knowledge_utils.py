from app.routes.code_query import session_manager, KnowledgeSession, KnowledgeCodeBase, wait_for_initialization
from app.core.Settings import settings 
from app.connection.establish_db_connection import get_mongo_db
from app.utils.datetime_utils import generate_timestamp
from app.telemetry.logger_config import get_logger
import os
import uuid
from app.telemetry.logger_config import get_logger,set_task_id

# Create a logger specifically for knowledge utils

failed_session = {
    "session_id": None,
    "status": "failed",
    "exception": "Build incomplete"
}

async def initialize_code_query(repoDetails, project_id, task_id):
    set_task_id(task_id)
    knowledge_logger = get_logger(__name__)

    knowledge_logger.info(f"Starting initialize_code_query for project_id: {project_id}")
    
    try:
        knowledge_logger.info(f"Connecting to MongoDB to fetch project data")
        mongo_handler = get_mongo_db(
                db_name=settings.MONGO_DB_NAME,
                collection_name='project_repositories'
            )
        
        project_data = await mongo_handler.get_one(
            filter={'project_id': int(project_id)},
            db=mongo_handler.db
        )
        
        knowledge_logger.info(f"Retrieved project data: {project_data}")

        session_id = str(uuid.uuid4())
        knowledge_logger.info(f"Generated new session_id: {session_id}")
        
        # Initialize lists to store build paths and incomplete builds
        build_paths = []
        incomplete_builds = []

        # Iterate through repositories and their branches to find matching build IDs
        knowledge_logger.info(f"Processing {len(repoDetails)} repositories")
        
        for repo_index, repo in enumerate(repoDetails):
            knowledge_logger.info(f"Processing repo {repo_index+1}/{len(repoDetails)}: {repo.get('gitUrl', 'Unknown URL')}")
            
            for branch_index, branch in enumerate(repo.get('branches', [])):
                branch_name = branch.get('name')
                build_id = branch.get('buildId')
                build_path = branch.get('path')
                kg_creation_status = branch.get('kg_creation_status')
                
                knowledge_logger.info(f"Processing branch {branch_index+1}/{len(repo.get('branches', []))}: "
                                     f"name={branch_name}, build_id={build_id}, "
                                     f"path={build_path}, kg_status={kg_creation_status}")

                if kg_creation_status != 2:
                    knowledge_logger.warning(f"Skipping incomplete build: build_id={build_id}, status={kg_creation_status}")
                    incomplete_builds.append({
                        'build_id': build_id,
                        'status': kg_creation_status
                    })
                    continue

                if build_path:
                    knowledge_logger.info(f"Pulling latest changes for repo at {build_path}")
                    try:
                        os.chdir(build_path)
                        git_pull_result = os.system(f'git pull --rebase')
                        knowledge_logger.info(f"Git pull result: {git_pull_result}")
                        
                        git_switch_result = os.system(f'git switch {branch_name}')
                        knowledge_logger.info(f"Git switch to {branch_name} result: {git_switch_result}")
                        
                        os.chdir(os.path.dirname(os.path.dirname(build_path)))
                    except Exception as git_error:
                        knowledge_logger.info(f"Error during git operations: {str(git_error)}")
                        raise

                    build_paths.append({
                        'build_id': build_id,
                        'path': build_path,
                        'repo_url': repo['gitUrl'],
                        'branch': branch_name
                    })
                    knowledge_logger.info(f"Added build path: {build_id}")

        # Check if there are any incomplete builds
        if incomplete_builds:
            knowledge_logger.info(f"Found {len(incomplete_builds)} incomplete builds, aborting initialization")
            return failed_session

        # Check if we found any valid build paths
        if not build_paths:
            knowledge_logger.info("No valid build paths found, aborting initialization")
            return failed_session

        knowledge_logger.info(f"Creating codebase paths for {len(build_paths)} builds")
        codebase_paths = [
            KnowledgeCodeBase(
                build_path["path"], f"{build_path['repo_url']}:{build_path['branch']}",
            )
            for build_path in build_paths
        ]
        
        knowledge_logger.info(f"Creating new KnowledgeSession with ID: {session_id}")
        session = KnowledgeSession(session_id, codebase_paths)
        session_manager.sessions[session_id] = session

        knowledge_logger.info(f"Getting or creating knowledge manager instance for session: {session_id}")
        instance = await session_manager.get_or_create_instance(session_id)
        instance = session_manager.get_instance_by_session(session_id)

        # Session initialization
        if not instance.initialization_started:
            knowledge_logger.info(f"Starting initialization for session: {session_id}")
            await session_manager.initialize_instance(codebase_paths, session_id, instance)
            knowledge_logger.info(f"Waiting for initialization to complete...")
            await wait_for_initialization(instance=instance)
            knowledge_logger.info(f"Initialization completed for session: {session_id}")
        else:
            knowledge_logger.info(f"Instance already initialized for session: {session_id}")
            
        # Connect to MongoDB
        knowledge_logger.info(f"Recording session in MongoDB")
        code_query_session_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='code_query_session'
        )

        # Insert the session record
        session_data = {
            "session_id": session_id,
            "start_time": generate_timestamp(),
            "session_name": str(session_id),
            "description": f"Auto config-{session_id}"
        }
        knowledge_logger.info(f"Inserting session data: {session_data}")
        
        inserted_result = await code_query_session_handler.insert(
            session_data,
            db=code_query_session_handler.db
        )
        knowledge_logger.info(f"Session record inserted successfully: {inserted_result}")
        
        await session.update_last_accessed()
        knowledge_logger.info(f"Updated last accessed timestamp for session: {session_id}")

        result = {
            "status": "success",
            "session_id": session_id,
            "project_id": project_id,
            "build_paths": build_paths
        }
        knowledge_logger.info(f"initialize_code_query completed successfully: {result}")
        return result
        
    except Exception as e:
        knowledge_logger.info(f"Error in initialize_code_query: {str(e)}", exc_info=True)
        failed_response = {
            "session_id": None,
            "status": "failed",
            "exception": str(e)
        }
        return failed_response