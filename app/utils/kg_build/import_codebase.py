import os
from app.core.Settings import settings
from github import Github
from datetime import datetime
from fastapi import HTTPException
import shutil
import logging
from app.connection.tenant_middleware import get_tenant_id
from app.utils.datetime_utils import generate_timestamp
def get_latest_commit_hash(git_url, branch_name, github_token=None):
    """Get latest commit hash from GitHub repository"""
    try:
        # Extract owner and repo from git URL
        url_parts = git_url.split('/')
        owner = url_parts[-2]
        repo_name = url_parts[-1].replace('.git', '')
        
        # Initialize Github with or without token
        g = Github(github_token) if github_token else Github()
        
        # Get repository and latest commit
        repo = g.get_repo(f"{owner}/{repo_name}")
        latest_commit = repo.get_branch(branch_name).commit
        
        return latest_commit.sha
    except Exception as e:
        logging.error(f"Failed to get latest commit hash: {str(e)}")
        return None
    finally:
        g.close()