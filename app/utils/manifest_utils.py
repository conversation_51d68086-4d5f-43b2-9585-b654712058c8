from app.connection.establish_db_connection import get_mongo_db, get_node_db
from app.utils.code_generation_utils import yaml_to_json, json_to_yaml
from app.utils.batch_utils import get_container_type
from code_generation_core_agent.project_schemas import ContainerType
from app.core.constants import REPOSITORIES_COLLECTION
import json
from app.core.Settings import settings



def _get_container_dependancy(container_type, container_name):
    if container_type.lower() in 'backend':
        return [container_name+'_'+'database']
    if container_type.lower() in 'frontend':
        return [container_name+'_'+'backend']
    if container_type.lower() in 'database':
        return [container_name+'_'+'backend']
    return []
    
def _def_interfaces(container_type):
    if container_type.lower() in 'backend':
        return "API endpoints based of the project description"
    if container_type.lower() in 'frontend':
        return "API requests to the backend container"
    if container_type.lower() in 'database':
        return "Database Accessible by the backend for data persistence"
    return ""
    
async def get_project_manifest_for_generation(project_id: int, container_ids =[], node_db=None):
    
    project_node = await node_db.get_node_by_id(project_id)
    project_properties = project_node.get("properties", {})
    print(f"Project Properties: {project_properties}")
    project_schema = {
            "overview": {
                "project_name": project_properties.get("Title", ""),
                "description": project_properties.get("Description", ""),
                "third_party_services": []
            },
            "containers": []
        }
    
    ports_to_map = {
        "frontend": settings.FRONTEND_PORT,
        "backend": settings.BACKEND_PORT,
        "database": settings.DATABASE_PORT,
    }
    
    def get_container_port(container):
        container_type = get_container_type(container)
        if container_type == ContainerType.FRONTEND.value:
            return settings.FRONTEND_PORT
        elif container_type == ContainerType.BACKEND.value:
            return settings.BACKEND_PORT
        elif container_type == ContainerType.DATABASE.value:
            return settings.DATABASE_PORT
        elif container_type == ContainerType.MOBILE.value:
            return None
        
        return ports_to_map.pop(ContainerType.FRONTEND.value, 0)

        
    for container_id in container_ids:
        container_id = int(container_id)
        container_node = await node_db.get_node_by_id(container_id)
        container_properties = container_node.get("properties", {})
        container_name = container_properties.get("Title").replace(' ','').replace('-','_')
        container_type = get_container_type(container_properties)
        container = {
            "container_name": container_name,
            "description": container_properties.get("Description", ""),
            "interfaces": _def_interfaces(container_type),
            "container_type": container_type,
            "dependent_containers": _get_container_dependancy(get_container_type(container_properties), container_name),
            "workspace": "",
            "container_root":  "",
            "port": get_container_port(container_properties),
            "framework": container_properties.get("framework", ""),
            "type": container_properties.get("type", ""),
            "buildCommand": container_properties.get("buildCommand", ""),
            "startCommand": container_properties.get("startCommand", ""),
            "installCommand": container_properties.get("installCommand", ""),
            "lintCommand": container_properties.get("lintCommand", ""),
            "working_dir": container_properties.get("working_dir", ""),
            "container_details": {'features': container_properties.get("UserInteractions","")},
            "lintConfig": container_properties.get("lintConfig", ""),
            "routes": container_properties.get("routes", []),
            "apiSpec": container_properties.get("apiSpec", ""),
            "auth": container_properties.get("auth"),
            "schema": container_properties.get("schema", ""),
            "migrations": container_properties.get("migrations", ""),
            "seed": container_properties.get("seed", ""),
            "env": container_properties.get("env", {}),
            "private": container_properties.get("private", {})
            }
        
        if container['container_type'] == 'mobile':
            container.pop('port', None)
        project_schema['containers'].append(container)

    return json.dumps(project_schema)

async def get_project_manifest_for_maintenance(project_id: int, repository_ids = [], all_repositories=False, node_db=None):
    mongo_db = get_mongo_db().db
    
    if not node_db:
        node_db = get_node_db()
    
    project = await node_db.get_node_by_id(project_id)
    project_details = project.get("properties")
    
    project_repos = mongo_db[REPOSITORIES_COLLECTION].find_one({
            "project_id": project_id
        }
        )
    project_manifest = project_repos.get("project_manifest", "")
    
    if project_manifest and not repository_ids:
        return project_manifest
    
    if repository_ids or all_repositories:
        container_manifests = []
        third_party_services = []
        for repository in project_repos.get("repositories"):
            if (repository.get("repo_id") in repository_ids) or all_repositories:
                current_manifest = repository.get("project_manifest")
                if current_manifest:
                    container_manifest = yaml_to_json(current_manifest)
                    containers = container_manifest.get("containers", [])
                    third_party_services.extend(container_manifest.get("third_party_services", []))
                    container_manifests.extend(containers)
                    
    if not container_manifests:
        return ""
    final_manifest = {
        "overview" : {
            "project_name" : project_details.get("Title", ""),
            "description" : project_details.get("Description", ""),
            "third_party_services" : list(set(third_party_services))
        },
        "containers" : container_manifests
    }
    
    return json_to_yaml(json.dumps(final_manifest))

    