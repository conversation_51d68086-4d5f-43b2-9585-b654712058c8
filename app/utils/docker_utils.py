
import subprocess
import logging
from app.core.custom_docker_executor import get_container_ports
import json
from pathlib import Path


def update_directory_permissions():
    """Update permissions for critical directories"""
    directories = ["/home/<USER>", "/home/<USER>/workspace"]
    
    for directory in directories:
        try:
            if Path(directory).exists():
                subprocess.run(
                    ["chmod", "-R", "777", directory],
                    check=True,
                    capture_output=True,
                    text=True,
                    timeout=60
                )
                logging.info(f"Successfully updated permissions for {directory}")
            else:
                logging.warning(f"Directory {directory} does not exist, skipping")
        except subprocess.CalledProcessError as e:
            logging.error(f"Failed to update permissions for {directory}: {e.stderr}")
        except subprocess.TimeoutExpired:
            logging.error(f"Permission update timed out for {directory}")
        except Exception as e:
            logging.error(f"Error updating permissions for {directory}: {str(e)}")

def check_docker_container_status(container_name="kavia_default_container_custom"):
    """
    Check if Docker container is running.
    
    Args:
        container_name: Name of the container to check
        
    Returns:
        bool: True if container is running, False otherwise
    """
    try:
        result = subprocess.run(
            ["docker", "ps", "--filter", f"name={container_name}", "--format", "{{.Names}}"],
            capture_output=True, text=True, check=False
        )
        
        is_running = container_name in result.stdout
        logging.info(f"Docker container {container_name} status: {'running' if is_running else 'not running'}")
        return is_running
        
    except Exception as e:
        logging.error(f"Error checking Docker container status: {str(e)}")
        return False

def _process_container_ports(raw_ports, logger: logging):
    """
    Process container ports from various formats to Docker-compatible format.
    
    Args:
        raw_ports: Can be dict, list, or string from get_container_ports
        logger: Logger instance
        
    Returns:
        list: List of port mappings in format ["host_port:container_port", ...]
    """
    default_ports = ["3001:3000", "8081:8080", "8089:8088", "5901:5900", "5002:5001"]
    
    try:
        # Handle different return types from get_container_ports
        if isinstance(raw_ports, dict):
            # Format: {"3000/tcp": 3001, "8080/tcp": 8081}
            port_mappings = []
            for container_port_spec, host_port in raw_ports.items():
                # Extract port number from "3000/tcp" format or use as-is if just number
                if isinstance(container_port_spec, str) and '/' in container_port_spec:
                    container_port = container_port_spec.split('/')[0]
                else:
                    container_port = str(container_port_spec)
                
                port_mappings.append(f"{host_port}:{container_port}")
            
            logger.info(f"Converted dict ports to mappings: {port_mappings}")
            return port_mappings
            
        elif isinstance(raw_ports, list):
            # Check if it's already in the right format ["3001:3000", "8081:8080"]
            if all(isinstance(port, str) and ':' in port for port in raw_ports):
                logger.info("Ports already in correct format")
                return raw_ports
            
            # Handle list of dicts or other formats
            port_mappings = []
            for port_item in raw_ports:
                if isinstance(port_item, dict):
                    # Handle list of dict items
                    for container_port_spec, host_port in port_item.items():
                        if isinstance(container_port_spec, str) and '/' in container_port_spec:
                            container_port = container_port_spec.split('/')[0]
                        else:
                            container_port = str(container_port_spec)
                        port_mappings.append(f"{host_port}:{container_port}")
                elif isinstance(port_item, str):
                    # Handle string format like "3001:3000"
                    if ':' in port_item:
                        port_mappings.append(port_item)
                    else:
                        logger.warning(f"Invalid port format: {port_item}")
            
            return port_mappings if port_mappings else default_ports
            
        elif isinstance(raw_ports, str):
            # Handle JSON string
            try:
                parsed_ports = json.loads(raw_ports)
                return _process_container_ports(parsed_ports, logger)
            except json.JSONDecodeError:
                logger.error(f"Failed to parse ports JSON string: {raw_ports}")
                return default_ports
                
        else:
            logger.warning(f"Unexpected ports format: {type(raw_ports)} - {raw_ports}")
            return default_ports
            
    except Exception as e:
        logger.error(f"Error processing container ports: {str(e)}")
        logger.info(f"Using default ports: {default_ports}")
        return default_ports

def ensure_docker_container_running(container_name="kavia_default_container_custom", 
                                  image_name="kavia_default_container_image",
                                  mount_path="/home/<USER>/workspace"):
    """
    Ensure Docker container is running. Start it if not running.
    
    Args:
        container_name: Name of the container
        image_name: Docker image name
        mount_path: Path to mount into container
        
    Returns:
        bool: True if container is running after check/start, False otherwise
    """
    try:
        # Check if already running
        if check_docker_container_status(container_name):
            return True
        
        logging.warning(f"Docker container {container_name} is not running, attempting to start...")
        
        # Get ports from config
        logger = logging.getLogger(__name__)
        raw_ports = get_container_ports(logger=logger)
        ports = _process_container_ports(raw_ports, logger)
        
        logging.info(f"Starting Docker container with ports: {ports}")
        
        # Clean up any stopped instance first
        subprocess.run(
            ["docker", "rm", "-f", container_name],
            capture_output=True, text=True, check=False
        )
        
        # Build the docker run command - CORRECTED ORDER
        run_cmd = [
            "docker", "run", "-ti", "--rm", "-d"
        ]
        
        # Add port mappings FIRST
        for port in ports:
            run_cmd.extend(["-p", port])
        
        # Add environment variables and volume
        run_cmd.extend([
            "-e", "LOCAL_UID=1000",
            "-e", "LOCAL_GID=1000",
            "-v", f"{mount_path}:{mount_path}:rw,shared",
            "--name", container_name,
            image_name,  # Image name goes here
            "bash", "-c", "sudo chown -R kavia:kavia /home/<USER>/home/<USER>/workspace && sudo chmod -R 777 /home/<USER>/bin/bash"
        ])
        
        # Log the command for debugging
        logging.info(f"Docker start command: {' '.join(run_cmd)}")
        
        # Try to start the container
        result = subprocess.run(run_cmd, capture_output=True, text=True, check=False)
        
        if result.returncode == 0:
            logging.info(f"Successfully started Docker container {container_name}")
            return True
        else:
            logging.error(f"Failed to start Docker container: {result.stderr}")
            
            # If port binding fails, try without ports as fallback
            logging.info("Attempting to start without port forwarding as fallback...")
            
            run_cmd_no_ports = [
                "docker", "run", "-d", "--restart", "always",
                "-e", "LOCAL_UID=1000",
                "-e", "LOCAL_GID=1000",
                "-v", f"{mount_path}:{mount_path}:rw,shared",
                "--name", container_name,
                image_name
            ]
            
            fallback_result = subprocess.run(run_cmd_no_ports, capture_output=True, text=True, check=False)
            
            if fallback_result.returncode == 0:
                logging.warning(f"Started Docker container {container_name} without port forwarding")
                return True
            else:
                logging.error(f"Failed to start Docker container even without ports: {fallback_result.stderr}")
                return False
                
    except Exception as e:
        logging.error(f"Error ensuring Docker container is running: {str(e)}")
        return False