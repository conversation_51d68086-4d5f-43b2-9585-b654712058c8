from typing import Dict, Optional, List
from pymongo import MongoClient
from datetime import datetime
from app.core.Settings import settings
from app.connection.tenant_middleware import get_tenant_based_db_name


class MongoPermissionManager:
    def __init__(self):
        """
        Initialize MongoDB Permission Manager
        
        Args:
            mongodb_uri: MongoDB connection URI
            database_name: Name of the database
        """
        self.client = MongoClient(settings.MONGO_CONNECTION_URI)
        db_name = get_tenant_based_db_name(settings.MONGO_DB_NAME)
        print("MongoPermissionManager initialized with db_name:", db_name)
        self.db = self.client[db_name]
        self.permissions = self.db.permissions

    def create_permission(self, permission_id: str, permissions: Dict) -> bool:
        """
        Create a new permission entry
        
        Args:
            permission_id: Combined tenant_id and precedence
            permissions: Dictionary of permissions
            
        Returns:
            bool: True if creation successful
        """
        try:
            document = {
                "_id": permission_id,
                "permissions": permissions,
                "createdAt": datetime.utcnow(),
                "updatedAt": datetime.utcnow()
            }
            
            self.permissions.insert_one(document)
            return True
            
        except Exception as e:
            raise Exception(f"Failed to create permission: {str(e)}")

    def get_permission(self, permission_id: str) -> Optional[Dict]:
        """
        Get permissions by permission_id
        
        Args:
            permission_id: Combined tenant_id and precedence
            
        Returns:
            Dict: Permission document or None if not found
        """
        try:
            return self.permissions.find_one({"_id": permission_id})
            
        except Exception as e:
            raise Exception(f"Failed to get permission: {str(e)}")

    def get_permissions(self, permission_ids: List[str]) -> Dict:
        """
        Get union of permissions from multiple permission IDs
        
        Args:
            permission_ids: List of permission IDs
            
        Returns:
            Dict: Combined permissions where any True value takes precedence
            
        Example:
            Input permission documents:
            doc1: {
                "users": {
                    "create": True,
                    "read": False
                }
            }
            doc2: {
                "users": {
                    "read": True,
                    "update": False
                }
            }
            
            Result: {
                "users": {
                    "create": True,
                    "read": True,
                    "update": False
                }
            }
        """
        try:
            # Get all permission documents
            documents = list(self.permissions.find({"_id": {"$in": permission_ids}}))
            
            if not documents:
                return {"permissions": {}}
            
            combined_permissions = {}
            
            # Iterate through each document
            for doc in documents:
                perms = doc.get("permissions", {})
                
                # Iterate through each permission type (e.g., "users", "reports")
                for perm_type, actions in perms.items():
                    if perm_type not in combined_permissions:
                        combined_permissions[perm_type] = {}
                    
                    # Iterate through each action (e.g., "create", "read")
                    for action, value in actions.items():
                        # If action already exists, OR it with new value
                        # If action doesn't exist, set the new value
                        current_value = combined_permissions[perm_type].get(action, False)
                        combined_permissions[perm_type][action] = current_value or value

            return {"permissions": combined_permissions}
            
        except Exception as e:
            raise Exception(f"Failed to get permissions: {str(e)}")

    def update_permissions(self, permission_id: str, new_permissions: Dict) -> bool:
        """
        Update permissions for a permission_id
        
        Args:
            permission_id: Combined tenant_id and precedence
            new_permissions: New permissions dictionary
            
        Returns:
            bool: True if update successful
        """
        try:
            result = self.permissions.update_one(
                {"_id": permission_id},
                {
                    "$set": {
                        "permissions": new_permissions,
                        "updatedAt": datetime.utcnow()
                    }
                }
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            raise Exception(f"Failed to update permissions: {str(e)}")

    def delete_permission(self, permission_id: str) -> bool:
        """
        Delete a permission document
        
        Args:
            permission_id: Combined tenant_id and precedence
            
        Returns:
            bool: True if deletion successful
        """
        try:
            result = self.permissions.delete_one({"_id": permission_id})
            return result.deleted_count > 0
            
        except Exception as e:
            raise Exception(f"Failed to delete permission: {str(e)}")
