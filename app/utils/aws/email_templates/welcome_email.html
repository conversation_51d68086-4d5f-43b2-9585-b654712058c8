<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Welcome to KAVIA AI</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
        
        /* Mobile responsive styles */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
                max-width: 100% !important;
            }
            .content-padding {
                padding: 20px !important;
            }
            .header-padding {
                padding: 20px 20px 16px 20px !important;
            }
            .cta-padding {
                padding: 16px 20px 20px 20px !important;
            }
            .contact-padding {
                padding: 20px 20px 0 20px !important;
            }
            .footer-padding {
                padding: 20px !important;
            }
            .outer-padding {
                padding: 20px 15px !important;
            }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #F4F3F3; font-family: 'Inter', Arial, Helvetica, sans-serif;">
    <!-- Main Container -->
    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #F4F3F3;">
        <tr>
            <td align="center" class="outer-padding" style="padding: 40px 30px;">
                <!-- Email Content Container -->
                <table border="0" cellpadding="0" cellspacing="0" width="600" class="email-container" style="background-color: #ffffff; max-width: 600px; border-radius: 6px; overflow: hidden;">
                    
                    <!-- Header Section -->
                    <tr>
                        <td align="center" class="header-padding" style="padding: 24px 32px 20px 32px;">
                            <!-- Logo -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <td align="center" style="padding-bottom: 16px;">
                                        <table border="0" cellpadding="0" cellspacing="0">
                                            <tr>
                                                <td style="font-size: 24px;  font-weight: bold; font-family: 'Inter', Arial, sans-serif;">
                                                    <span style="vertical-align: middle; display: inline-block; width: 24px; height: 24px;">
                                                        <!-- Paste your SVG code below, replacing the example -->
                                                        <svg width="21" height="24" viewBox="0 0 21 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <rect width="21" height="24" fill="#ffffff"/>
                                                            <path d="M19.2263 15.224C17.0527 14.3496 14.9359 13.3472 12.8639 12.26C12.7047 12.1752 12.5463 12.0896 12.3879 12.0024C14.5999 10.7928 16.8887 9.7256 19.2263 8.776C20.2639 8.3872 20.6359 7.0224 19.9263 6.1648C19.3471 5.4408 18.2911 5.3224 17.5671 5.9016C15.7231 7.3472 13.7967 8.6792 11.8191 9.9296C11.6663 10.0256 11.5127 10.1192 11.3583 10.2128C11.4175 7.692 11.6375 5.1768 11.9831 2.6776C12.1655 1.5848 11.1687 0.579999 10.0711 0.765599C9.18632 0.899999 8.56792 1.7024 8.65192 2.5824C9.27832 5.3616 9.63832 8.228 9.69592 11.1584C9.69832 11.2784 9.70072 11.3984 9.70232 11.5184C9.70392 11.6552 9.70472 11.7912 9.70552 11.928C9.70552 11.952 9.70552 11.976 9.70552 12C9.70552 12.0224 9.70552 12.044 9.70552 12.0664C9.70552 12.068 9.70552 12.0696 9.70552 12.0712C9.70552 12.2176 9.70392 12.364 9.70232 12.5104C9.70232 12.5312 9.70232 12.5528 9.70152 12.5736C9.70152 12.6272 9.69992 12.68 9.69912 12.7336C9.64792 15.7016 9.28712 18.604 8.65272 21.4176C8.56952 22.2976 9.18712 23.1 10.0719 23.2344C11.1695 23.42 12.1663 22.416 11.9839 21.3224C11.6383 18.8248 11.4183 16.3112 11.3591 13.792C13.5111 15.1024 15.5783 16.5504 17.5679 18.0984C18.4231 18.8032 19.7911 18.4416 20.1791 17.3984C20.5167 16.5352 20.0911 15.5608 19.2271 15.2232L19.2263 15.224Z" fill="#F26A1B"/>
                                                            <path d="M4.93202 13.6872C4.64322 13.8224 4.35362 13.9552 4.06242 14.0856C3.18722 14.4784 2.31122 14.8688 1.41922 15.2232C0.385622 15.6136 0.0152218 16.9728 0.720822 17.8296C1.29762 18.5552 2.35362 18.6752 3.07922 18.0976C3.33042 17.8976 3.58482 17.704 3.84082 17.5104C4.09762 17.3192 4.35282 17.1272 4.61122 16.94C5.12642 16.5632 5.64322 16.188 6.16642 15.8232C7.37922 14.9304 6.32402 13.0808 4.93202 13.6856V13.6872Z" fill="#F26A1B"/>
                                                            <path d="M6.16557 8.1752C5.90397 7.9928 5.64397 7.808 5.38557 7.6216C4.60797 7.06 3.83197 6.4968 3.07837 5.9016C2.22397 5.2008 0.860765 5.5608 0.471965 6.6C0.131965 7.4624 0.555966 8.4368 1.41837 8.7768C1.71677 8.8944 2.01277 9.0184 2.30717 9.1424C2.60077 9.2688 2.89517 9.3944 3.18637 9.5248C3.77037 9.7824 4.35357 10.0424 4.93117 10.3136C6.31037 10.9176 7.38557 9.0792 6.16557 8.176V8.1752Z" fill="#F26A1B"/>
                                                        </svg>
                                                        
                                                      </span><span style="position: relative; top: 2px; font-weight: 100;">KAVIA AI</span>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="center">
                                        <h1 style="margin: 0; margin-top: 10px;font-size: 24px; font-family: 'Inter', Arial, sans-serif; font-weight: 600; color: #000000; line-height: 33.6px;">Welcome to KAVIA AI</h1>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Dark Card with Gradient -->
                    <tr>
                        <td style="padding: 0 32px 13px 32px;">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #231F20; border-radius: 12px; overflow: hidden;">
                                <tr>
                                    <td height="226" style="background: #231F20; position: relative;">
                                        <!--[if gte mso 9]>
                                        <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width:536px;height:226px;">
                                            <v:fill type="gradient" color="#231F20" color2="#E15E0D" angle="180" />
                                            <v:textbox inset="0,0,0,0">
                                        <![endif]-->
                                        <div style="height: 226px; background: linear-gradient(to bottom, #231F20 0%, #231F20 60%, #E15E0D 150%);">
                                            &nbsp;
                                        </div>
                                        <!--[if gte mso 9]>
                                            </v:textbox>
                                        </v:rect>
                                        <![endif]-->
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Main Content -->
                    <tr>
                        <td class="content-padding" style="padding: 32px;">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                <!-- Greeting -->
                                <tr>
                                    <td style="padding-bottom: 12px;">
                                        <p style="margin: 0; font-size: 15px; line-height: 21px; color: #191616; font-family: 'Inter', Arial, sans-serif;">
                                            Dear <strong>{{ user_fullname }}</strong>,
                                        </p>
                                    </td>
                                </tr>
                                
                                <!-- Introduction -->
                                <tr>
                                    <td style="padding-bottom: 12px;">
                                        <p style="margin: 0; font-size: 15px; line-height: 21px; color: #191616; font-family: 'Inter', Arial, sans-serif;">
                                            We're excited to have you onboard as part of our <strong>Beta Launch</strong>.
                                        </p>
                                    </td>
                                </tr>
                                
                                
                                <!-- Platform Description -->
                                <tr>
                                    <td style="padding-bottom: 30px;">
                                        <p style="margin: 0; font-size: 15px; line-height: 21px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 600;">
                                            KAVIA is a new AI-powered software development platform, built for experienced developers and beginners.
                                        </p>
                                    </td>
                                </tr>
                                
                                <!-- Features Header -->
                                <tr>
                                    <td style="padding-bottom: 11px;">
                                        <p style="margin: 0; font-size: 15px; line-height: 21px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 600;">
                                            What you can do with KAVIA:
                                        </p>
                                    </td>
                                </tr>
                                
                                <!-- Features List -->
                                <tr>
                                    <td>
                                        <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                            <!-- Feature 1 -->
                                            <tr>
                                                <td style="padding: 10px 0; border-bottom: 1px solid #F4F3F3;">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                                        <tr>
                                                            <td width="24" valign="middle">
                                                                <span style="color: #4997B3; font-size: 18px;">✓</span>
                                                            </td>
                                                            <td style="padding-left: 8px;">
                                                                <p style="margin: 0; font-size: 14px; line-height: 19.6px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 500;">Generate & deploy web apps & websites from prompts</p>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <!-- Feature 2 -->
                                            <tr>
                                                <td style="padding: 10px 0; border-bottom: 1px solid #F4F3F3;">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                                        <tr>
                                                            <td width="24" valign="middle">
                                                                <span style="color: #4997B3; font-size: 18px;">✓</span>
                                                            </td>
                                                            <td style="padding-left: 8px;">
                                                                <p style="margin: 0; font-size: 14px; line-height: 19.6px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 500;">Build backends, mobile apps and full stack applications</p>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <!-- Feature 3 -->
                                            <tr>
                                                <td style="padding: 10px 0; border-bottom: 1px solid #F4F3F3;">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                                        <tr>
                                                            <td width="24" valign="middle">
                                                                <span style="color: #4997B3; font-size: 18px;">✓</span>
                                                            </td>
                                                            <td style="padding-left: 8px;">
                                                                <p style="margin: 0; font-size: 14px; line-height: 19.6px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 500;">Connect to Supabase and public cloud hosted databases</p>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <!-- Feature 4 -->
                                            <tr>
                                                <td style="padding: 10px 0; border-bottom: 1px solid #F4F3F3;">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                                        <tr>
                                                            <td width="24" valign="middle">
                                                                <span style="color: #4997B3; font-size: 18px;">✓</span>
                                                            </td>
                                                            <td style="padding-left: 8px;">
                                                                <p style="margin: 0; font-size: 14px; line-height: 19.6px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 500;">Import existing code to identify gaps, add features and fix bugs</p>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <!-- Feature 5 -->
                                            <tr>
                                                <td style="padding: 10px 0 0 0;">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                                        <tr>
                                                            <td width="24" valign="middle">
                                                                <span style="color: #4997B3; font-size: 18px;">✓</span>
                                                            </td>
                                                            <td style="padding-left: 8px;">
                                                                <p style="margin: 0; font-size: 14px; line-height: 19.6px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 500;">Built for beginners and experienced developers</p>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- CTA Section -->
                    <tr>
                        <td align="center" class="cta-padding" style="padding: 16px 32px 32px 32px;">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                <!-- CTA Button -->
                                <tr>
                                    <td align="center" style="padding-bottom: 24px;">
                                        <table border="0" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td align="center" style="border-radius: 6px;" bgcolor="#F26A1B">
                                                    <a href="https://kavia.ai/startwithkavia" target="_blank" style="font-size: 15px; font-family: 'Inter', Arial, sans-serif; color: #ffffff; text-decoration: none; border-radius: 6px; padding: 12px 48px; border: 1px solid #F26A1B; display: inline-block; font-weight: 600; line-height: 20px;">
                                                        Get started with KAVIA AI <span><svg width="6" height="11" viewBox="0 0 6 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M0.765035 10.7192L-0.00537109 9.9488L4.46453 5.4789L-0.00537109 1.00899L0.765035 0.238586L6.00535 5.4789L0.765035 10.7192Z" fill="#F4F3F3"/>
                                                            </svg>
                                                            </span>
                                                    </a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                
                                <!-- Tutorials Link -->
                                <tr>
                                    <td align="center" style="padding-bottom: 16px;">
                                        <p style="margin: 0; font-size: 15px; line-height: 21px; color: #191616; font-family: 'Inter', Arial, sans-serif;">
                                            If you need help getting started, take a look at our <a href="https://kavia.ai/documentation/home" target="_blank" style="color: #191616; font-weight: 500;">Guide Document</a>.
                                        </p>
                                    </td>
                                </tr>
                                
                                <!-- Feedback Request -->
                                <tr>
                                    <td align="center">
                                        <p style="margin: 0; font-size: 15px; line-height: 21px; color: #191616; font-family: 'Inter', Arial, sans-serif;">
                                            <strong>We'd love your feedback.</strong> Please try out the platform and share your thoughts via this <a href="https://forms.gle/jyjby6wAsiYsnAs17" style="color: #191616; font-weight: 600;">Feedback</a>.
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Contact Section -->
                    <tr>
                        <td class="contact-padding" style="padding: 32px 32px 0 32px; border-top: 1px solid #F4F3F3;">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <td style="padding-bottom: 18px;">
                                        <p style="margin: 0; font-size: 15px; line-height: 21px; color: #191616; font-family: 'Inter', Arial, sans-serif;">
                                            If you have any questions or need help, just email us at:<br/>
                                            <strong><EMAIL></strong>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Footer Section -->
                    <tr>
                        <td align="center" class="footer-padding" style="padding: 32px;">
                            <table border="0" cellpadding="0" cellspacing="0">
                                <!-- Thank You -->
                                <tr>
                                    <td align="center" style="padding-bottom: 7px;">
                                        <p style="margin: 0; font-size: 16px; line-height: 22.4px; color: #191616; font-family: 'Inter', Arial, sans-serif;">Thank You!</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="center" style="padding-bottom: 20px;">
                                        <p style="margin: 0; font-size: 20px; line-height: 28px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 600;">KAVIA AI Team</p>
                                    </td>
                                </tr>
                                
                                <!-- Footer Logo -->
                                <tr>
                                    <td align="center" style="padding-bottom: 16px;">
                                        <span style="font-size: 18px; font-weight: bold; font-family: 'Inter', Arial, sans-serif;">
  <span style="vertical-align: middle; display: inline-block; width: 18px; height: 18px;">
    <svg width="18" height="18" viewBox="0 0 21 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="21" height="24" fill="#ffffff"/>
      <path d="M19.2263 15.224C17.0527 14.3496 14.9359 13.3472 12.8639 12.26C12.7047 12.1752 12.5463 12.0896 12.3879 12.0024C14.5999 10.7928 16.8887 9.7256 19.2263 8.776C20.2639 8.3872 20.6359 7.0224 19.9263 6.1648C19.3471 5.4408 18.2911 5.3224 17.5671 5.9016C15.7231 7.3472 13.7967 8.6792 11.8191 9.9296C11.6663 10.0256 11.5127 10.1192 11.3583 10.2128C11.4175 7.692 11.6375 5.1768 11.9831 2.6776C12.1655 1.5848 11.1687 0.579999 10.0711 0.765599C9.18632 0.899999 8.56792 1.7024 8.65192 2.5824C9.27832 5.3616 9.63832 8.228 9.69592 11.1584C9.69832 11.2784 9.70072 11.3984 9.70232 11.5184C9.70392 11.6552 9.70472 11.7912 9.70552 11.928C9.70552 11.952 9.70552 11.976 9.70552 12C9.70552 12.0224 9.70552 12.044 9.70552 12.0664C9.70552 12.068 9.70552 12.0696 9.70552 12.0712C9.70552 12.2176 9.70392 12.364 9.70232 12.5104C9.70232 12.5312 9.70232 12.5528 9.70152 12.5736C9.70152 12.6272 9.69992 12.68 9.69912 12.7336C9.64792 15.7016 9.28712 18.604 8.65272 21.4176C8.56952 22.2976 9.18712 23.1 10.0719 23.2344C11.1695 23.42 12.1663 22.416 11.9839 21.3224C11.6383 18.8248 11.4183 16.3112 11.3591 13.792C13.5111 15.1024 15.5783 16.5504 17.5679 18.0984C18.4231 18.8032 19.7911 18.4416 20.1791 17.3984C20.5167 16.5352 20.0911 15.5608 19.2271 15.2232L19.2263 15.224Z" fill="#F26A1B"/>
      <path d="M4.93202 13.6872C4.64322 13.8224 4.35362 13.9552 4.06242 14.0856C3.18722 14.4784 2.31122 14.8688 1.41922 15.2232C0.385622 15.6136 0.0152218 16.9728 0.720822 17.8296C1.29762 18.5552 2.35362 18.6752 3.07922 18.0976C3.33042 17.8976 3.58482 17.704 3.84082 17.5104C4.09762 17.3192 4.35282 17.1272 4.61122 16.94C5.12642 16.5632 5.64322 16.188 6.16642 15.8232C7.37922 14.9304 6.32402 13.0808 4.93202 13.6856V13.6872Z" fill="#F26A1B"/>
      <path d="M6.16557 8.1752C5.90397 7.9928 5.64397 7.808 5.38557 7.6216C4.60797 7.06 3.83197 6.4968 3.07837 5.9016C2.22397 5.2008 0.860765 5.5608 0.471965 6.6C0.131965 7.4624 0.555966 8.4368 1.41837 8.7768C1.71677 8.8944 2.01277 9.0184 2.30717 9.1424C2.60077 9.2688 2.89517 9.3944 3.18637 9.5248C3.77037 9.7824 4.35357 10.0424 4.93117 10.3136C6.31037 10.9176 7.38557 9.0792 6.16557 8.176V8.1752Z" fill="#F26A1B"/>
    </svg>
  </span>
  <span style="position: relative; top: 2px; font-weight: 100;">KAVIA AI</span>
</span>
                                    </td>
                                </tr>
                                
                                <!-- Social Icons -->
                                <!-- <tr>
                                    <td align="center" style="padding-bottom: 12px;">
                                        <table border="0" cellpadding="0" cellspacing="0">
                                            <tr>
                                                <td style="padding: 0 8px;">
                                                    <a href="#" style="color: #484546; text-decoration: none; font-size: 18px;">in</a>
                                                </td>
                                                <td style="padding: 0 8px;">
                                                    <a href="#" style="color: #767172; text-decoration: none; font-size: 18px;">𝕏</a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr> -->
                                
                                <!-- Copyright and Links -->
                                <tr>
                                    <td align="center">
                                        <p style="margin: 0; font-size: 12px; line-height: 16.8px; color: #555555; font-family: Arial, sans-serif;">
                                            © 2025 KAVIA | <a href="https://kavia.ai/privacy" target="_blank" style="color: #555555;">Privacy Policy</a> | <a href="#" style="color: #555555;">Unsubscribe</a>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
                <!-- End Email Content Container -->
            </td>
        </tr>
    </table>
    <!-- End Main Container -->
</body>
</html>