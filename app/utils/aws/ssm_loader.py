import boto3
import json

ssm_dev_param_name = f'/kavia-backend/kube/dev/config'
ssm_qa_param_name = f'/kavia-backend/kube/qa/config'
ssm_pre_prod_param_name = f'/kavia-backend/kube/preprod/config'

def load_ssm_param(param_name):
    ssm = boto3.client('ssm')
    response = ssm.get_parameter(Name=param_name)
    return response['Parameter']['Value']

def json_load(param):
    return json.loads(param)

def load_ssm_dev_param():
    return json_load(load_ssm_param(ssm_dev_param_name))

def load_ssm_qa_param():
    return json_load(load_ssm_param(ssm_qa_param_name))

def load_ssm_pre_prod_param():  
    return json_load(load_ssm_param(ssm_pre_prod_param_name))

