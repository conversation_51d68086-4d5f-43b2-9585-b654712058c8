import json
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from enum import Enum

# Base class to handle enum serialization
class SerializableEnum(str, Enum):
    pass

# Convert all enums to use SerializableEnum
class BlendMode(SerializableEnum):
    NORMAL = "NORMAL"
    MULTIPLY = "MULTIPLY"
    SCREEN = "SCREEN"
    OVERLAY = "OVERLAY"
    DARKEN = "DARKEN"
    LIGHTEN = "LIGHTEN"
    COLOR_DODGE = "COLOR_DODGE"
    COLOR_BURN = "COLOR_BURN"
    HARD_LIGHT = "HARD_LIGHT"
    SOFT_LIGHT = "SOFT_LIGHT"
    DIFFERENCE = "DIFFERENCE"
    EXCLUSION = "EXCLUSION"
    HUE = "HUE"
    SATURATION = "SATURATION"
    COLOR = "COLOR"
    LUMINOSITY = "LUMINOSITY"
    PASS_THROUGH = "PASS_THROUGH"

class EffectType(SerializableEnum):
    INNER_SHADOW = "INNER_SHADOW"
    DROP_SHADOW = "DROP_SHADOW"
    LAYER_BLUR = "LAYER_BLUR"
    BACKGROUND_BLUR = "BACKGROUND_BLUR"

class PaintType(SerializableEnum):
    SOLID = "SOLID"
    GRADIENT_LINEAR = "GRADIENT_LINEAR"
    GRADIENT_RADIAL = "GRADIENT_RADIAL"
    GRADIENT_ANGULAR = "GRADIENT_ANGULAR"
    GRADIENT_DIAMOND = "GRADIENT_DIAMOND"
    IMAGE = "IMAGE"
    EMOJI = "EMOJI"

@dataclass
class JsonSerializable:
    def for_json(self):
        def serialize_value(v):
            if hasattr(v, 'for_json'):
                return v.for_json()
            if isinstance(v, list):
                return [serialize_value(i) for i in v]
            if isinstance(v, dict):
                return {k: serialize_value(v) for k, v in v.items()}
            return v

        return {k: serialize_value(v) for k, v in self.__dict__.items()}

@dataclass
class Color(JsonSerializable):
    r: float
    g: float
    b: float
    a: float = 1.0

@dataclass
class Vector(JsonSerializable):
    x: float
    y: float

@dataclass
class Bounds(JsonSerializable):
    x: float
    y: float
    width: float
    height: float

@dataclass
class GradientStop(JsonSerializable):
    position: float
    color: Color

@dataclass
class Paint(JsonSerializable):
    type: PaintType
    color: Optional[Color] = None
    gradient_stops: Optional[List[GradientStop]] = None
    blend_mode: BlendMode = BlendMode.NORMAL
    opacity: float = 1.0


@dataclass
class Effect(JsonSerializable):
    type: EffectType
    color: Optional[Color] = None
    offset: Optional[Vector] = None
    radius: float = 0
    visible: bool = True
    spread: float = 0

@dataclass
class TextStyle(JsonSerializable):
    font_family: str
    font_size: float
    font_weight: int
    letter_spacing: float
    line_height_px: float
    text_align_horizontal: str
    text_align_vertical: str
    text_decoration: Optional[str] = None
    text_transform: Optional[str] = None
    paragraph_spacing: Optional[float] = None
    paragraph_indent: Optional[float] = None

@dataclass
class Constraints(JsonSerializable):
    horizontal: str
    vertical: str

@dataclass
class Padding(JsonSerializable):
    top: float = 0
    right: float = 0
    bottom: float = 0
    left: float = 0

@dataclass
class Layout(JsonSerializable):
    mode: Optional[str] = None
    padding: Padding = field(default_factory=Padding)
    spacing: float = 0
    alignment: Dict[str, str] = field(default_factory=dict)
    position: Optional[Bounds] = None

@dataclass
class Action(JsonSerializable):
    type: str
    destination_id: Optional[str] = None
    navigation: Optional[str] = None
    transition: Optional[Dict[str, Any]] = None

@dataclass
class Interaction(JsonSerializable):
    trigger: Dict[str, str]
    actions: List[Action]

@dataclass
class Node(JsonSerializable):
    id: str
    name: str
    type: str
    visible: bool = True
    properties: Dict[str, Any] = field(default_factory=dict)
    children: List['Node'] = field(default_factory=list)
    fills: List[Paint] = field(default_factory=list)
    strokes: List[Paint] = field(default_factory=list)
    effects: List[Effect] = field(default_factory=list)
    layout: Optional[Layout] = None
    constraints: Optional[Constraints] = None
    text_style: Optional[TextStyle] = None
    characters: Optional[str] = None
    interactions: List[Interaction] = field(default_factory=list)

@dataclass
class Component(JsonSerializable):
    id: str
    file_key: str
    hierarchy: Node
    styles: Dict[str, List[Union[Paint, Effect]]]
    layout: Layout
    constraints: Optional[Constraints]

@dataclass
class FigmaDocument(JsonSerializable):
    metadata: Dict[str, str]
    components: List[Component]
    styles: Dict[str, List[Union[Paint, Effect, TextStyle]]]
    assets: Dict[str, List[str]]
    interactions: List[Dict[str, Any]]

    def to_json(self) -> str:
        """Convert the document to a JSON string"""
        return json.dumps(self.for_json())