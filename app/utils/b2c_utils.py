from app.connection.tenant_middleware import get_tenant_id, KAVIA_ROOT_DB_NAME,get_user_id
from app.core.Settings import settings

def get_collection_name(db_name:str, collection_name:str, user_id:str = None) -> str:
    current_user = None
    get_current_tenant_id = get_tenant_id()
    if collection_name in ['tenant_permissions', 'tenant_users', 'tenant_groups', 'tenant_organizations','tenant_plans','public_projects']:
        db_name = KAVIA_ROOT_DB_NAME
    elif get_current_tenant_id == settings.KAVIA_B2C_CLIENT_ID and db_name.endswith(settings.KAVIA_B2C_CLIENT_ID): 
        try:
            current_user = get_user_id() if user_id == None else user_id
            user_id=current_user
            if user_id:
                collection_name = f"{user_id}_{collection_name}"
        except Exception as e:
            print(f"Error getting user_id: {str(e)}")        
    return collection_name 