import boto3
from botocore.exceptions import ClientError
import logging
from app.core.Settings import settings
import io
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter
from io import BytesIO
import time
from concurrent.futures import ThreadPoolExecutor, as_completed


def get_s3_client():
    return boto3.client('s3',
                        region_name=settings.AWS_REGION,
                        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)

def upload_file_to_s3(file_content, bucket, object_name):
    """
    Upload a file to an S3 bucket

    :param file_content: Bytes content of the file to upload
    :param bucket: Bucket to upload to
    :param object_name: S3 object name (file name to use in S3)
    :return: True if file was uploaded, else False
    """
    try:
        s3_client = get_s3_client()
        # Use BytesIO to handle the file content
        file_obj = io.BytesIO(file_content)
        s3_client.upload_fileobj(file_obj, bucket, object_name)
    except ClientError as e:
        logging.error(f"Error in S3 upload operation: {e}")
        return False
    return True

def extract_text_with_textract(file_content=None, bucket=None, object_name=None):
    """
    Extract text from an image or document using Amazon Textract

    :param file_content: Bytes content of the file (mutually exclusive with bucket and object_name)
    :param bucket: S3 bucket name (required if file_content is not provided)
    :param object_name: S3 object name (required if file_content is not provided)
    :return: Extracted text as a string, or None if extraction failed
    """
    textract_client = boto3.client('textract',
                                   region_name=settings.AWS_REGION,
                                   aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                                   aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)

    try:
        if file_content:
            response = textract_client.detect_document_text(Document={'Bytes': file_content})
        elif bucket and object_name:
            response = textract_client.detect_document_text(
                Document={'S3Object': {'Bucket': bucket, 'Name': object_name}}
            )
        else:
            raise ValueError("Either file_content or both bucket and object_name must be provided")

        extracted_text = ""
        for item in response['Blocks']:
            if item['BlockType'] == 'LINE':
                extracted_text += item['Text'] + "\n"

        return extracted_text.strip()

    except ClientError as e:
        logging.error(f"Error in Textract operation: {e}")
        return None
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        return None

def get_s3_file_url(bucket, object_name):
    """Generate a URL for the file in S3."""
    return f"https://{bucket}.s3.amazonaws.com/{object_name}"

# def extract_text_from_pdf(file_content):
#     start = time.time()
#     """
#     Extract text from a PDF using Amazon Textract, processing each page separately in parallel

#     :param file_content: Bytes content of the PDF file
#     :return: Extracted text as a string with page separators, or None if extraction failed
#     """
#     # Initialize the Textract client outside the thread function to avoid re-initialization
#     textract_client = boto3.client('textract',
#                                    region_name=settings.AWS_REGION,
#                                    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
#                                    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)

#     try:
#         # Create a PdfReader object
#         pdf = PdfReader(BytesIO(file_content))
#         num_pages = len(pdf.pages)

#         all_pages_text = [""] * num_pages  # Pre-allocate list for storing page texts

#         def process_page(page_num):
#             try:
#                 page = pdf.pages[page_num]
#                 page_content = BytesIO()
#                 pdf_writer = PdfWriter()
#                 pdf_writer.add_page(page)
#                 pdf_writer.write(page_content)
#                 page_bytes = page_content.getvalue()

#                 # Process the page with Textract
#                 response = textract_client.detect_document_text(Document={'Bytes': page_bytes})

#                 page_text = f"Page {page_num + 1}:\n"
#                 for item in response['Blocks']:
#                     if item['BlockType'] == 'LINE':
#                         page_text += item['Text'] + "\n"
#                 return page_num, page_text
#             except ClientError as e:
#                 logging.error(f"Error in Textract operation on page {page_num + 1}: {e}")
#                 return page_num, ""
#             except Exception as e:
#                 logging.error(f"Unexpected error on page {page_num + 1}: {e}")
#                 return page_num, ""

#         # Set the maximum number of worker threads
#         max_workers = min(10, num_pages)  # Adjust based on AWS rate limits
#         with ThreadPoolExecutor(max_workers=max_workers) as executor:
#             futures = [executor.submit(process_page, page_num) for page_num in range(num_pages)]
#             for future in as_completed(futures):
#                 page_num, page_text = future.result()
#                 all_pages_text[page_num] = page_text

#         print(f"Total processing time: {time.time() - start} seconds")
#         # Join all pages with double newlines for separation
#         return "\n\n".join(all_pages_text)

#     except Exception as e:
#         logging.error(f"Unexpected error: {e}")
#         return None
    