import json
from datetime import datetime
import os
import tempfile

def debug_redis_knowledge(knowledge_obj):
    """
    Debug the knowledge object from getRedisKnowledge and write to tmp directory
    """
    # Get system temp directory
    tmp_dir = tempfile.gettempdir()
    debug_dir = os.path.join(tmp_dir, 'knowledge_debug')
    
    # Create debug directory in tmp if it doesn't exist
    if not os.path.exists(debug_dir):
        os.makedirs(debug_dir)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = os.path.join(debug_dir, f"redis_knowledge_{timestamp}.txt")
    
    with open(filename, 'w') as f:
        f.write(f"Debug Time: {datetime.now()}\n")
        f.write("=" * 50 + "\n\n")
        
        # Write type information
        f.write(f"Object Type: {type(knowledge_obj)}\n")
        f.write("=" * 50 + "\n\n")
        
        # Write attributes
        f.write("Attributes:\n")
        for attr in dir(knowledge_obj):
            if not attr.startswith('__'):
                try:
                    value = getattr(knowledge_obj, attr)
                    f.write(f"{attr}: {type(value)} = {value}\n")
                except Exception as e:
                    f.write(f"{attr}: <Unable to access: {str(e)}>\n")
        
        # If the object is dict-like, dump its contents
        if hasattr(knowledge_obj, 'items'):
            f.write("\nDictionary Contents:\n")
            try:
                f.write(json.dumps(knowledge_obj, indent=2, default=str))
            except Exception as e:
                f.write(f"<Unable to serialize object: {str(e)}>\n")
    
    print(f"Redis knowledge debug file created at: {filename}")
    return filename

def debug_session_knowledge(knowledge_obj):
    """
    Debug the knowledge object from Knowledge.getKnowledge and write to tmp directory
    """
    # Get system temp directory
    tmp_dir = tempfile.gettempdir()
    debug_dir = os.path.join(tmp_dir, 'knowledge_debug')
    
    # Create debug directory in tmp if it doesn't exist
    if not os.path.exists(debug_dir):
        os.makedirs(debug_dir)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = os.path.join(debug_dir, f"session_knowledge_{timestamp}.txt")
    
    with open(filename, 'w') as f:
        f.write(f"Debug Time: {datetime.now()}\n")
        f.write("=" * 50 + "\n\n")
        
        # Write type information
        f.write(f"Object Type: {type(knowledge_obj)}\n")
        f.write("=" * 50 + "\n\n")
        
        # Write attributes
        f.write("Attributes:\n")
        for attr in dir(knowledge_obj):
            if not attr.startswith('__'):
                try:
                    value = getattr(knowledge_obj, attr)
                    f.write(f"{attr}: {type(value)} = {value}\n")
                except Exception as e:
                    f.write(f"{attr}: <Unable to access: {str(e)}>\n")
        
        # Try to get any special attributes that might be relevant
        f.write("\nSpecial Attributes:\n")
        try:
            if hasattr(knowledge_obj, 'session_id'):
                f.write(f"session_id: {knowledge_obj.session_id}\n")
            if hasattr(knowledge_obj, 'data'):
                f.write(f"data: {knowledge_obj.data}\n")
        except Exception as e:
            f.write(f"<Unable to access special attributes: {str(e)}>\n")
        
        # Try to get dictionary representation
        f.write("\nDictionary Representation:\n")
        try:
            if hasattr(knowledge_obj, '__dict__'):
                f.write(json.dumps(knowledge_obj.__dict__, indent=2, default=str))
            else:
                f.write("<Object has no __dict__ attribute>\n")
        except Exception as e:
            f.write(f"<Unable to get dictionary representation: {str(e)}>\n")
    
    print(f"Session knowledge debug file created at: {filename}")
    return filename

def debug_embeddings_object(embeddings_obj):
    """
    Debug the embeddings object from Knowledge.embeddings and write to tmp directory
    
    Args:
        embeddings_obj: The KnowledgeEmbeddings instance to debug
        
    Returns:
        str: Path to the debug file
    """
    # Get system temp directory
    tmp_dir = tempfile.gettempdir()
    debug_dir = os.path.join(tmp_dir, 'knowledge_debug')
    
    # Create debug directory in tmp if it doesn't exist
    if not os.path.exists(debug_dir):
        os.makedirs(debug_dir)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = os.path.join(debug_dir, f"embeddings_debug_{timestamp}.txt")
    
    with open(filename, 'w') as f:
        f.write(f"Debug Time: {datetime.now()}\n")
        f.write("=" * 50 + "\n\n")
        
        # Write type information
        f.write(f"Object Type: {type(embeddings_obj)}\n")
        f.write("=" * 50 + "\n\n")
        
        # Write main attributes
        f.write("Main Configuration:\n")
        try:
            f.write(f"Collection Name: {embeddings_obj.milvus_collection_name}\n")
            f.write(f"Embedding Dimension: {embeddings_obj.embedding_dim}\n")
            f.write(f"Top K Results: {embeddings_obj.top_k}\n")
            f.write(f"Search Radius: {embeddings_obj.radius}\n")
            f.write(f"Range Filter: {embeddings_obj.range_filter}\n")
            f.write(f"Connection Timeout: {embeddings_obj.conn_timeout}\n")
            f.write(f"Embedding Model: {embeddings_obj.model}\n")
        except Exception as e:
            f.write(f"<Unable to access main config: {str(e)}>\n")
        
        # Write clients information
        f.write("\nMilvus Clients:\n")
        try:
            for code_base_name, client in embeddings_obj.clients.items():
                f.write(f"Code Base: {code_base_name}\n")
                f.write(f"  Client Type: {type(client)}\n")
                
                # Check if collection exists
                try:
                    has_collection = client.has_collection(embeddings_obj.milvus_collection_name)
                    f.write(f"  Has Collection: {has_collection}\n")
                    
                    if has_collection:
                        # Get collection statistics
                        try:
                            stats = client.get_collection_stats(embeddings_obj.milvus_collection_name)
                            f.write(f"  Collection Stats: {stats}\n")
                        except Exception as e:
                            f.write(f"  <Unable to get collection stats: {str(e)}>\n")
                        
                        # Try to count entities
                        try:
                            count = client.count(embeddings_obj.milvus_collection_name)
                            f.write(f"  Entity Count: {count}\n")
                        except Exception as e:
                            f.write(f"  <Unable to count entities: {str(e)}>\n")
                except Exception as e:
                    f.write(f"  <Unable to check collection: {str(e)}>\n")
        except Exception as e:
            f.write(f"<Unable to access clients: {str(e)}>\n")
        
        # Test creating an embedding
        f.write("\nEmbedding Creation Test:\n")
        try:
            test_terms = ["test", "embedding"]
            f.write(f"Test Terms: {test_terms}\n")
            
            # Try to prepare text
            try:
                prepared_text = embeddings_obj._prepare_search_terms_text(test_terms)
                f.write(f"Prepared Text: {prepared_text}\n")
            except Exception as e:
                f.write(f"<Unable to prepare text: {str(e)}>\n")
                
            # Don't actually create an embedding to avoid API costs
            f.write("Note: Skipping actual embedding creation to avoid API costs\n")
        except Exception as e:
            f.write(f"<Test setup error: {str(e)}>\n")
        
        # Write all other attributes
        f.write("\nAll Attributes:\n")
        for attr in dir(embeddings_obj):
            if not attr.startswith('__') and attr not in [
                'milvus_collection_name', 'embedding_dim', 'top_k', 'radius', 
                'range_filter', 'conn_timeout', 'model', 'clients',
                '_instances', '_lock'
            ]:
                try:
                    value = getattr(embeddings_obj, attr)
                    # Skip method objects to keep output clean
                    if not callable(value):
                        f.write(f"{attr}: {type(value)} = {value}\n")
                    else:
                        f.write(f"{attr}: <method>\n")
                except Exception as e:
                    f.write(f"{attr}: <Unable to access: {str(e)}>\n")
    
    print(f"Embeddings debug file created at: {filename}")
    return filename