# app/utils/session_manager.py
from datetime import timedelta
import asyncio
import os
from typing import Dict, List, Optional

from app.core.Settings import Settings
from app.core.websocket.client import WebSocketClient
from app.knowledge.redis_kg import add_redis_support_to_knowledge
from app.utils.kg_inspect.knowledge import KnowledgeCodeBase
from app.utils.kg_inspect.knowledge_helper import Knowledge_Helper
from app.utils.kg_inspect.knowledge_reporter import Reporter
settings = Settings()

class KnowledgeInstance:
    def __init__(self, session_id: str = None):
        self.ws_client = WebSocketClient(session_id, settings.WEBSOCKET_URI)
        self.reporter = Reporter(self.ws_client)
        self.reporter.initialize()
        self.knowledge_helper = None
        self.is_initialized = False
        self.files_processed = 0
        self.total_files = 0
        self.initialization_started = False
        self.session_id = session_id  # Add this line
        
class SessionManager:
    def __init__(self):
        self._instances: Dict[str, KnowledgeInstance] = {}
        self.sessions = {}
        self.session_timeout = timedelta(hours=24)
        self._lock = asyncio.Lock()

    async def get_session(self, session_id: str):
        print(self.sessions)
        
        async with self._lock:
            print("SESSION STATUS : ", self.sessions.get(session_id))
            return self.sessions.get(session_id)

    async def get_or_create_instance(self, session_id: str) -> KnowledgeInstance:
        print("Trying to get the instance")
        async with self._lock:
            
            if session_id not in self._instances:
                print("Creating new instance")
                instance = KnowledgeInstance(session_id)
                self._instances[session_id] = instance
            else:
                print("Instance already there")
                
            return self._instances[session_id]

    def get_instance_by_session(self, session_id: str) -> Optional[KnowledgeInstance]:
        instance = self._instances.get(session_id)
        if instance and instance.session_id == session_id:
            return instance
        return None

    # Add method to get all instances for a session
    def get_instances_by_session(self, session_id: str) -> List[KnowledgeInstance]:
        return [instance for instance in self._instances.values() if instance.session_id == session_id]

    async def initialize_instance(self, codebase_paths: KnowledgeCodeBase, session_id: str, instance: KnowledgeInstance):
        print("Starting the instance")
        
        if instance.initialization_started:
            print("Skipping, because of the instance : ")
            return

        instance.initialization_started = True

        instance.knowledge_helper = Knowledge_Helper(
            _id=session_id,
            reporter=instance.reporter,
            base_path=os.getcwd(),
            code_bases=codebase_paths
        )
        
        knowledge = instance.knowledge_helper.knowledge.getKnowledge(id=session_id)
        add_redis_support_to_knowledge(knowledge)
        # Initialize total_files count before starting
        instance.total_files = 0
        for base in codebase_paths:
            count = len(instance.knowledge_helper.knowledge._list_important_files(base.base_path))
            instance.total_files += count 
            
        # Start the knowledge processing
        instance.knowledge_helper.knowledge.start()
        instance.knowledge_helper.knowledge.save_to_redis()

    async def remove_instance_by_session(self, session_id: str):
        """Remove a specific instance associated with a session"""
        async with self._lock:
            if session_id in self._instances:
                del self._instances[session_id]