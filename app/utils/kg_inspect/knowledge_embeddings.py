from pymilvus import MilvusClient, CollectionSchema, FieldSchema, DataType
import numpy as np
import openai
import os
from typing import List, Optional, Dict, Any
import threading
import json
import logging
from code_generation_core_agent.config import config

# Add logger setup at the top of the file
logger = logging.getLogger(__name__)


class KnowledgeEmbeddings:
    """Handles embedding creation and vector search for source file search terms lists using Milvus Lite."""

    _instances = {}
    _lock = threading.Lock()

    def __init__(self, code_bases):
        self.milvus_collection_name = "file_search_terms_embeddings"
        self.embedding_dim = config.getint('KNOWLEDGE', 'semantic_search_embedding_dim', fallback=1536)
        self.top_k = config.getint('KNOWLEDGE', 'semantic_search_top_k', fallback=10)
        self.radius = config.getfloat('KNOWLEDGE', 'semantic_search_radius', fallback=0.8)
        self.range_filter = config.getfloat('KN<PERSON>LEDGE', 'semantic_search_range_filter', fallback=0.95)
        self.conn_timeout = config.getint('KNOWLEDGE', 'semantic_search_conn_timeout', fallback=60)
        self.model = config.get('KNOWLEDGE', 'semantic_search_model', fallback='text-embedding-ada-002')

        self.clients = {}
        
        print("Inside the Knowledeg Embedding class : ", code_bases)

        for code_base_name, base_path in code_bases.items():
            # Set up Milvus Lite in the .knowledge folder
            milvus_lite_path = os.path.join(
                base_path, ".knowledge", ".vector_db", "milvus.db"
            )
            os.makedirs(os.path.dirname(milvus_lite_path), exist_ok=True)

            # Initialize Milvus Lite client
            client = MilvusClient(uri=milvus_lite_path, timeout=self.conn_timeout)
            self._ensure_collection(client)
            self.clients[code_base_name] = client

    @staticmethod
    def get_instance(id, code_bases):
        instance = None
        with KnowledgeEmbeddings._lock:
            if id not in KnowledgeEmbeddings._instances:
                if code_bases is None:
                    raise ValueError("code_bases is required for initialization")
                KnowledgeEmbeddings._instances[id] = KnowledgeEmbeddings(code_bases)
            instance = KnowledgeEmbeddings._instances[id]
        return instance

    @staticmethod
    def release_instance(id):
        with KnowledgeEmbeddings._lock:
            if id in KnowledgeEmbeddings._instances:
                instance = KnowledgeEmbeddings._instances[id]
                instance.__del__()
                KnowledgeEmbeddings._instances.pop(id)

    def _ensure_collection(self, client):
        """Create Milvus Lite collection if it doesn't exist."""
        try:
            if not client.has_collection(self.milvus_collection_name):
                # Define fields
                id_field = FieldSchema(
                    name="id",
                    dtype=DataType.INT64,
                    is_primary=True,
                    auto_id=True
                )
                file_path_field = FieldSchema(
                    name="file_path",
                    dtype=DataType.VARCHAR,
                    max_length=512
                )
                search_terms_field = FieldSchema(
                    name="search_terms",
                    dtype=DataType.VARCHAR,
                    max_length=4096
                )
                embedding_field = FieldSchema(
                    name="embedding",
                    dtype=DataType.FLOAT_VECTOR,
                    dim=self.embedding_dim
                )

                # Create schema
                schema = CollectionSchema(
                    fields=[id_field, file_path_field, search_terms_field, embedding_field],
                    description="File search terms embeddings collection"
                )

                index_params = client.prepare_index_params()
                index_params.add_index(
                    field_name="embedding",
                    index_type="AUTOINDEX",
                    metric_type="COSINE"
                )

                # Create collection
                client.create_collection(
                    collection_name=self.milvus_collection_name,
                    schema=schema,
                    index_params=index_params
                )

            # Load collection for immediate use - moved outside the if block
            client.load_collection(self.milvus_collection_name)
            res = client.get_load_state(
                collection_name=self.milvus_collection_name
            )
            logger.info(f"Search terms embeddings {self.milvus_collection_name} loaded: {res}")

        except Exception as e:
            logger.error(f"Error creating collection: {e}")
            raise

    def _prepare_search_terms_text(self, search_terms: List[str]) -> str:
        """Prepare search terms for embedding by combining them into a meaningful text."""
        combined_text = f"Source code file related to: {', '.join(search_terms)}"
        return combined_text

    def create_embedding_for_terms(self, search_terms: List[str]) -> List[float]:
        """Create embedding for a list of search terms using OpenAI API."""
        try:
            combined_text = self._prepare_search_terms_text(search_terms)
            response = openai.embeddings.create(
                model=self.model,
                input=combined_text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Error creating embedding: {e}")
            raise

    def store_file_search_terms(
        self, file_path: str, code_base_name: str, search_terms: List[str], embedding: List[float]
    ):
        """Store search terms and their embedding for a file in Milvus Lite."""
        try:
            # Store search terms as JSON string to preserve the list structure
            search_terms_json = json.dumps(search_terms)

            data = [
                {
                    "file_path": file_path,
                    "search_terms": search_terms_json,
                    "embedding": embedding,
                }
            ]

            client = self.clients[code_base_name]
            result = client.insert(
                collection_name=self.milvus_collection_name, data=data
            )
            return result
        except Exception as e:
            logger.error(f"Error storing embedding: {e}")
            raise

    def search_similar_files(
        self, code_base_name: str, search_terms: List[str], top_k: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Search for files with similar search terms contexts.
        
        Args:
            code_base_name: Name of codebase
            search_terms: List of search terms to find similar files for
            top_k: Number of results to return. Defaults to config value
        """
        try:
            if top_k is None:
                top_k = self.top_k

            # Use the config values loaded in constructor
            search_radius = self.radius
            search_range_filter = self.range_filter

            # Create embedding for the search query
            query_embedding = self.create_embedding_for_terms(search_terms)

            search_results = []

            code_base_names = []
            if code_base_name:
                code_base_names.append(code_base_name)
            else:
                code_base_names = self.clients.keys()

            for name in code_base_names:
                client = self.clients[name]

                results = client.search(
                    collection_name=self.milvus_collection_name,
                    data=[query_embedding],
                    limit=top_k,
                    output_fields=["file_path", "search_terms"],
                    search_params={
                        "metric_type": "COSINE",
                        "params": {
                            "radius": search_radius,
                            "range_filter": search_range_filter
                        }
                    },
                )

                for hit in results[0]:  # First element since we only have one query vector
                    # Parse the stored JSON string back to list
                    stored_terms = json.loads(hit["entity"].get("search_terms"))
                    search_results.append(
                        {
                            "file_path": hit["entity"].get("file_path"),
                            "search_terms": stored_terms,
                            "score": hit["distance"],
                        }
                    )

            return search_results
        except Exception as e:
            logger.error(f"Error searching similar files: {e}")
            raise

    def delete_file_embeddings(self, code_base_name: str, file_path: str):
        """Delete embeddings for a specific file from Milvus Lite."""
        try:
            client = self.clients[code_base_name]
            client.delete(
                collection_name=self.milvus_collection_name,
                filter=f'file_path == "{file_path}"',
            )
        except Exception as e:
            logger.error(f"Error deleting embeddings: {e}")
            raise

    def clear_all_embeddings(self, code_base_name: str):
        """Clear all embeddings from the Milvus Lite collection."""
        try:
            client = self.clients[code_base_name]
            if client.has_collection(self.milvus_collection_name):
                client.drop_collection(self.milvus_collection_name)
                self._ensure_collection(client)
        except Exception as e:
            logger.error(f"Error clearing embeddings: {e}")
            raise

    def __del__(self):
        """Cleanup Milvus Lite connection."""
        try:
            for client in self.clients:
                client.close()
        except:
            pass

    def has_file_embeddings(self, code_base_name: str, filename: str) -> bool:
        """Check if embeddings already exist for a given file
        
        Args:
            filename: The name of the file to check
            
        Returns:
            bool: True if embeddings exist, False otherwise
        """
        try:
            client = self.clients[code_base_name]
            results = client.query(
                collection_name=self.milvus_collection_name,
                filter=f'file_path == "{filename}"',
                output_fields=["file_path"],
                limit=1
            )
            return len(results) > 0
        except Exception as e:
            logger.error(f"Error checking embeddings existence: {e}")
            return False
