from PIL import Image
import io
import pytesseract
import base64
def extract_text_from_image(content):
    image = Image.open(io.BytesIO(content))
    text = pytesseract.image_to_string(image)
    return text

def generate_thumbnail(image, size=(400, 400)):
    """Generate a thumbnail from the given image."""
    thumbnail = image.copy()
    thumbnail.thumbnail(size)
    return thumbnail

def generate_preview(image, max_size=(1200, 1200)):
    """Generate a preview from the given image."""
    preview = image.copy()
    preview.thumbnail(max_size)
    return preview

def get_primary_color(image):
    """Extract the primary color from the image."""
    image = image.convert('RGB')
    colors = image.getcolors(image.size[0] * image.size[1])
    max_occurrence, most_present = max(colors, key=lambda x: x[0])
    return ''.join(f'{c:02x}' for c in most_present)

def image_to_bytes(image, format='PNG'):
    """Convert a PIL Image to bytes."""
    img_byte_arr = io.BytesIO()
    image.save(img_byte_arr, format=format)
    return img_byte_arr.getvalue()

def image_to_base64url(image_data, mime_type):
    base64_encoded = base64.b64encode(image_data).decode('utf-8')
    return f"data:{mime_type};base64,{base64_encoded}"
