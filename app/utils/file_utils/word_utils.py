import io
from docx import Document
import subprocess
import tempfile
import os

try:
    import docx2txt
    DOCX2TXT_AVAILABLE = True
except ImportError:
    DOCX2TXT_AVAILABLE = False

def extract_text_from_docx(content):
    """
    Extract text from a .docx file using python-docx library with docx2txt fallback

    Args:
        content (bytes): The binary content of the .docx file

    Returns:
        str: Extracted text from the document
    """
    try:
        # First try with python-docx (more comprehensive)
        try:
            # Create a BytesIO object from the content
            doc_stream = io.BytesIO(content)

            # Load the document
            document = Document(doc_stream)

            # Extract text from all paragraphs
            text_content = []

            # Extract text from paragraphs
            for paragraph in document.paragraphs:
                if paragraph.text.strip():  # Only add non-empty paragraphs
                    text_content.append(paragraph.text)

            # Extract text from tables
            for table in document.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content.append(" | ".join(row_text))

            # Join all text with newlines
            extracted_text = "\n".join(text_content)

            return extracted_text.strip()

        except Exception as docx_error:
            print(f"python-docx failed: {str(docx_error)}, trying docx2txt fallback")

            # Fallback to docx2txt if available
            if DOCX2TXT_AVAILABLE:
                try:
                    # Create a temporary file for docx2txt
                    with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
                        temp_file.write(content)
                        temp_file_path = temp_file.name

                    try:
                        # Extract text using docx2txt
                        text = docx2txt.process(temp_file_path)
                        return text.strip() if text else None
                    finally:
                        # Clean up temporary file
                        try:
                            os.unlink(temp_file_path)
                        except OSError:
                            pass

                except Exception as docx2txt_error:
                    print(f"docx2txt fallback failed: {str(docx2txt_error)}")

            # If both methods fail, re-raise the original error
            raise docx_error

    except Exception as e:
        print(f"Error extracting text from DOCX: {str(e)}")
        return None


def extract_text_from_doc(content):
    """
    Extract text from a .doc file using antiword or catdoc
    
    Args:
        content (bytes): The binary content of the .doc file
        
    Returns:
        str: Extracted text from the document
    """
    try:
        # Create a temporary file to store the .doc content
        with tempfile.NamedTemporaryFile(suffix='.doc', delete=False) as temp_file:
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # Try using antiword first (more reliable for .doc files)
            try:
                result = subprocess.run(
                    ['antiword', temp_file_path],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if result.returncode == 0:
                    return result.stdout.strip()
                else:
                    print(f"Antiword failed with return code {result.returncode}: {result.stderr}")
                    
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                print(f"Antiword not available or timed out: {str(e)}")
            
            # Fallback to catdoc
            try:
                result = subprocess.run(
                    ['catdoc', temp_file_path],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if result.returncode == 0:
                    return result.stdout.strip()
                else:
                    print(f"Catdoc failed with return code {result.returncode}: {result.stderr}")
                    
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                print(f"Catdoc not available or timed out: {str(e)}")
            
            # Try docx2txt as a fallback (sometimes works for .doc files)
            if DOCX2TXT_AVAILABLE:
                try:
                    text = docx2txt.process(temp_file_path)
                    if text and text.strip():
                        return text.strip()
                except Exception as docx2txt_error:
                    print(f"docx2txt fallback failed: {str(docx2txt_error)}")

            # If docx2txt fails, try python-docx as a last resort
            # (it might work for some .doc files that are actually .docx)
            try:
                return extract_text_from_docx(content)
            except Exception as docx_error:
                print(f"Python-docx fallback failed: {str(docx_error)}")

            # If all methods fail, return an error message
            return "Error: Unable to extract text from .doc file. Please ensure antiword or catdoc is installed, or convert the file to .docx format."
            
        finally:
            # Clean up the temporary file
            try:
                os.unlink(temp_file_path)
            except OSError:
                pass
                
    except Exception as e:
        print(f"Error extracting text from DOC: {str(e)}")
        return None


def extract_text_from_word_document(content, file_extension):
    """
    Extract text from Word documents (.doc or .docx)

    Args:
        content (bytes): The binary content of the Word document
        file_extension (str): The file extension ('doc' or 'docx')

    Returns:
        str: Extracted text from the document
    """
    if file_extension.lower() == 'docx':
        return extract_text_from_docx(content)
    elif file_extension.lower() == 'doc':
        return extract_text_from_doc(content)
    else:
        raise ValueError(f"Unsupported Word document format: {file_extension}")


def convert_word_to_txt(content, file_extension, output_path=None):
    """
    Convert Word document to .txt file

    Args:
        content (bytes): The binary content of the Word document
        file_extension (str): The file extension ('doc' or 'docx')
        output_path (str, optional): Path where to save the .txt file

    Returns:
        tuple: (extracted_text, txt_file_path) or (extracted_text, None) if no output_path
    """
    try:
        # Extract text from the Word document
        extracted_text = extract_text_from_word_document(content, file_extension)

        if extracted_text is None:
            return None, None

        # If output path is provided, save to file
        if output_path:
            # Ensure the output path has .txt extension
            if not output_path.lower().endswith('.txt'):
                output_path = output_path + '.txt'

            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Write text to file with UTF-8 encoding
            with open(output_path, 'w', encoding='utf-8') as txt_file:
                txt_file.write(extracted_text)

            return extracted_text, output_path

        return extracted_text, None

    except Exception as e:
        print(f"Error converting Word document to TXT: {str(e)}")
        return None, None


def generate_txt_filename(original_filename):
    """
    Generate TXT filename with format: filename(extension).txt

    Args:
        original_filename (str): Original filename with extension

    Returns:
        str: Generated TXT filename

    Examples:
        'sample.docx' -> 'sample(docx).txt'
        'report.doc' -> 'report(doc).txt'
        'document.DOCX' -> 'document(docx).txt'
    """
    try:
        # Handle empty or invalid filenames
        if not original_filename or original_filename.strip() == '':
            return "(unknown).txt"

        # Split filename and extension
        if '.' in original_filename:
            base_name, extension = original_filename.rsplit('.', 1)

            # Handle edge cases
            if not extension or extension.strip() == '':
                extension = 'unknown'
            else:
                # Convert extension to lowercase for consistency
                extension = extension.lower()

            # Handle case where base_name is empty (e.g., ".docx")
            if not base_name or base_name.strip() == '':
                base_name = ''
        else:
            # If no extension found, use the whole filename as base
            base_name = original_filename
            extension = 'unknown'

        # Generate the new filename format: filename(extension).txt
        txt_filename = f"{base_name}({extension}).txt"

        return txt_filename

    except Exception as e:
        print(f"Error generating TXT filename: {str(e)}")
        # Fallback to simple naming
        return f"{original_filename}.txt"


def convert_word_to_txt_bytes(content, file_extension):
    """
    Convert Word document to .txt file content as bytes

    Args:
        content (bytes): The binary content of the Word document
        file_extension (str): The file extension ('doc' or 'docx')

    Returns:
        bytes: The .txt file content as bytes, or None if conversion failed
    """
    try:
        # Extract text from the Word document
        extracted_text = extract_text_from_word_document(content, file_extension)

        if extracted_text is None:
            return None

        # Convert text to bytes with UTF-8 encoding
        return extracted_text.encode('utf-8')

    except Exception as e:
        print(f"Error converting Word document to TXT bytes: {str(e)}")
        return None
