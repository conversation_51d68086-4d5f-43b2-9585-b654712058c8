import requests
import os
from dotenv import load_dotenv

load_dotenv()

def get_github_client_details():
    """Load GitHub OAuth details from environment variables."""
    GITHUB_CLIENT_ID = os.getenv("GITHUB_CLIENT_ID")
    GITHUB_CLIENT_SECRET = os.getenv("GITHUB_CLIENT_SECRET")
    GITHUB_REDIRECT_URI = os.getenv("GITHUB_REDIRECT_URI")
    print(GITHUB_CLIENT_SECRET)
    
    return GITHUB_CLIENT_ID, GITHUB_CLIENT_SECRET, GITHUB_REDIRECT_URI

def exchange_github_code_for_token(code: str):
    """Exchange authorization code for an access token."""
    GITHUB_CLIENT_ID, GITHUB_CLIENT_SECRET, GITHUB_REDIRECT_URI = get_github_client_details()
    
    token_url = "https://github.com/login/oauth/access_token"
    headers = {"Accept": "application/json"}
    payload = {
        "client_id": GITHUB_CLIENT_ID,
        "client_secret": GITHUB_CLIENT_SECRET,
        "code": code,
        "redirect_uri": GITHUB_REDIRECT_URI,
    }

    # print(payload)
    response = requests.post(token_url, headers=headers, data=payload)
    response_json = response.json()
    return response_json.get("access_token")

def get_user_info(access_token: str):
    """Get user information from GitHub."""
    user_info_url = "https://api.github.com/user"
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(user_info_url, headers=headers)
    return response.json()

def get_user_repos(token: str):
    """Get list of repositories for authenticated user."""
    all_repos = []
    page = 1
    per_page = 100  # Maximum allowed by GitHub
    
    while True:
        repo_url = f"https://api.github.com/user/repos?per_page={per_page}&page={page}"
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(repo_url, headers=headers)
        
        if response.status_code != 200:
            return None
            
        repos = response.json()
        if not repos:  # No more repos to fetch
            break
            
        all_repos.extend(repos)
        page += 1
    
    return all_repos
