{"name": "name", "pathname": "Scope.pdf", "title": "Scope.pdf", "hash": "a3fc06df5afe376ffc737918a2bebbda", "doc_info": {"description": "NICL intends to utilize the services of a comprehensive online KYC process with video verification in accordance with CCA guidelines. The selected bidder shall perform activities such as Integration, Configuration, Deployment, Customization, Commissioning, Testing, Training, and Capacity Building. The online KYC service will be implemented through CKYC, eKYC, and manually uploaded OVDs, utilizing Optical Character Recognition (OCR) for data extraction and assessing data based on a customized risk matrix via online API integration. The solution aims to comply with the latest IRDAI guidelines on AML/CFT, ensure minimal human intervention, and maintain high data security standards while providing a seamless user experience for both retail and corporate customers. The architecture will be based on Data Centre - Disaster Recovery setup within India.", "purpose_and_responsibilies": "NICL intends to utilize comprehensive online KYC processes, including video verification in compliance with CCA guidelines. Key functionalities include integration, configuration, deployment, customization, testing, training, and capacity building for online KYC services via CKYC, eKYC, and manual OVD upload. The solution will feature Optical Character Recognition (OCR) for data extraction and will assess customer risk through customized matrices during various interactions such as purchases or renewals. The project aims to comply with IRDAI guidelines on AML/CFT by screening and verifying customer identities. The online KYC solution will minimize human intervention and include a secure, end-to-end encrypted process with real-time dashboards, data residency in India, and a comprehensive audit trail of activities. The solution must also support business continuity, high availability, customizable KYC workflows, and be scalable to manage varying transaction volumes.", "functional_requirements": "NICL intends to utilize the services of a comprehensive online KYC process with video verification according to CCA guidelines. The selected bidder shall perform activities including Integration, Configuration, Deployment, Customization, Commissioning, Testing, Training & Capacity Building. The online KYC service will involve CKYC, eKYC, and manual OVD uploads utilizing Optical Character Recognition (OCR) for data extraction and evaluating data based on a customized risk matrix. Key functional requirements include: compliance with IRDAI guidelines, support for CKYC and other OVDs, real-time and secure processes with end-to-end encryption, liveness checks to prevent fraud, real-time dashboards for KYC activities, data residency in India, application security against vulnerabilities, training for NICL officials, isolation of NICL data, dedicated cloud availability, high availability at DC and DR, customization of UI interface, performance testing, scalability for high transaction volumes, customizable KYC verification workflows, integration with APIs and SDKs, audit trails for activities, report generation, bulk KYC processing capability, and integration with NICL’s existing systems.", "non_functional_requirements": "1. The solution must comply with IRDAI guidelines for the KYC Process.\n3. The solution shall ensure that the process is a seamless, real-time, secured and end-to-end encrypted.\n4. The solution should enable the National Insurance Corporation (NICL) official to carry out a liveness check in order to guard against spoofing and such other fraudulent manipulations.\n6. Data Residency: The Platform should be entirely hosted in the country 'India'. All data stored on the platform must always reside within the country 'India'.\n7. Application Security: Service provider at times to provide, maintain and support IT software and subsequent updates, upgrades and bug fixes such that the software is and remains secure from vulnerabilities.\n10. NICL’s environment should not be shared with other clients. The Application Data should remain completely isolated and exclusive from other clients data.\n25. The solution architecture should allow it to quickly scale up in case volume surges up. Any limitation to future scalability should be mentioned in the response.\n28. The solution should be highly scalable and capable of delivering high performance as & when transaction volumes/users increases without compromising on the response time.\n33. The data should be stored encrypted in transit and storage.\n41. The solution must have a comprehensive audit trail of all activities done on the platform and their associated results.", "standards_compliance": "1. The proposed solution must comply with IRDAI guidelines for the KYC Process.\n2. The solution must possess real-time dashboards capabilities to track KYC activities.\n3. The solution should ensure that the process is a seamless, real-time, secured and end-to-end encrypted.\n4. The solution must comply with all the latest guidelines of the regulatory authorities. During the contract period, a successful bidder needs to make necessary changes in the solution to meet regulatory requirement changes.\n5. The bidder must ensure that all the regulations of Information Technology ACT, 2000 as amended from time to time, as being adhered to.", "regulatory_compliance": "The proposed solution must comply with IRDAI guidelines for the KYC Process. The solution must possess real-time dashboards capabilities to track KYC activities. The solution must be compliant with all the latest guidelines of the regulatory authorities. During the contract period, a successful bidder needs to make necessary changes in the solution to meet regulatory requirement changes. The bidder should ensure that all the regulations of Information Technology ACT, 2000 as amended from time to time, are being adhered to.", "security_requirements": {"security_requirements": ["The solution must comply with IRDAI guidelines for the KYC Process.", "The solution shall ensure that the process is a seamless, real-time, secured and end-to-end encrypted.", "The solution should enable the NICL official to carry out a liveness check to guard against spoofing and such other fraudulent manipulations.", "Data Residency: The Platform should be entirely hosted in the country 'India'. All data stored on the platform must always reside within the country 'India'.", "Application Security: Service provider at times to provide, maintain and support IT software and subsequent updates, upgrades and bug fixes such that the software is and remains secure from vulnerabilities.", "The solution should be compliant with all the latest guidelines of the regulatory authorities.", "NICL's environment should not be shared with other clients. The Application Data should remain completely isolated and exclusive from other clients' data.", "To share what compartmentalization techniques are employed to isolate NICL data from other customer's data.", "The solution should be made available on a dedicated cloud.", "The solution should have proper Business Continuity Plan.", "The bidder will provide customization of UI interface as per NICL's requirements.", "The bidder must have built-in centralized access-logs at individual level as well as at application resources level for security compliance purposes.", "IP Whitelisting: The service provider must have the capability of IP whitelisting to restrict platform access from a limited number of known locations / IP addresses.", "The solution must ensure that all regulations of Information Technology ACT, 2000 as amended from time to time, are adhered to.", "The data should be stored encrypted in transit and storage.", "The solution should maintain a log and report for all transactions for audit purpose. Reporting of this module shall be integrated with the Dashboard Module.", "The solution must have a comprehensive audit trail of all activities done on the platform and their associated results."]}, "subsystem_descriptions": "NICL intends to implement a comprehensive online KYC process with video verification according to CCA guidelines. Key features include: 1. Compliance with IRDAI guidelines for KYC. 2. Support for CKYC, DigiLocker, Aadhar XML, and other OVDs for customer verification. 3. Seamless, real-time, secured, end-to-end encrypted processes. 4. Liveness checks for fraud prevention. 5. Real-time dashboards for KYC activity tracking. 6. Data residency in India. 7. Application security with ongoing maintenance and updates. 8. Compliance with regulatory guidelines. 9. Training for NICL officials on the KYC software application. 10. Isolation of NICL's environment from other clients. 11. Detailed system requirement specifications and architectural documentation. 12. End-to-end testing of the application and integration. 13. Support for high availability and business continuity planning including disaster recovery. 14. Custom UI interface as per NICL requirements. 15. Capability for performance testing and audit trails. 16. Customizable KYC verification workflow and real-time analytics of application metrics. 17. Support for multi-channel ecosystems for distributors. 18. Capability for integration with E-Nach and BBPS. 19. Features to enhance user experience, such as capturing live selfies and identity documents, detecting fake documents, and preventing impersonation.", "component_interactions": "NICL intends to utilize a comprehensive online KYC process with Video verification as per CCA guidelines. The selected bidder will perform Integration, Configuration, Deployment, Customization, Commissioning, Testing, Training & Capacity Building. The online KYC service will include CKYC, eKYC, and manual OVD uploads using OCR for data extraction and a customized risk matrix. The solution will integrate with APIs for customer identity verification during various stages such as purchase or renewal. It will support seamless, real-time, secured, and end-to-end encrypted processing. NICL officials will carry out a liveness check to prevent spoofing. The platform will have real-time dashboards to track KYC activities and will store data entirely within India. The solution architecture will allow for scalability and responsiveness across devices and platforms. It will provide an interface for displaying and filtering KYC applications based on risk scores, as well as an integrated dashboard for auditing transactions, making it suitable for interactions with NICL users and external systems.", "behavior_descriptions": "NICL intends to utilize the services of comprehensive online KYC process with the facility of Video verification in accordance to CCA guidelines. The proposed solution must comply with IRDAI guidelines for the KYC Process and support CKYC/Digilocker/Aadhar XML or any other OVDs based customer verification. The solution shall ensure that the process is seamless, real-time, secured, and end-to-end encrypted. NICL officials should be able to carry out liveness checks to guard against spoofing and other fraudulent manipulations. The solution must possess real-time dashboard capabilities to track KYC activities and all data must reside in India while maintaining application security and compliance with all latest regulatory guidelines. Comprehensive training, documentation, and support must be provided for all functionalities before and after Go-Live. The solution must facilitate customizable KYC verification workflows and accept files uploaded by customers. Additionally, a dashboard for reviewing KYC applications, a back-end interface for filtering applications based on risk score, and capabilities for automated decision-making through a business rule engine are required. The system should log all activities for auditing purposes and provide customizable reports. It must support a multi-channel ecosystem for distributors and direct selling agents (DSAs), as well as be scalable for future demands.", "data_management": "NICL intends to utilize an online KYC process with video verification, integrating CKYC, eKYC, and manual OVD upload. Data extraction will employ Optical Character Recognition (OCR) to evaluate customer data against a customized risk matrix. Data residency requires all data to be stored within India, encrypted in transit and storage. The architecture involves DC-DR setups ensuring high availability and business continuity. The system will maintain data isolation, employing compartmentalization techniques to separate NICL data from other clients. It will support real-time dashboards for KYC activities, maintain comprehensive audit trails, and integrate with various APIs for seamless data flow. The solution should also offer capabilities to store, filter, and review KYC applications based on specific requirements.", "error_handling": "Potential error scenarios may include issues with data extraction using Optical Character Recognition (OCR), failures in video verification leading to false negatives or positives, problems with API integrations, and compliance failures with IRDAI guidelines. Strategies for error handling should involve implementing robust logging and monitoring mechanisms, having contingency plans for data recovery and troubleshooting, performing end-to-end testing for data integrity, utilizing real-time dashboards to track and address issues promptly, and ensuring that the solution is secure from vulnerabilities. Additionally, the solution should provide mechanisms to manage customization issues, support for troubleshooting, and an audit trail to facilitate compliance checks.", "test_considerations": "Important testing aspects and areas to focus on for testing include: \n1. End-to-end testing of the application and integration to check for data and actions. \n2. Performance testing of the application. \n3. Ensuring compliance with IRDAI guidelines for KYC processes. \n4. Real-time feature testing including liveness checks to guard against spoofing and fraudulent manipulations. \n5. Validation of data residency to ensure all data is stored within India. \n6. Functionality testing of the customization of KYC verification workflow as per NICL's requirements. \n7. Testing of built-in centralized access-logs for security compliance purposes. \n8. Capability testing for fuzzy logic to match records for strict and loose matches. \n9. Verification of scalability under increased transaction volumes while maintaining high performance. \n10. Testing of the application for the encryption of data both in transit and storage. \n11. Audit trail testing to ensure comprehensive logging of all activities and associated results on the platform.", "input_data": {"required_input_data": ["CKYC (Central KYC) data", "eKYC (electronic Know your customer) data", "Officially Valid Documents (OVD) for customer verification", "Data extracted through Optical Character Recognition (OCR)", "Customer identity data for existing and prospective customers", "Live selfies for liveness checks", "Identity documents to verify with the selfie", "Files uploaded by customers for KYC completion", "API and SDK integration data for different KYC scenarios"]}, "output_data": "The desired output data includes: \n1. Real-time dashboards to track KYC activities.\n2. Encrypted data stored in transit and storage.\n3. A backend interface displaying customer applications filtered by risk score.\n4. A comprehensive audit trail of all activities performed on the platform.\n5. Transaction log and report for audit purposes, integrated with the dashboard module.\n6. Custom reports as required by NICL, with options for various filters.\n7. Real-time analytics on various metrics such as rejections, acceptance, and pending applications.\n8. Ability to generate downloadable/exportable reports.\n9. Information from the KYC verification workflow that can be customized per requirements.\n10. Live selfies and identity documents captured during the liveness test.\n11. Data outputs that can be customized for storage and integration with other systems.\n12. Results of checks and verifications performed during the KYC process.", "dependencies": "1. Compliance with IRDAI guidelines for KYC Process.\n2. Support for CKYC (Search and Download), Digilocker, Aadhar XML, or other OVDs for customer verification.\n3. Seamless, real-time, secured, and end-to-end encrypted process.\n4. Capability for liveness checks to prevent spoofing.\n5. Real-time dashboards for tracking KYC activities.\n6. Data Residency requirement; all data must be stored in India.\n7. Application security and maintenance support from the service provider.\n8. Compliance with latest regulatory authority guidelines.\n9. Training for NICL officials on proposed features and functionalities.\n10. Isolation of NICL’s environment from other clients.\n11. Compartamentalization techniques for data isolation.\n12. Dedicated cloud availability including various hosting options.\n13. High availability at both Data Center (DC) and Disaster Recovery (DR).\n14. A proper Business Continuity Plan with separate hosting in different seismic zones in India.\n15. Customization of UI interface according to NICL’s requirements.\n16. Contingency plans including disaster recovery measures.\n17. Centralized access-logs for security compliance at individual and application levels.\n18. Required documentation for system requirement specifications.\n19. Architectural documentation including High-Level and Low-Level Designs.\n20. End to end testing of the application including integration.\n21. Performance testing of the application.\n22. Utilizing standard frameworks for multi-platform development.\n23. IP whitelisting capability for restricted access.\n24. Scalability for future requirements.\n25. Responsiveness across various mobile/devices/form factors and operating systems.\n26. Ticketing-based support mechanism.\n27. High performance and scalability with increasing transaction volumes.\n28. Capability for matching records through fuzzy logic.\n29. Integration capability with E-Nach and BBPS if required.\n30. Future support for Video Based KYC.\n31. Adherence to Information Technology ACT, 2000 and amendments.\n32. Encrypted storage for data in transit and at rest.\n33. Customizable KYC verification workflow.\n34. Ability to accept file uploads from customers.\n35. Ready-to-integrate platform for KYC workflows.\n36. API and SDK integration for different KYC scenarios.\n37. Dashboard for reviewing customer KYC applications.\n38. Back end interface for displaying customer applications based on Risk Score.\n39. Business Rule Engine for automated decision-making.\n40. Comprehensive audit trail for platform activities.\n41. Logging and reporting for all transactions integrated with dashboard.\n42. Implementation of maker-checker workflow.\n43. Multi-channel ecosystem support for Distributors and DSAs.\n44. Unified interface for displaying all applications via Distributors and DSAs.\n45. Development of custom MIS reports with various filters.\n46. Real-time analytics for metrics: rejections, acceptance, and pending.\n47. Console for viewing summarized and detailed reports of notifications.\n48. Customizable reporting features as per NICL's requirements.\n49. Approach for bulk KYC processing.\n50. Dedicated support for the solution.\n51. Post-production support in coordination with the System Integrator.", "code_languages": "The document does not specify the implementation programming languages to be used in the project.", "frameworks": "Service provider shall utilize standard frameworks implementing best practices for multi-platform development with single codebase.", "algorithns": "The project will utilize Optical Character Recognition (OCR) for data extraction. Additionally, it includes the evaluation of data based on a customized risk matrix and will support algorithms for matching records through fuzzy logics for strict and loose matches. A Business Rule Engine will be integrated into the solution for automated decision-making.", "code_fragments": "The document describes a comprehensive online KYC process that includes sample functionalities and features such as: \n\n- Data extraction through Optical Character Recognition (OCR) \n- API integration for real-time customer verification \n- Support for CKYC, eKYC, and officially valid documents \n- Features for liveness checks to prevent spoofing \n- Real-time dashboards to track KYC activities \n- Requirements for data encryption, customization of KYC workflows, and capabilities for multi-channel ecosystems \n- Implementation of business rule engines for automated decision making \n- The system's capability to perform facial recognition and live document detection with specific goals related to identity verification \n\nThe focus is on minimizing human intervention while ensuring compliance with regulations and best practices."}}