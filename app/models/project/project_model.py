# app/models/project/project_model.py
from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, Field
from app.connection.establish_db_connection import get_mongo_db
from datetime import datetime

class ProjectFields(BaseModel):
    """Fields for project data"""
    design_ids: List[str] = Field(default_factory=list)
    designs: List[Dict] = Field(default_factory=list)
    # Add other common fields here

class ProjectModel(BaseModel):
    """Model for project data in MongoDB"""
    _id: str
    fields: ProjectFields = Field(default_factory=ProjectFields)

    class Config:
        populate_by_name = True

    @classmethod
    def _get_collection(cls):
        """Get MongoDB handler for projects collection"""
        return get_mongo_db(collection_name="projects_activity")

    # Generic CRUD Operations
    @classmethod
    async def add_one(cls, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Add a single document to the collection.
        
        Args:
            data (Dict[str, Any]): Document to insert
            
        Returns:
            Optional[Dict[str, Any]]: Inserted document or None
        """
        try:
            handler = cls._get_collection()
            # Ensure _id exists
            if "_id" not in data:
                return None
                
            result = await handler.create(data, handler.db)
            return result
        except:
            return None

    @classmethod
    async def get_one(cls, id: str) -> Optional[Dict[str, Any]]:
        """
        Get a document by ID.
        
        Args:
            id (str): Document ID
            
        Returns:
            Optional[Dict[str, Any]]: Found document or None
        """
        try:
            handler = cls._get_collection()
            result = await handler.get_one({"_id": id}, handler.db)
            if not result:
                return {
                    "_id": id,
                    "fields": {}
                }
            return result
        except:
            return None

    @classmethod
    async def update_one(cls, id: str, data: Dict[str, Any], upsert: bool = False) -> bool:
        """
        Update a single document.
        
        Args:
            id (str): Document ID
            data (Dict[str, Any]): Update data
            upsert (bool): Whether to insert if document doesn't exist
            
        Returns:
            bool: Success status
        """
        try:
            handler = cls._get_collection()
            result = await handler.update_one(
                {"_id": id},
                data,
                upsert=upsert,
                db=handler.db
            )
            return True if result else False
        except:
            return False

    @classmethod
    async def delete_one(cls, id: str) -> bool:
        """
        Delete a single document.
        
        Args:
            id (str): Document ID
            
        Returns:
            bool: Success status
        """
        try:
            handler = cls._get_collection()
            result = await handler.delete(id, handler.db)
            return True if result else False
        except:
            return False

    @classmethod
    async def upsert(cls, data: Dict[str, Any]) -> bool:
        """
        Update or insert a document.
        
        Args:
            data (Dict[str, Any]): Document data (must include _id)
            
        Returns:
            bool: Success status
        """
        try:
            if "_id" not in data:
                return False
                
            handler = cls._get_collection()
            result = await handler.update_one(
                {"_id": data["_id"]},
                data,
                upsert=True,
                db=handler.db
            )
            return True if result else False
        except:
            return False

    @classmethod
    async def get_many(cls, filter_query: Dict[str, Any], 
                      projection: Optional[Dict[str, Any]] = None,
                      sort: Optional[Dict[str, Any]] = None,
                      skip: int = 0,
                      limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get multiple documents based on query.
        
        Args:
            filter_query (Dict[str, Any]): Query filter
            projection (Optional[Dict[str, Any]]): Fields to include/exclude
            sort (Optional[Dict[str, Any]]): Sort criteria
            skip (int): Number of documents to skip
            limit (Optional[int]): Maximum number of documents to return
            
        Returns:
            List[Dict[str, Any]]: List of matching documents
        """
        try:
            handler = cls._get_collection()
            results = await handler.get_by_and_sort_with_pagination_projection(
                filter=filter_query,
                project=projection or {},
                sort_field=sort.get("field", "_id") if sort else "_id",
                page=skip,
                per_page=limit or 100,
                sort=sort.get("field", "_id") if sort else "_id",
                direction=sort.get("direction", "-1") if sort else "-1",
                db=handler.db
            )
            return results if results else []
        except:
            return []

    @classmethod
    async def update_field(cls, id: str, field_path: str, value: Any, upsert: bool = False) -> bool:
        """
        Update a specific field in a document.
        
        Args:
            id (str): Document ID
            field_path (str): Dot notation path to field (e.g., "fields.design_ids")
            value (Any): New value
            upsert (bool): Whether to create document if it doesn't exist
            
        Returns:
            bool: Success status
        """
        try:
            handler = cls._get_collection()
            update_data = {field_path: value}
            result = await handler.update_one(
                {"_id": id},
                update_data,
                upsert=upsert,
                db=handler.db
            )
            return True if result else False
        except:
            return False

    @classmethod
    async def add_to_array(cls, id: str, array_path: str, value: Any) -> bool:
        """
        Add a value to an array field.
        
        Args:
            id (str): Document ID
            array_path (str): Path to array field (e.g., "fields.design_ids")
            value (Any): Value to add
            
        Returns:
            bool: Success status
        """
        try:
            handler = cls._get_collection()
            result = await handler.push_to_array(
                filter={"_id": id},
                array_field=array_path,
                new_element=value,
                db=handler.db
            )
            return bool(result)
        except:
            return False

    @classmethod
    async def add_design_id(cls, project_id: str, design_id: str) -> bool:
        """Add a design ID to a project"""
        try:
            handler = cls._get_collection()
            
            # First ensure project exists with proper structure
            project = await cls.get_one(project_id)
            if not project:
                project = {
                    "_id": project_id,
                    "fields": {
                        "design_ids": [],
                        "designs": []
                    }
                }
                
            # Add the design_id if it doesn't exist
            if "design_ids" not in project["fields"]:
                project["fields"]["design_ids"] = []
            if design_id not in project["fields"]["design_ids"]:
                project["fields"]["design_ids"].append(design_id)
                
            # Ensure designs array exists
            if "designs" not in project["fields"]:
                project["fields"]["designs"] = []
                
            # Update the entire project document
            result = await handler.update_one(
                {"_id": project_id},
                project,
                upsert=True,
                db=handler.db
            )
            
            return True if result else False
        except Exception as e:
            print(f"Error in add_design_id: {str(e)}")
            return False

    @classmethod
    async def remove_design_id(cls, project_id: str, design_id: str) -> bool:
        """Remove a design ID from a project"""
        try:
            project = await cls.get_one(project_id)
            if not project:
                return False

            # Remove from design_ids array
            if "design_ids" in project.get("fields", {}):
                project["fields"]["design_ids"] = [
                    did for did in project["fields"]["design_ids"]
                    if did != design_id
                ]

            # Remove from designs array if it exists
            if "designs" in project.get("fields", {}):
                project["fields"]["designs"] = [
                    design for design in project["fields"]["designs"]
                    if design.get("id") != design_id
                ]

            return await cls.update_one(project_id, project)
        except:
            return False

    @classmethod
    async def get_design_ids(cls, project_id: str) -> List[str]:
        """Get all design IDs for a project"""
        try:
            project = await cls.get_one(project_id)
            if not project:
                return []
            return project.get("fields", {}).get("design_ids", [])
        except:
            return []