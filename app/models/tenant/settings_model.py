from typing import Any
from pydantic import BaseModel, Field
from app.connection.establish_db_connection import get_mongo_db
from app.core.Settings import settings

class SettingsFields(BaseModel):
    name: str = Field(default="")
    value: str = Field(default="")
    secure: bool = Field(default=False)

class IntegrationSettings(BaseModel):
    figma: list[SettingsFields] = Field(default=[SettingsFields(name="figma_api_key", value="*********************************************", secure=True)])

class TenantSettingsModel(BaseModel):
    integrations: IntegrationSettings = Field(default=IntegrationSettings())

class TenantSettings(BaseModel):
    id: str
    settings: TenantSettingsModel

    @classmethod
    def get_connection(cls):
        return get_mongo_db(db_name=settings.MONGO_DB_NAME, collection_name="settings")

    @classmethod
    async def get_settings(cls, tenant_id: str) -> TenantSettingsModel:
        db = cls.get_connection()
        settings = await db.get_one({"_id": tenant_id}, db.db)
        if not settings:
            # Return default settings if none exist
            return TenantSettingsModel()
        return TenantSettingsModel(**settings.get("settings", {}))
    
    @classmethod
    async def update_settings(cls, tenant_id: str, settings: TenantSettingsModel):
        db = cls.get_connection()
        await db.update_one(
            {"_id": tenant_id}, 
            {"settings": settings.dict()},
            upsert=True,
            db=db.db
        )
        return settings