from pydantic import BaseModel, Field
from typing import Optional

class GenericChat(BaseModel):
    message: str = Field(..., description="Message content")
    node_id: int = Field(..., description="ID of the node associated with the message")
    user_id: str = Field(..., description="ID of the user who sent the message")
    discussion_id: Optional[int] = Field(None, description="ID of the discussion if applicable")

    class Config:
        json_schema_extra = {
            "example": {
                "message": "Hello, this is a sample message.",
                "node_id": 123,
                "user_id": 456
            }
        }

class ChatContext(BaseModel):
    project_id: int = Field(..., description="ID of the project associated with the chat context")
    message: str = Field(..., description="Message content in the chat context")
    discussion_id: Optional[int] = Field(None, description="ID of the discussion if applicable")
    user_id: str = Field(..., description="unique ID to find the user")
    agent_name: str = Field(..., description="Agent name to identify during LLM interaction")
    class Config:
        json_schema_extra = {
            "example": {
                "project_id": 1,
                "message": "Hello, this is a sample message.",
                "discussion_id": 456
            }
        }