# notification_models.py

from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, Union
from datetime import datetime
from app.utils.datetime_utils import generate_timestamp
import uuid



class NotificationData(BaseModel):
    """Base model for custom notification data."""
    message: str
    link: str  # URL to the resource
    
class CodeGenerationNotificationData(NotificationData):
    """Custom data for code generation notifications."""
    project_id: int
    task_id: str
    design_id: int = None

class DiscussionNotificationData(NotificationData):
    """Custom data for discussion notifications."""
    assigner_id: str  # Person by whom added to the discussion
    discussion_id: int
    project_id: int
    
class TaskNotificationData(NotificationData):
    """Custom data for task notifications."""
    task_id: int
    task_type: str
    project_id: int
    assigner_id: str  # Person who assigned the task

class ProjectNotificationData(NotificationData):
    """Custom data for project notifications."""
    project_id: int
    assigner_id: str  # Person who created the project

class ApprovalNotificationData(NotificationData):
    """Custom data for approval notifications."""
    project_id: int
    initiator_id: str  # Person who initiated the approval
    approver_id: str  # Person who approved the request
    discussion_id: int

class NotificationModel(BaseModel):
    """Model for creating notifications."""
  
    receiver_id: str = Field(...)
    type: str = Field(...)  # e.g., "discussion", "task", "project"
    action: str = Field(...)  # e.g., "add", "update", "comment"
    created_at: str = Field(default_factory=generate_timestamp)
    is_read: bool = Field(default=False)
    data: Union[DiscussionNotificationData, TaskNotificationData, ProjectNotificationData, ApprovalNotificationData, CodeGenerationNotificationData] = Field(...)  # Use the appropriate subclass based on type
    class Config:
        arbitrary_types_allowed = True  # Allow Union types