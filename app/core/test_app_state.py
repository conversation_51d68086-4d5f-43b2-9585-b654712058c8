import os
from urllib.parse import urlparse, urlunparse

def app_state_callback(url, state=None):
    print("Url and state", url, state)
    try:
        if ":3000" in url:
            url = url.replace(":3000", ":3001")
        
        print("Url after replacement", url)
    except Exception as e: 
        print(f"Error in app_state_callback: {e}")
        # Log more detailed error information
        import traceback
        traceback.print_exc()

def change_host(url, new_host, upgrade_to_https=False):
    """
    Changes the host of a URL and optionally upgrades the protocol to HTTPS.
    
    Args:
        url (str): The original URL
        new_host (str): The new host to set
        upgrade_to_https (bool, optional): Whether to upgrade the URL to HTTPS. Defaults to False.
        
    Returns:
        str: The URL with the updated host as a string, or None if an error occurs
    """
    try:
        # Parse the URL
        parsed_url = urlparse(url)
        
        # Determine the scheme
        scheme = "https" if upgrade_to_https else parsed_url.scheme
        
        # Extract port if it exists in the original netloc
        port = ""
        if ':' in parsed_url.netloc:
            _, port_part = parsed_url.netloc.split(':', 1)
            port = f":{port_part}"
        
        # Extract port if it exists in the new_host
        if ':' in new_host:
            # If new_host already includes a port, use it and ignore the original port
            port = ""
        
        # Construct the new netloc with the new host and original port if applicable
        new_netloc = f"{new_host}{port}" if port and ':' not in new_host else new_host
        
        # Create the new URL parts
        new_parts = (
            scheme,
            new_netloc,
            parsed_url.path,
            parsed_url.params,
            parsed_url.query,
            parsed_url.fragment
        )
        
        # Construct and return the new URL
        print("New parts", new_parts)
        print("Returning new url", urlunparse(new_parts))
        return urlunparse(new_parts)
        
    except Exception as e:
        print(f"Error changing URL host: {e}")
        return None


def find_host(url):
    """
    Extracts the host from a URL, without the port.
    
    Args:
        url (str): The URL to extract the host from
        
    Returns:
        str: The host part of the URL, or None if an error occurs
    """
    try:
        # Parse the URL
        parsed_url = urlparse(url)
        
        # Extract the host part (without port if present)
        host = parsed_url.netloc.split(':')[0] if ':' in parsed_url.netloc else parsed_url.netloc
        print("Host extracted", host)
        return host
        
    except Exception as e:
        print(f"Error extracting host from URL: {e}")
        return None
       
if __name__ == "__main__":
    
    # Example usage
    app_state_callback("http://localhost:3000/some/path", state={"key": "value"})
    app_state_callback("http://localhost:3000/some/path")
    app_state_callback("http://localhost:3001/some/path")
    app_state_callback("http://localhost:3001/some/path", state={"key": "value"})
    change_host("http://localhost:3000/some/path", "newhost.com", upgrade_to_https=True)
    change_host("http://localhost:3000/some/path", "newhost.com")
    find_host("http://localhost:3000/some/path")
    find_host("https://vscode-internal-601-beta.beta.cloud.kavia.ai/?folder=/home/<USER>/workspace/code-generation/")