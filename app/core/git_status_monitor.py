
import re
import async<PERSON>
from threading import Thread
import logging
from datetime import datetime
from app.core.constants import TASKS_COLLECTION_NAME
from app.core.git_tools import EnhancedGitTools
from app.utils.datetime_utils import generate_timestamp
logger = logging.getLogger(__name__)

class GitStatusMonitor:
    def __init__(self, git_tool:EnhancedGitTools, ws_client,  db, task_id ,interval=20):
        self.git_tool = git_tool
        self.ws_client = ws_client
        self.interval = interval
        self.monitor_thread = None
        self.is_running = False
        self._last_status = None
        self._last_branch = None
        self.db = db
        self.task_id = task_id
        self.status_monitor = None
        self.current_base_path = None

    def set_current_base_path(self, base_path):
        self.current_base_path = base_path

    
    def set_git_tool(self, git_tool:EnhancedGitTools):
        self.git_tool = git_tool


        
    async def _check_status(self):
        try:
            if not self.current_base_path:
                logger.error("Current base path is not set")
                return
            status = self.git_tool.git_status(repository_path=self.current_base_path)
            branch_match = re.search(r'On branch ([^\n]+)', status)
            current_branch = branch_match.group(1) if branch_match else "unknown"

            
            # if current_branch != self._last_branch:
            #     try:
            #         self.db[TASKS_COLLECTION_NAME].update_one(
            #             {"_id": self.task_id},
            #             {"$set": {"branch_name": current_branch}}
            #         )
            #         logger.info(f"Updated MongoDB branch to: {current_branch}")
            #     except Exception as db_error:
            #         logger.error(f"MongoDB update failed: {db_error}")
                    
            # Send update regardless of changes to debug the issue
            logger.info(f"Sending git status update - Branch: {current_branch}")
            print(f"Sending git status update - Branch: {current_branch}")  # Add explicit print for debugging
            
            if self.ws_client and self.ws_client.connected:
                self.ws_client.send_message("git_status_update", {
                    "current_branch": current_branch,
                    "status": status,
                    "timestamp": generate_timestamp()
                })

            self._last_status = status
            self._last_branch = current_branch

        except Exception as e:
            logger.error(f"Error in git status check: {e}")
            print(f"Error in git status check: {e}")  # Add explicit print for debugging

    async def _monitor_loop(self):
        while self.is_running:
            await self._check_status()
            await asyncio.sleep(self.interval)

    def _run(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(self._monitor_loop())
        loop.close()

    def start(self):
        if not self.monitor_thread:
            self.is_running = True
            self.monitor_thread = Thread(target=self._run, daemon=True)
            self.monitor_thread.start()
            logger.info("Git status monitor started")
            print("Git status monitor started")  # Add explicit print for debugging

    def stop(self):
        self.is_running = False
        if self.monitor_thread:
            self.monitor_thread = None
            logger.info("Git status monitor stopped")
            print("Git status monitor stopped")  