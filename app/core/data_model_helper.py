import json
import os

base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
data_model_path = os.path.join(base_dir, 'app', 'core', 'datamodel.json')
print("base_path", data_model_path)

print()
data_model = {}
with open(data_model_path, 'r') as file:
    data_model = json.load(file)
class DataModelHelper:
    def __init__(self):
        self.data_model = data_model

    def get_child_node_types(self, node_type):
        node_info = self.data_model['model'].get(node_type, {})
        relationships = node_info.get('relationships', {})
        has_child = relationships.get('hasChild', {})
        return has_child.get('types', [])


#singleton 
data_model_helper = DataModelHelper()