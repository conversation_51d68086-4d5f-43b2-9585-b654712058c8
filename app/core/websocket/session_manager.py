# app/core/websocket/session_manager.py

import threading
from typing import Dict, Optional, Any
from app.core.websocket.client import WebSocketClient
from app.core.Settings import settings

class WebSocketSessionManager:
    """
    Global WebSocket session manager for handling client sessions across the application.
    Implemented as a thread-safe singleton.
    """
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(WebSocketSessionManager, cls).__new__(cls)
                # Initialize dictionary attributes without inline type annotations
                cls._instance._sessions = {}  # Will store Dict[str, WebSocketClient]
                cls._instance._metadata = {}  # Will store Dict[str, Dict[str, Any]]
            return cls._instance
    
    # Define proper type hint in method signature instead
    def get_session(self, session_id: str) -> Optional[WebSocketClient]:
        """
        Get a WebSocket session by ID.
        
        Args:
            session_id: The unique session identifier
            
        Returns:
            The WebSocket client if found, None otherwise
        """
        return self._sessions.get(session_id)
    
    def set_session(self, session_id: str, client: WebSocketClient, metadata: Dict[str, Any] = None) -> None:
        """
        Store a WebSocket session.
        
        Args:
            session_id: The unique session identifier
            client: The WebSocket client instance
            metadata: Optional metadata to associate with the session
        """
        self._sessions[session_id] = client
        if metadata:
            self._metadata[session_id] = metadata
    
    def delete_session(self, session_id: str) -> bool:
        """
        Delete a WebSocket session.
        
        Args:
            session_id: The unique session identifier
            
        Returns:
            True if the session was deleted, False if it wasn't found
        """
        if session_id in self._sessions:
            # Close the connection if it's open
            client = self._sessions[session_id]
            if client.connected:
                client.disconnect()
            
            # Remove from sessions
            del self._sessions[session_id]
            
            # Remove metadata if it exists
            if session_id in self._metadata:
                del self._metadata[session_id]
                
            return True
        return False
    
    def get_all_sessions(self) -> Dict[str, WebSocketClient]:
        """
        Get all active WebSocket sessions.
        
        Returns:
            Dictionary of all session IDs mapped to their WebSocket clients
        """
        return self._sessions.copy()
    
    def create_session(self, session_id: str, uri: str = None, metadata: Dict[str, Any] = None) -> WebSocketClient:
        """
        Create a new WebSocket session.
        
        Args:
            session_id: The unique session identifier
            uri: Optional WebSocket URI (defaults to settings.WEBSOCKET_URI)
            metadata: Optional metadata to associate with the session
            
        Returns:
            New WebSocketClient instance
        """
        # Use provided URI or default from settings
        websocket_uri = uri or settings.WEBSOCKET_URI
        
        # Create new client
        client = WebSocketClient(session_id, uri=websocket_uri)
        
        # Store in session manager
        self.set_session(session_id, client, metadata)
        
        return client
   
    def add_handler(self, session_id: str, handler) -> None:
        client : WebSocketClient = self.get_session(session_id)
        if client:
            client.add_message_handler(handler)
            client.start_message_handler()


    def update_session_metadata(self, session_id: str, metadata: Dict[str, Any]) -> None:
        """
        Update a WebSocket session's metadata.
        
        Args:
            session_id: The unique session identifier
            metadata: New metadata to associate with the session
        """
        self._metadata[session_id] = metadata
    
    def get_session_metadata(self, session_id: str) -> Dict[str, Any]:
        """
        Get a WebSocket session's metadata.
        
        Args:
            session_id: The unique session identifier
            
        Returns:
            Metadata associated with the session
        """
        return self._metadata.get(session_id, {})

    def close_all_sessions(self) -> None:
        """
        Close all active WebSocket sessions.
        """
        for session_id, client in list(self._sessions.items()):
            if client.connected:
                client.disconnect()
        
        self._sessions.clear()
        self._metadata.clear()
    
    def clean_disconnected_sessions(self) -> int:
        """
        Remove all disconnected sessions.
        
        Returns:
            Number of sessions removed
        """
        disconnected_sessions = [
            session_id for session_id, client in self._sessions.items()
            if not client.connected
        ]
        
        for session_id in disconnected_sessions:
            self.delete_session(session_id)
            
        return len(disconnected_sessions)