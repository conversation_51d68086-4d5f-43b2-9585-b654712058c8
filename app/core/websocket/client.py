# app/core/websocket/client.py

import asyncio
import json
import os
import time
import logging
import threading
from typing import Optional, Dict, Any
from queue import Queue, Empty
import websocket
import ssl


log_directory = "logs"
if not os.path.exists(log_directory):
    os.makedirs(log_directory)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
logger.propagate = False


class WebSocketThread:
    def __init__(self):
        self.loop = asyncio.new_event_loop()
        self._running_tasks = set()

    def run_coroutine(self, coro):
        """Run a coroutine in the thread's event loop"""
        if not self.loop.is_running():
            threading.Thread(target=self._run_loop, daemon=True).start()
        
        future = asyncio.run_coroutine_threadsafe(coro, self.loop)
        self._running_tasks.add(future)
        future.add_done_callback(self._running_tasks.discard)
        return future

    def _run_loop(self):
        """Run the event loop"""
        asyncio.set_event_loop(self.loop)
        self.loop.run_forever()

    def stop(self):
        """Stop all running tasks and the event loop"""
        for task in self._running_tasks:
            task.cancel()
        if self.loop.is_running():
            self.loop.call_soon_threadsafe(self.loop.stop)


class WebSocketClient:
    def __init__(self, task_id: str, uri: str = "ws://localhost:8765", client_type: str = "agent"):
        self.ws_thread = WebSocketThread()
        self.task_id = task_id
        self.uri = f"{uri}/{task_id}"
        self.client_type = client_type
        self.websocket: Optional[websocket.WebSocketApp] = None
        self.connected = False
        self._message_queue = Queue()
        self._connect_lock = threading.Lock()
        self._message_handlers = []
        self._handler_running = False 
        self._message_lock = asyncio.Lock()
        self._input_queue = Queue()
        self._last_message = None
        self._message_processors = []
        self._offline_mode = False
        self._reconnect_attempts = 0
        self._max_reconnect_attempts = 5
        self._reconnect_delay = 1
        self._last_connection_check = time.time()
        self._connection_check_interval = 5
        
        # WebSocket message queue for async compatibility
        self._async_message_queue = asyncio.Queue()
        self._ws_thread: Optional[threading.Thread] = None

    def _on_open(self, ws):
        """WebSocket connection opened callback"""
        logger.info(f"WebSocket connection opened to {self.uri}")
        
        # Register with the specified client type
        registration_message = {
            "type": self.client_type,
            "task_id": self.task_id
        }
        
        try:
            ws.send(json.dumps(registration_message))
            self.connected = True
            self._reconnect_attempts = 0
            self._reconnect_delay = 1
            self._offline_mode = False
            logger.info(f"Successfully registered as {self.client_type} for task {self.task_id}")
        except Exception as e:
            logger.error(f"Failed to register client: {e}")
            self.connected = False

    def _on_message(self, ws, message):
        """WebSocket message received callback"""
        try:
            parsed_message = json.loads(message)
            self._last_message = parsed_message
            
            # Put message in both sync and async queues
            self._message_queue.put(parsed_message)
            
            # Put in async queue for async handlers
            def put_async():
                try:
                    self._async_message_queue.put_nowait(parsed_message)
                except:
                    pass
            
            if self.ws_thread and self.ws_thread.loop:
                self.ws_thread.loop.call_soon_threadsafe(put_async)
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse message: {e}")
        except Exception as e:
            logger.error(f"Error handling message: {e}")

    def _on_error(self, ws, error):
        """WebSocket error callback"""
        logger.error(f"WebSocket error: {error}")
        self.connected = False

    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket connection closed callback"""
        logger.warning(f"WebSocket connection closed: {close_status_code} - {close_msg}")
        self.connected = False

    def reset_thread_and_reconnect(self):
        self.stop_message_handler()
        self.disconnect()
        if self.ws_thread:
            self.ws_thread.stop()
        self.ws_thread = WebSocketThread()
        if self.connect():
            self.start_message_handler(force_start=True)
            logger.info("WebSocket thread and handlers successfully reset and reconnected.")
        else:
            logger.error("Failed to reconnect after resetting WebSocket thread.")
            
    def connect(self) -> bool:
        """Connect to WebSocket server with improved error handling"""
        try:
            print("Starting connection attempt...")

            if self.connected:
                self.start_message_handler()
                print("Already connected, returning")
                return True
                
            retry_delay = self._reconnect_delay
            
            while self._reconnect_attempts < self._max_reconnect_attempts:
                try:
                    print(f"Attempt {self._reconnect_attempts + 1} of {self._max_reconnect_attempts}")
                    
                    # Close existing connection if any
                    if self.websocket:
                        try:
                            self.websocket.close()
                        except:
                            pass
                    
                    # Configure WebSocket
                    websocket.enableTrace(False)
                    
                    # Create WebSocket app with callbacks
                    self.websocket = websocket.WebSocketApp(
                        self.uri,
                        on_open=self._on_open,
                        on_message=self._on_message,
                        on_error=self._on_error,
                        on_close=self._on_close
                    )
                    
                    # Configure SSL if using wss://
                    sslopt = {}
                    if self.uri.startswith("wss://"):
                        sslopt = {
                            "cert_reqs": ssl.CERT_NONE,
                            "check_hostname": False,
                            "ssl_version": ssl.PROTOCOL_TLS
                        }
                    
                    # Start connection in separate thread
                    self._ws_thread = threading.Thread(
                        target=self.websocket.run_forever,
                        kwargs={
                            "sslopt": sslopt,
                            "ping_interval": 30,
                            "ping_timeout": 10,
                            "ping_payload": "ping"
                        },
                        daemon=True
                    )
                    self._ws_thread.start()
                    
                    # Wait for connection to be established
                    max_wait = 10  # seconds
                    start_time = time.time()
                    while not self.connected and time.time() - start_time < max_wait:
                        time.sleep(0.1)
                    
                    if self.connected:
                        print("Connection and registration successful")
                        self._reconnect_attempts = 0
                        self._reconnect_delay = 1
                        logger.info("Successfully connected to WebSocket server")
                        
                        # Create async message queue for this loop
                        async def create_async_queue():
                            self._async_message_queue = asyncio.Queue()
                        
                        self.ws_thread.run_coroutine(create_async_queue())
                        return True
                        
                except Exception as e:
                    print(f"Connection attempt failed: {str(e)}")
                    self._reconnect_attempts += 1
                    if self._reconnect_attempts < self._max_reconnect_attempts:
                        print(f"Waiting {retry_delay} seconds before next attempt...")
                        time.sleep(retry_delay)
                        retry_delay = min(retry_delay * 2, 30)
                    else:
                        print("Max reconnection attempts reached")
                        self._offline_mode = True
                        break

            return self._offline_mode  # Return True even in offline mode to allow operation
                
        except Exception as e:
            print(f"Outer exception occurred: {str(e)}")
            self._offline_mode = True
            return False

    def disconnect(self):
        """Disconnect from WebSocket server"""
        try:
            if self.websocket:
                self.websocket.close()
                
            if self._ws_thread and self._ws_thread.is_alive():
                self._ws_thread.join(timeout=2)
                
            self.connected = False
            self.websocket = None
            self._handler_running = False
                
        except Exception as e:
            pass

    def send_message(self, message_type: str, data: Dict[str, Any]):
        """Send a message with offline mode support"""
        if self._offline_mode:
            logger.info(f"Offline mode - Message queued: {message_type} - {json.dumps(data)}")
            return

        try:
            if self.websocket and self.connected:
                message = {
                    "type": message_type,
                    "task_id": self.task_id,
                    "data": data
                }
                self.websocket.send(json.dumps(message))
            else:
                if not self.connected:
                    self.connect()
                
                if self.connected and self.websocket:
                    message = {
                        "type": message_type,
                        "task_id": self.task_id,
                        "data": data
                    }
                    self.websocket.send(json.dumps(message))
                
        except Exception as e:
            logger.error(f"Error sending message: {str(e)}")
            self._offline_mode = True

    async def receive_message(self):
        """Receive a message from WebSocket"""
        if not self._offline_mode:
            try:
                # Get message from async queue
                message = await asyncio.wait_for(self._async_message_queue.get(), timeout=1.0)
                return message
            except asyncio.TimeoutError:
                return None
            except Exception as e:
                logger.error(f"Error receiving message: {e}")
                self.connected = False
                return None
        return None

    def _monitor_connection(self):
        """Continuously monitor WebSocket connection"""
        while self._handler_running:
            current_time = time.time()
            
            if current_time - self._last_connection_check >= self._connection_check_interval:
                self._last_connection_check = current_time
                
                if not self.connected and not self._offline_mode:
                    self._reconnect_attempts = 0
                    if self.reset_thread_and_reconnect():
                        logger.info("Successfully reconnected to WebSocket")
                
            time.sleep(1)

    async def handle_messages(self):
        """Handle incoming messages with improved error handling"""
        while self._handler_running:
            try:
                if self.websocket and self.connected:
                    async with self._message_lock:
                        # Process any pending input first
                        while not self._input_queue.empty():
                            try:
                                input_data = self._input_queue.get_nowait()
                                for handler in self._message_handlers:
                                    if asyncio.iscoroutinefunction(handler):
                                        await handler({
                                            "type": "user_input",
                                            "input": input_data
                                        })
                                    else:
                                        handler({
                                            "type": "user_input",
                                            "input": input_data
                                        })
                            except Empty:
                                break

                        # Then process received message
                        message = await self.receive_message()
                        if message:
                            for handler in self._message_handlers:
                                if asyncio.iscoroutinefunction(handler):
                                    await handler(message)
                                else:
                                    handler(message)

            except Exception as e:
                logger.error(f"Error in message handler: {e}")
                self.connected = False
                if not self.connect():
                    break
            
            await asyncio.sleep(0.1)

    def start_message_handler(self, force_start=False):
        """Start message handler with connection monitoring"""
        if not self._handler_running or force_start:
            self._handler_running = True
            self.ws_thread.run_coroutine(self.handle_messages())

    def stop_message_handler(self):
        """Stop message handler"""
        self._handler_running = False

    def add_message_handler(self, handler):
        """Add message handler"""
        if handler not in self._message_handlers:
            self._message_handlers.append(handler)

    def remove_message_handler(self, handler):
        """Remove message handler"""
        if handler in self._message_handlers:
            self._message_handlers.remove(handler)

    def add_message_processor(self, processor):
        """Add message processor"""
        if processor not in self._message_processors:
            self._message_processors.append(processor)

    def cleanup(self):
        """Cleanup WebSocket thread"""
        self.stop_message_handler()
        self.disconnect()
        if self.ws_thread:
            self.ws_thread.stop()