import os
import json
import asyncio
import uuid
from openai import OpenAI
from app.connection.establish_db_connection import connect_mongo_db, get_node_db, get_vector_db
from app.routes.node_route import get_node, get_nodes, delete_node, get_child_nodes, get_associated_nodes
from app.utils.chat_utils import functions as f_list
from app.core.Settings import settings
import logging
from app.connection.establish_db_connection import get_mongo_db
import re


client = OpenAI(api_key=settings.OPENAI_API_KEY)


  
class Chat:

    instruction = '''
        "You are a helpful assistant who can assist a user in interacting with a system
        that supports various functions related to node management. 
        If a user inquires about creating nodes, such as projects or products, 
        please guide the user through the process of creating these nodes. 
        The system supports multiple node types, and your role is to engage the user in
        understanding their intentions clearly and invoking the necessary functions to
        accomplish their intent.

        If the user has not provided all the required parameters to invoke a function,
        you should make the user aware of the missing parameters and ask the user to 
        provide those missing parameters. Focus solely on understanding the function 
        content and respond back to the user. If the user does not provide enough
        parameters, you need to ask for them before processing the function calls.
        Do not create any sample nodes yourself. Instead, ask the user to provide
        all the necessary details for the node creation."
        
        Don't ask any extra question to user which is related to node. Just except the requriment for the functions and invoke the functions then provide the result to user.
    
        Don't show any node id to user and Don't ask any node id to user while doing conversation.
        Don't ask any confirmation to user.
        
        if user wants to create association between project and product, please call the create_node_association function.
        '''

    global mongo_handler
    mongo_handler = get_mongo_db(collection_name='confirmation')

    def __init__(self) -> None:

        self.node_db = get_node_db()
        self._logger = logging.getLogger("GraphInspector")
        
    async def load_nodes(self, items):

        formatted_data = "Here are the available nodes:\n"
        for node_type in items:
            items = await get_nodes(node_type, db=get_node_db())

            if items:
                for i in items:
                    _id = i['id']
                    _name = i['properties']['Name']
                    formatted_data += f"- Name: {_name}, Type: {node_type}, ID: {_id}\n"

        return formatted_data

    async def getParamsDetails(self, function_name, function_args):
        node_types = ['project', 'product', 'Project', 'Product']
        node_type = function_args.get("node_type")
        node_id = function_args.get("node_id")
    
        if function_name == 'create_node':

            name = function_args.get("name")
            description = function_args.get("description")

            if node_type in node_types and not name and not description:
                return False, False
            
            params = {
                'request': {
                    'node_type': node_type.capitalize(),
                    'name': name,
                    'description': description
                }
            }

            details = {
                'Node Type': node_type,
                'Name': name,
                'Description': description
            }

        elif function_name == "update_node":
            
            if not node_id and node_type in node_types and not name and not description:
                return False, False
            
            params = {
                'node_id': node_id,
                'request': {
                    'node_type': node_type,
                    'properties': {
                        'Name': name,
                        'Description': description
                    }
                }
            }

            details = {
                'Node Type': node_type,
                'Name': name,
                'Description': description
            }

        elif function_name == "delete_node":
            
            if not node_id and node_type in node_types:
                return False, False
            
            call_fn = await get_node(node_id, node_type.capitalize(), get_node_db())
            
            if call_fn:
                params = {
                    'node_id': node_id,
                    'node_type': call_fn['labels'][0]
                }

                details = {
                    'Id': node_id,
                    'Name': call_fn['properties']['Name']
                }
            else:
                return False, False
        
        elif function_name == "create_workitem":
            title = function_args.get("title")
            description = function_args.get("description")
            project_id = function_args.get("project_id")
            
            if not title and not description and not project_id:
                return False, False
            
            rootWorkItem = await get_child_nodes(int(project_id), 'Project', 'WorkItemRoot', db=get_node_db())
            root_node_id = rootWorkItem[0]['id']
            # project_name = rootWorkItem[0]['properties']['Name']
            result = await get_node(int(project_id), 'Project', get_node_db())
            print(result)
            params = {
                'request': {
                    'node_type': "WorkItem",
                    'name': title,
                    'description': description,
                    'properties': {
                        "Title": title,
                        "Description": description,
                        "Type": "WorkItem",
                        "parent_id": root_node_id
                    }
                }
            }

            details = {
                'Project Name': result['properties']['Name'],
                'Node Type': 'Work Item',
                'Title': title,
                'Description': description,
            }
        
        elif function_name == "update_workitem":
            workitem_id = function_args.get('workitem_id')
            title = function_args.get("title")
            description = function_args.get("description")
            project_id = function_args.get("project_id")
            
            if not workitem_id and not title and not description and not project_id:
                return False, False
            
            rootWorkItem = await get_child_nodes(int(project_id), 'Project', 'WorkItemRoot', db=get_node_db())
            root_node_id = rootWorkItem[0]['id']
            
            params = {
                'node_id': workitem_id,
                'request': {
                    'node_type': 'WorkItem',
                    'properties': {
                        'Title': title,
                        'Description': description,
                        'Type': 'WorkItem',
                        'parent_id': root_node_id
                    }
                }
            }

            details = {
                'Node Type': 'Work Item',
                'Title': title,
                'Description': description
            }
            
        elif function_name == "delete_workitem":
            workitem_id = function_args.get("workitem_id")
            if not workitem_id:
                return False, False
            
            call_fn = await get_node(int(workitem_id), "WorkItem", get_node_db())
            
            if call_fn:
                params = {
                    'node_id': workitem_id,
                    'node_type': call_fn['labels'][0]
                }

                details = {
                    'Id': workitem_id,
                    'Name': call_fn['properties']['Title']
                }
            else:
                return False, False  
        
        elif function_name == "create_requirement":
            title = function_args.get("title")
            description = function_args.get("description")
            product_id = function_args.get("product_id")
            
            if not title and not description and not product_id:
                return False, False
            
            rootWorkItem = await get_child_nodes(int(product_id), 'Product', 'RequirementRoot', db=get_node_db())
            root_node_id = rootWorkItem[0]['id']
            # project_name = rootWorkItem[0]['properties']['Name']
            result = await get_node(int(product_id), 'Project', get_node_db())

            params = {
                'request': {
                    'node_type': "Epic",
                    'name': title,
                    'description': description,
                    'properties': {
                        "Title": title,
                        "Description": description,
                        "Type": "Epic",
                        "parent_id": root_node_id
                    }
                }
            }

            details = {
                'Product Name': result['properties']['Name'],
                'Node Type': 'Epic',
                'Title': title,
                'Description': description,
            }
        
        elif function_name == "update_requirement":
            requirement_id = function_args.get('requirement_id')
            title = function_args.get("title")
            description = function_args.get("description")
            product_id = function_args.get("product_id")
            
            if not requirement_id and not title and not description and not product_id:
                return False, False
            
            rootWorkItem = await get_child_nodes(int(product_id), 'Product', 'Requirement', db=get_node_db())
            root_node_id = rootWorkItem[0]['id']
            
            params = {
                'node_id': requirement_id,
                'request': {
                    'node_type': 'Epic',
                    'properties': {
                        'Title': title,
                        'Description': description,
                        'Type': 'Epic',
                        'parent_id': root_node_id
                    }
                }
            }

            details = {
                'Node Type': 'Epic',
                'Title': title,
                'Description': description
            }
            
        elif function_name == "delete_requirement":
            requirement_id = function_args.get("requirement_id")
            if not requirement_id:
                return False, False
            
            call_fn = await get_node(int(requirement_id), "Epic", get_node_db())
            
            if call_fn:
                params = {
                    'node_id': requirement_id,
                    'node_type': call_fn['labels'][0]
                }

                details = {
                    'Id': requirement_id,
                    'Name': call_fn['properties']['Title']
                }
            else:
                return False, False 
            
        return params, details

    async def getFunctions(self,function_name, function_args):

        if function_name == "get_node":
            node_id = function_args.get("node_id")
            node_type = function_args.get('node_type')
            if node_id and node_type:
                result = await get_node(node_id, node_type.capitalize(), get_node_db())
            else:
                result = "Node id and type is not found."

        elif function_name == "get_nodes":
            node_type = function_args.get("node_type")
            result = await get_nodes(node_type.capitalize(), db=get_node_db())
            if result:
                data = result
                result = ""
                for node in data:
                    node_id = node['id']
                    node_name = node['properties']['Name']
                    result += "node_type" + node_type + "id : " + \
                        str(node_id) + "name" + node_name

        elif function_name == "list_work_items":
            node_id = function_args.get('node_id')
            node_type = function_args.get('node_type')
            child_node_type = "WorkItemRoot"

            if node_id and node_type:
                rootWorkItem = await get_child_nodes(int(node_id), node_type.capitalize(), child_node_type, db=get_node_db())
                root_node_id = rootWorkItem[0]['id']
                result = await get_child_nodes(int(root_node_id), 'WorkItem', 'WorkItem', db=get_node_db())
            else:
                result = "please give the node id or node types"

        elif function_name == "list_product_epics":
            node_id = function_args.get('node_id')
            node_type = function_args.get('node_type')
            child_node_type = "Requirement"
            
            if node_id and node_type:
                rootWorkItem = await get_child_nodes(int(node_id), node_type.capitalize(), child_node_type, db=get_node_db())
                root_node_id = rootWorkItem[0]['id']
                result = await get_child_nodes(int(root_node_id), 'RequirementRoot', 'Requirement', db=get_node_db())
            else:
                result = "please give the node id or node types"
        
        elif function_name == "delete_all_nodes":
            node_type = function_args.get('node_type')
            result = await self.node_db.delete_nodes_by_label([node_type.capitalize()])
            
        return result
    
    async def list_functions(self, messages):
        node_types = ['project', 'product', 'Project', 'Product']
        asking_confirmation = False

        # Make the API request
        response = client.chat.completions.create(model="gpt-4o-mini",
                                                  messages=messages,
                                                  functions=f_list)

        # Extract the assistant's reply
        assistant_reply = response.choices[0].message

        # Check if the assistant's reply includes a function call
        if assistant_reply.function_call:

            # Extract the function name and arguments from the function call
            function_name = assistant_reply.function_call.name
            function_args = json.loads(assistant_reply.function_call.arguments)

            print("Yes, it's a function call : ", function_name)
            confirmation = ['create_node', 'update_node', 'delete_node', 'create_workitem','update_workitem','delete_workitem', 'create_requirement', 'update_requirement', 'delete_requirement']
            get_functions = ['get_node', 'get_nodes', 'list_work_items', 'list_product_epics', 'delete_all_nodes']
            
            #confirmation
            if function_name in confirmation:

                # dynamic function calling.
                node_type = function_args.get("node_type")
                params, details = await self.getParamsDetails(
                    function_name, function_args)

                if params and details:
                    
                    creation = ['create_workitem', 'create_requirement']
                    
                    if function_name in creation:
                        function_name = 'create_node'
                    
                    updation = ['update_workitem', 'update_requirement']
                    if function_name in updation:
                        function_name = 'update_node'
                    
                    deletion = ['delete_workitem', 'delete_requirement']
                    
                    if function_name in deletion:
                        function_name = 'delete_node'

                    
                    # Assumes function name format is "operation_entity"
                    operation = function_name.split('_')[0]

                    data = {
                        'operation': operation,
                        'function': function_name,
                        'type': node_type,
                        'is_active': 1,
                        'parameters': params,
                        'details': details
                    }

                    mongo_result = await mongo_handler.create({'function_call': data}, mongo_handler.db)

                    # append the mongo_result _id as the task_id to that data variable
                    data["task_id"] = str(mongo_result['_id'])
                    return False, False, data

                result = f"Wrong data received. Please provide the correct values for {function_name}"

            #directly getting the data
            elif function_name in get_functions:    
                result = await self.getFunctions(function_name, function_args) 
            
            #it's used to call the front-end component
            elif function_name == "configure_node":
                data = {
                    'function': function_name
                }
                return False, False, data
            
            else:
                result = "Sorry, functionality not implemented yet"
            
            messages.append({
                "role": "function",
                "name": function_name,
                "content": str(result),
            })

            return True, messages, asking_confirmation
        else:
            return False, assistant_reply, asking_confirmation

    async def functions_call(self, messages):

        functions, messages, asking_confirmation = await self.list_functions(messages)

        if functions == True:
            stream = True
            messages = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=messages,
                functions=None,
                response_format={"type": "text"},
                temperature=0.2,
                stream=stream
            )

            return stream, messages, asking_confirmation

        return False, messages, asking_confirmation
 
    async def generic_chat(self, user_id, message, discussion_id, node_id):

        # get the discussion id from the request
        # not implement, need some discussion
        domain = settings.DOMAIN
        if not user_id and not message:
            yield f"data: {json.dumps({'content': 'Please provide the user_id and message.'})}\n\n"

        if node_id == None:

            project_nodes = await get_nodes("Project", db=get_node_db())
            product_nodes = await get_nodes("Product", db=get_node_db())

            if not project_nodes and not product_nodes:
                yield f"data: {json.dumps({'content': 'Please create a project or product to start conversation.'})}\n\n"
            else:
                yield f"data: {json.dumps({'content': 'Please choose a project or product to start conversation.'})}\n\n"

            if project_nodes:
                for i, project in enumerate(project_nodes):
                    project_id = project['id']
                    project_name = project['properties']['Name']
                    temp = json.dumps(
                        {'content': f'<br>{i+1}. <a class="mr-4" style="color: blue" href="/panel/project/{project_id}">{project_name}</a>'})

                    yield f"data: {temp}\n\n"

            if product_nodes:
                for i, product in enumerate(product_nodes):
                    product_id = product['id']
                    product_name = product['properties']['Name']
                    temp = json.dumps(
                        {'content': f'<br>{i+1}. <a class="mr-4" style="color: blue" href="panel/product/{product_id}">{product_name}</a>'})

                    yield f"data: {temp}\n\n"

            yield f"event: stop\n"
            yield f"data: stopped\n\n"

        # load the previous conversation
        if discussion_id:
            discussion_node = await self.node_db.get_node_by_id(int(discussion_id))
            discussion_so_far = json.loads(
                discussion_node['properties'].get('Discussion'))
            print("Discussion do far :" + str(len(discussion_so_far)))
            if len(discussion_so_far) > 20:
                yield f"data: {json.dumps({'content': 'Discussion limit reached'})}\n\n"
                yield f"event: stop\n"
                yield f"data: stopped\n\n"
            messages = discussion_so_far

        else:

            nodes = ['Project', 'Product']
            node_data = await self.load_nodes(nodes)

            system_prompt = f'''
            You will answer a question based on a knowledge base in the given format.
            node_type : project or product only
            
            while listing the node names. please use the example url for only node navigation : {domain}/panel/[node_type]/[node_id]\n
            while listing the workitems. please use the example url : {domain}/panel/project/[node_id]/workitem/[work_item_id]\n
            To view full workitems : {domain}/panel/project/[node_id]/workitem\n
            
            Current available nodes listed below : {str(node_data)}\n
            Current node id : {str(node_id)} and pick the node type from the above result.\n 
            Instruction : {Chat.instruction}
            
            Source: [the information from a relevant paragraph returned from node db]
            Link: [link for the listed above]
            
            USER QUESTION
            [user question]

            OUTPUT FORMAT
            In your answer,For every source from the knowledge base you are using, add its link to the nodes.
            
            '''

            messages = [
                {
                    "role": "system",
                    "content": system_prompt
                }
            ]

            # check user_is associated with node
            user = await self.node_db.check_user_under_node(int(node_id), user_id)
            if not user:
                user = await self.node_db.create_node(['user'], {'user_id': user_id})
                link_user_id = user.get('id')
                await self.node_db.create_relationship(int(node_id), user['id'], "HAS_USER")
            else:
                link_user_id = user[0]['id']

        # after receiving the user message
        if message:
            nodes = ['Project', 'Product']
            node_data = await self.load_nodes(nodes)

            messages.append({
                "role": "user",
                "content":  message
            })

            llm_response = ""
            stream, msg, asking_confirmation = await self.functions_call(messages)

            if asking_confirmation:

                # asking for confirmation
                yield f"data: {json.dumps({'function_call':  asking_confirmation})}\n\n"

                yield f"event: stop\n"
                yield f"data: stopped\n\n"

            elif stream:

                for chunk in msg:
                    if chunk.choices[0].delta.content:
                        llm_response += chunk.choices[0].delta.content
                        yield f"data: {json.dumps({'content': llm_response})}\n\n"
                        

                messages.append({
                    "role": "assistant",
                    "content": llm_response
                })

                # start discussion
                if not discussion_id:
                    await self.node_db.create_node(['chat'], {'Discussion': json.dumps(messages)}, link_user_id)

                # update discussion
                if discussion_id:
                    await self.node_db.update_node_by_id(int(discussion_id), {'Discussion': json.dumps(messages)})

                yield f"event: stop\n"
                yield f"data: stopped\n\n"

            else:

                messages.append({
                    "role": "assistant",
                    "content": msg.content
                })

                # start discussion
                if not discussion_id:

                    discussion = await self.node_db.create_node(['chat', 'chatNode'], {'Discussion': json.dumps(messages)}, link_user_id)
                    discussion_id = discussion.get("id")
                    yield f"data: {json.dumps({'discussion_id': + discussion_id})}\n\n"

                # update discussion
                if discussion_id:
                    await self.node_db.update_node_by_id(int(discussion_id), {'Discussion': json.dumps(messages)})

                # stream discussions
                for chunk in msg:
                    if chunk[1]:
                        llm_response = ""
                        for word in chunk[1].split():
                            llm_response += " "+ word
                            yield f"data: {json.dumps({'content': ' ' + llm_response})}\n\n"
                            

                    yield f"event: stop\n"
                    yield f"data: stopped\n\n"

        else:

            yield f"Data: Message not provided\n\n"
            yield f"event: stop\n"
            yield f"data: stopped\n\n"
