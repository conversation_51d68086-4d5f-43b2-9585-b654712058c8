from py2neo import Graph, Node, Relationship
import configparser
import os
import asyncio
import time
from app.models.user_model import ProjectMember
from datetime import datetime
from typing import Any, Dict, List
import csv
import os
import json


# Define the path to the CSV file
LOG_FILE_PATH = 'query_log.csv'


class InspectorDB:
    def __init__(self,uri,user,password):
        
        self.uri = uri
        self.user = user
        self.password = password

        self.graph = Graph(uri ,auth=(user, password))

        self.node_type_mapping = {
            "Project": {
                "api_fields": ["Title", "Description"],
                "db_fields": ["ProjectName", "ProjectDescription"]
            },
            # Add more mappings as needed
        }

    async def async_run(self, query, **params):
        retries = 3  # Number of retries
        base_delay = 1  # Base delay in seconds
        start_time = time.time()
        for attempt in range(retries):
            try:
                result = await asyncio.to_thread(self.graph.run, query, **params)
                end_time = time.time()
                
                return result  # Return the result on successful execution
            except Exception as e:
                print(f"Error running query on attempt {attempt+1}: {e}")
                
                if "Cannot connect" in str(e):  # Broaden the condition slightly
                    print("Re-initializing graph connection")
                    self.graph = Graph(self.uri, auth=(self.user, self.password))
                    
                elif attempt < retries - 1:
                    sleep_time = base_delay * (2 ** attempt)  # Exponential backoff
                    print(f"Retrying in {sleep_time} seconds...")
                    await asyncio.sleep(sleep_time)
                else:
                    print("All retry attempts failed.")
                    raise e  # Optionally re-raise the last exception after all retries fail

        return None  # You can decide what to return if all retries fail, if anything at all
    
    #get graph view
    async def get_graph_nodes(self, node_id: int, node_types: list = [], max_depth: int = 4):
        def format_node(node_data):
            if node_data.get("properties", {}).get("Type") == "Discussion":
                return None  # Exclude Discussion nodes
            return {
                "id": str(node_data.get("id")),
                "label": node_data.get("properties", {}).get("Title", None),
                "type": node_data.get("properties", {}).get("Type", None),
                "title": node_data.get("properties", {}).get("Title", None),
            }

        if node_types:
            node_types_str = '|'.join([f'{label}' for label in node_types])
            node_types_clause = f'(m:{node_types_str})'
        else:
            node_types_clause = '(m)'

        query = f"""
            MATCH path = (n)-[*1..{max_depth}]-{node_types_clause}
            WHERE ID(n) = $node_id
            WITH nodes(path) AS nodes, relationships(path) AS rels
            UNWIND nodes AS node
            UNWIND rels AS rel
            RETURN DISTINCT {{
                id: ID(node),
                label: COALESCE(node.name, ''),
                type: labels(node)[0],
                title: node.name,
                properties: properties(node)
            }} AS nodeInfo,
            {{
                from: ID(startNode(rel)),
                to: ID(endNode(rel)),
                id: ID(rel),
                label: type(rel)
            }} AS edgeInfo
        """

        query_result = await self.async_run(query, node_id=node_id)

        nodes = {}
        edges = []

        for row in query_result:
            node_info, edge_info = row
            formatted_node = format_node(node_info)
            if formatted_node:
                node_id = str(node_info['id'])
                nodes[node_id] = formatted_node

            edge = {
                "from": str(edge_info['from']),
                "to": str(edge_info['to']),
                "id": str(edge_info['id']),
                "label": edge_info['label']
            }
            edges.append(edge)

        return {
            "nodes": list(nodes.values()),
            "edges": edges
        }
        
    async def get_graph_code_view(self, node_type, name=None, limit=1):
        def format_node(node_info):
            return {
                "id": str(node_info["id"]),
                "label": node_info["label"],
                "type": node_info["type"],
                "properties": node_info["properties"]
            }

        def format_edge(edge_info):
            return {
                "from": str(edge_info["from"]),
                "to": str(edge_info["to"]),
                "id": str(edge_info["id"]),
                "label": edge_info["label"]
            }

        # Define the base query
        query = """
            MATCH (n:{node_type}:py {name_filter})
            WITH n
            ORDER BY n.name
            LIMIT $limit
            OPTIONAL MATCH path = (n)-[*1..1]->(f)
            WHERE NOT 'Function' IN labels(f)
            WITH n, 
                CASE WHEN path IS NULL 
                    THEN [n] 
                    ELSE nodes(path) 
                END AS nodes, 
                CASE WHEN path IS NULL 
                    THEN [] 
                    ELSE relationships(path) 
                END AS rels
            UNWIND nodes AS node
            RETURN DISTINCT {{
                id: ID(node),
                label: COALESCE(node.name, node.Title, ''),
                properties: properties(node),
                type: CASE
                    WHEN '{node_type}' IN labels(node) THEN '{node_type}'
                    WHEN 'Module' IN labels(node) THEN 'Module'
                    WHEN 'Class' IN labels(node) THEN 'Class'
                    WHEN 'Function' IN labels(node) THEN 'Function'
                    ELSE head(labels(node))
                END
            }} AS nodeInfo,
            CASE WHEN rels IS NOT NULL AND size(rels) > 0
                THEN [rel IN rels | {{
                    from: ID(startNode(rel)),
                    to: ID(endNode(rel)),
                    id: ID(rel),
                    label: type(rel)
                }}]
                ELSE []
            END AS edgeInfos
        """

        # Add name filter if provided
        name_filter = f"{{name: '{name}'}}" if name else ""
        query = query.format(node_type=node_type, name_filter=name_filter)

        query_result = await self.async_run(query, limit=limit)

        nodes = {}
        edges = []

        for record in query_result:
            node_info = record["nodeInfo"]
            edge_infos = record["edgeInfos"]

            node_id = str(node_info["id"])
            if node_id not in nodes:
                nodes[node_id] = format_node(node_info)

            for edge_info in edge_infos:
                edges.append(format_edge(edge_info))

        result = {
            "nodes": list(nodes.values())
        }

        if edges:
            result["edges"] = edges

        return result
        
    async def get_node_by_id(self, node_id):
        match_label= "(n)"
            
        query = f"MATCH {match_label} WHERE ID(n)  = $node_id RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query, node_id=node_id)

        result = query_result.data()
        if result and len(result) > 0:
            result = result[0]
            return result
        return []
    
    # async def get_graph_nodes_and_edges(self, depth_level: int, node_types: List[str]):
    #     def format_node(node_data):
    #         return {
    #             "id": str(node_data["id"]),
    #             "label": node_data["properties"].get("name", "") or node_data["properties"].get("Title", ""),
    #             "type": node_data["labels"][0],
    #             "properties": node_data["properties"]
    #         }

    #     def format_edge(edge_data):
    #         return {
    #             "id": str(edge_data["id"]),
    #             "from": str(edge_data["start_node"]),
    #             "to": str(edge_data["end_node"]),
    #             "type": edge_data["type"]
    #         }

    #     # Construct the node type filter
    #     node_type_filter = " OR ".join([f"n:{node_type}" for node_type in node_types])
        
    #     query = f"""
    #     MATCH path = (root:SuperRoot)-[*1..{depth_level}]->(n)
    #     WHERE {node_type_filter}
    #     WITH nodes(path) AS nodes, relationships(path) AS rels
    #     UNWIND nodes AS node
    #     WITH DISTINCT node, rels
    #     RETURN {{
    #         id: ID(node),
    #         labels: labels(node),
    #         properties: properties(node)
    #     }} AS nodeInfo,
    #     [rel IN rels WHERE startNode(rel) = node OR endNode(rel) = node | {{
    #         id: ID(rel),
    #         start_node: ID(startNode(rel)),
    #         end_node: ID(endNode(rel)),
    #         type: type(rel)
    #     }}] AS edgeInfos
    #     """

    #     query_result = await self.async_run(query)
        
    #     nodes = {}
    #     edges = set()

    #     for record in query_result:
    #         node_info = record["nodeInfo"]
    #         edge_infos = record["edgeInfos"]

    #         node_id = str(node_info["id"])
    #         if node_id not in nodes:
    #             nodes[node_id] = format_node(node_info)

    #         for edge_info in edge_infos:
    #             edges.add(json.dumps(format_edge(edge_info)))

    #     result = {
    #         "nodes": list(nodes.values()),
    #         "edges": [json.loads(edge) for edge in edges]
    #     }

    #     return result

    async def get_old_graph_nodes_and_edges(self, depth_level: int, node_types: List[str]):
        def format_node(node_data):
            return {
                "id": str(node_data["id"]),
                "label": node_data["properties"].get("name", "") or node_data["properties"].get("Title", ""),
                "type": node_data["labels"][0],
                "properties": node_data["properties"]
            }

        def format_edge(edge_data):
            return {
                "id": str(edge_data["id"]),
                "from": str(edge_data["start_node"]),
                "to": str(edge_data["end_node"]),
                "type": edge_data["type"]
            }

        # Construct the node type filter
        node_type_filter = " OR ".join([f"n:`{node_type}`" for node_type in node_types])

        query = f"""
        MATCH path = (root:ProjectRoot)-[*1..{depth_level}]->(n)
        WHERE root:ProjectRoot OR ({node_type_filter})
        WITH nodes(path) AS nodes, relationships(path) AS rels
        UNWIND nodes AS node
        WITH DISTINCT node, rels
        RETURN {{
            id: ID(node),
            labels: labels(node),
            properties: properties(node)
        }} AS nodeInfo,
        [rel IN rels WHERE startNode(rel) = node OR endNode(rel) = node | {{
            id: ID(rel),
            start_node: ID(startNode(rel)),
            end_node: ID(endNode(rel)),
            type: type(rel)
        }}] AS edgeInfos
        """
        query_result = await self.async_run(query)
 
        nodes = {}
        edges = set()

        for record in query_result:
            node_info = record["nodeInfo"]
            edge_infos = record["edgeInfos"]

            node_id = str(node_info["id"])
            if node_id not in nodes:
                nodes[node_id] = format_node(node_info)

            for edge_info in edge_infos:
                edges.add(json.dumps(format_edge(edge_info)))

        result = {
            "nodes": list(nodes.values()),
            "edges": [json.loads(edge) for edge in edges]
        }

        return result
    
    async def get_new_graph_nodes_and_edges(self, depth_level: int, node_types: List[str]):
        def format_node(node_data):
            return {
                "id": str(node_data["id"]),
                "label": node_data["properties"].get("name", "") or node_data["properties"].get("Title", ""),
                "type": node_data["labels"][0],
                "title": node_data["properties"].get("name", ""),
                "neighbors": [],
                "links": []
            }

        def format_edge(edge_data):
            return {
                "id": str(edge_data["id"]),
                "source": str(edge_data["start_node"]),
                "target": str(edge_data["end_node"]),
                "type": edge_data["type"]
            }

        # Construct the node type filter
        node_type_filter = " OR ".join([f"n:`{node_type}`" for node_type in node_types])

        query = f"""
        MATCH path = (root:ProjectRoot)-[*1..{depth_level}]->(n)
        WHERE root:ProjectRoot OR ({node_type_filter})
        WITH nodes(path) AS nodes, relationships(path) AS rels
        UNWIND nodes AS node
        WITH DISTINCT node, rels
        RETURN {{
            id: ID(node),
            labels: labels(node),
            properties: properties(node)
        }} AS nodeInfo,
        [rel IN rels WHERE startNode(rel) = node OR endNode(rel) = node | {{
            id: ID(rel),
            start_node: ID(startNode(rel)),
            end_node: ID(endNode(rel)),
            type: type(rel)
        }}] AS edgeInfos
        """
        query_result = await self.async_run(query)
 
        nodes = {}
        edges = set()

        for record in query_result:
            node_info = record["nodeInfo"]
            edge_infos = record["edgeInfos"]

            node_id = str(node_info["id"])
            if node_id not in nodes:
                nodes[node_id] = format_node(node_info)

            for edge_info in edge_infos:
                edges.add(json.dumps(format_edge(edge_info)))

        result = {
            "nodes": list(nodes.values()),
            "links": [json.loads(edge) for edge in edges]
        }

        return result

    async def get_frontend_graph_view(self, limit=500):
        def format_node(node_info):
            return {
                "id": str(node_info["id"]),
                "label": node_info["label"],
                "type": node_info["type"],
                "properties": node_info["properties"]
            }

        def format_edge(edge_info):
            return {
                "from": str(edge_info["from"]),
                "to": str(edge_info["to"]),
                "id": str(edge_info["id"]),
                "label": edge_info["label"]
            }

        query = """
            MATCH (n)
            WITH n
            LIMIT $limit
            OPTIONAL MATCH (n)-[r]->(m)
            RETURN {
                id: ID(n),
                label: COALESCE(n.name, n.Title, ''),
                properties: properties(n),
                type: head(labels(n))
            } AS nodeInfo,
            CASE WHEN r IS NOT NULL
                THEN [{
                    from: ID(n),
                    to: ID(m),
                    id: ID(r),
                    label: type(r)
                }]
                ELSE []
            END AS edgeInfos
        """

        query_result = await self.async_run(query, limit=limit)

        nodes = {}
        edges = set()

        for record in query_result:
            node_info = record["nodeInfo"]
            edge_infos = record["edgeInfos"]

            node_id = str(node_info["id"])
            if node_id not in nodes:
                nodes[node_id] = format_node(node_info)

            for edge_info in edge_infos:
                edges.add(json.dumps(format_edge(edge_info)))

        result = {
            "nodes": list(nodes.values()),
            "edges": [json.loads(edge) for edge in edges]
        }

        return result
        
    async def newgraph_search_nodes(self, node_id: int, search_text: str) -> Dict[str, List[Dict[str, Any]]]:
        print(f"Searching for node_id: {node_id}, search_text: {search_text}")

        def format_node(node_data: Dict[str, Any]) -> Dict[str, Any]:
            properties = node_data.get("properties", {})
            print(properties)
            return {
                "id": str(node_data.get("id")),
                "label": properties.get("name", ""),
                "type": properties.get("type", ""),
                "title": properties.get("name", ""),
                "neighbors": [],
                "links": []
            }

        def format_link(link_id: int, start_node_id: int, end_node_id: int, relationship_type: str) -> Dict[str, Any]:
            return {
                "id": str(link_id),
                "source": str(start_node_id),
                "target": str(end_node_id),
                "type": relationship_type
            }

        escaped_search_text = search_text.replace("'", "\\'")

        query = f"""
        MATCH (p)-[*]->(n)
        WHERE NOT (n:Discussion) AND n.name =~ '(?i).*{escaped_search_text}.*'
        WITH COLLECT(n) AS nodes
        UNWIND nodes AS n
        OPTIONAL MATCH (n)-[r]->(m)
        WHERE m IN nodes
        RETURN n, ID(n) as id, ID(m) as target_id, TYPE(r) as relationship_type, ID(r) as link_id
        """
        query_result = await self.async_run(query)

        formatted_nodes = []
        links = []
        node_map = {}

        # Process the nodes and relationships
        for record in query_result:
            node = record["n"]
            node_id = record["id"]
            target_id = record.get("target_id")
            relationship_type = record.get("relationship_type")
            link_id = record.get("link_id")

            if node_id not in node_map:
                node_data = {"id": node_id, "properties": dict(node)}
                formatted_node = format_node(node_data)
                formatted_nodes.append(formatted_node)
                node_map[node_id] = formatted_node

            # Process relationships and create links
            if target_id and relationship_type and link_id:
                link = format_link(link_id, node_id, target_id, relationship_type)
                links.append(link)

        result = {
            "nodes": formatted_nodes,
            "links": links
        }

        return result
    
    async def oldgraph_search_nodes(self, node_id: int, search_text: str) -> Dict[str, List[Dict[str, Any]]]:
        print(f"Searching for node_id: {node_id}, search_text: {search_text}")

        def format_node(node_data: Dict[str, Any]) -> Dict[str, Any]:
            properties = node_data.get("properties", {})
            print(properties)
            return {
                "id": str(node_data.get("id")),
                "label": properties.get("name", ""),
                "type": properties.get("type", ""),
                "properties": properties,
            }

        def format_link(link_id: int, start_node_id: int, end_node_id: int, relationship_type: str) -> Dict[str, Any]:
            return {
                "id": str(link_id),
                "from": str(start_node_id),
                "to": str(end_node_id),
                "type": relationship_type
            }

        escaped_search_text = search_text.replace("'", "\\'")

        query = f"""
        MATCH (p)-[*]->(n)
        WHERE NOT (n:Discussion) AND n.name =~ '(?i).*{escaped_search_text}.*'
        WITH COLLECT(n) AS nodes
        UNWIND nodes AS n
        OPTIONAL MATCH (n)-[r]->(m)
        WHERE m IN nodes
        RETURN n, ID(n) as id, ID(m) as target_id, TYPE(r) as relationship_type, ID(r) as link_id
        """
        query_result = await self.async_run(query)

        formatted_nodes = []
        links = []
        node_map = {}

        # Process the nodes and relationships
        for record in query_result:
            node = record["n"]
            node_id = record["id"]
            target_id = record.get("target_id")
            relationship_type = record.get("relationship_type")
            link_id = record.get("link_id")

            if node_id not in node_map:
                node_data = {"id": node_id, "properties": dict(node)}
                formatted_node = format_node(node_data)
                formatted_nodes.append(formatted_node)
                node_map[node_id] = formatted_node

            # Process relationships and create links
            if target_id and relationship_type and link_id:
                link = format_link(link_id, node_id, target_id, relationship_type)
                links.append(link)

        result = {
            "nodes": formatted_nodes,
            "edges": links
        }

        return result