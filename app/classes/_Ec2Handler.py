import json
from typing import Any, Dict, Optional
from urllib.parse import urlencode
import urllib3
import boto3
from botocore.exceptions import ClientError
from app.core.Settings import settings


class Ec2Handler:

    def __init__(self):
        self.http = urllib3.PoolManager(retries=urllib3.Retry(total=3, backoff_factor=0.1))
        self.ec2_client = boto3.client(
            'ec2',
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )
        self.cloudwatch = boto3.client(
            'cloudwatch',
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )
        self.READY_STATES = ['ReadyState1', 'ReadyState2', 'ReadyState3']
        self.AMI_ID = 'ami-036d8af171baef742'
        self.INSTANCE_TYPE = 't3.large'
        self.SECURITY_GROUP_ID = 'sg-09c88ecd8dbc64411'
        self.SUBNET_ID = 'subnet-0d9e5a0ef8e431281'

    def get_project(self, name: str):
        try:
            instances = self.get_instances_by_name(name)
            if instances:
                for instance in instances:
                    state = instance['State']['Name']
                    instance_id = instance['InstanceId']
                    if state in ['running', 'stopped']:
                        if state == 'stopped':
                            self.start_instance(instance_id)
                        ip_address = self.get_instance_ip(instance_id)
                        return self.response_format(ip_address)
            ready_instance = self.get_ready_state_instance()
            if ready_instance:
                return self.handle_ready_instance(ready_instance, name)
            self.trigger_ready_state_creation()
            return {"message": "No instances available. Creating new ReadyState instances. Please try again after a few minutes!"}
        except Exception as e:
            return {
                "statusCode": 500,
                "body": json.dumps({"error": "Internal server error"})
            }

    def get_instances_by_name(self, name: str) -> Optional[list]:
        try:
            response = self.ec2_client.describe_instances(
                Filters=[{'Name': 'tag:Name', 'Values': [name]},{'Name': 'instance-state-name', 'Values': ['running', 'stopped']}]
            )
            instances = []
            for reservation in response['Reservations']:
                for instance in reservation['Instances']:
                    instances.append({
                        'InstanceId': instance['InstanceId'],
                        'State': instance['State'],
                        'Tags': instance.get('Tags', [])
                    })
            return instances
        except ClientError as e:
            print(f"Error retrieving instances by name {name}: {str(e)}")
            return None

    def check_instance_availability(self, project_name: str) -> Dict[str, Any]:
        try:
            instances = self.get_instances_by_name(project_name)
            instance_state = None

            for instance in instances:
                state = instance['State']['Name']
                if state in ['running', 'stopped']:
                    instance_state = state
                    break

            if instances and instance_state in ['stopped', 'running']:
                instance_id = instance['InstanceId']
                return{
                        'available': True,
                        'status': instance_state,
                        'ip': self.get_instance_ip(instance_id)
                    }
            else:
                return {
                        'available': False,
                        'status': 'not available'
                    }
        except Exception as e:
            print(f"An error occurred in check_instance_availability: {str(e)}")
            return {
                "statusCode": 500,
                "body": json.dumps({"error": "Internal server error"})
            }

    def handle_fastapi_action(self, project_id: str, action: str, query_params: Dict[str, str], stage: str) -> Dict[str, Any]:
        try:
            instance_info = self.get_or_create_instance(stage + str(project_id))
            if not instance_info:
                return {"error": "Failed to get or create instance"}

            ip_info = self.response_format(instance_info['ip_address'])
            server_url = ip_info['server']

            if action == 'start':
                fastapi_response = self.call_fastapi_endpoint(server_url, 'start', {
                    'project_id': project_id,
                    'task_id': query_params.get('task_id'),
                    'architecture_id': query_params.get('architecture_id'),
                    'stage': query_params.get('stage',stage)
                })
            elif action == 'stop':
                fastapi_response = self.call_fastapi_endpoint(server_url, 'stop')
            elif action == 'status':
                fastapi_response = self.call_fastapi_endpoint(server_url, 'status')
            elif action == 'view_log':
                fastapi_response = self.call_fastapi_endpoint(server_url, 'view_log', {
                    'log_type': query_params.get('log_type')
                })
            elif action == 'clone_repo':
                fastapi_response = self.call_fastapi_endpoint(server_url, 'clone_repo', {
                    'repo_url': query_params.get('repo_url')
                })
                fastapi_response = {
                    "path": fastapi_response.get('path')
                }
            else:
                return {"error": f"Invalid action: {action}"}

            combined_response = {**ip_info, **fastapi_response}
            return combined_response
        except Exception as e:
            print(f"An error occurred in handle_fastapi_action: {str(e)}")
            return {"error": "Internal server error"}

    def response_format(self, ip_address: str) -> Dict[str, str]:
        return {
            "ip": ip_address,
            "iframe": f"https://{ip_address}.nip.io",
            "server": f"https://8765.{ip_address}.nip.io"
        }

    def call_fastapi_endpoint(self, server_url: str, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        url = f"{server_url}/{endpoint}"
        if params:
            url += f"?{urlencode(params)}"
        print(f"Sending request to FastAPI: URL={url}")
        try:
            response = self.http.request('GET', url, timeout=30.0)
            if response.status != 200:
                raise Exception(f"FastAPI returned status code {response.status}")
            return json.loads(response.data.decode('utf-8'))
        except (urllib3.exceptions.MaxRetryError, urllib3.exceptions.TimeoutError, Exception) as e:
            print(f"Error calling FastAPI endpoint: {str(e)}")
            return {"error": f"Failed to call FastAPI endpoint: {str(e)}. URL: {url}"}

    def get_or_create_instance(self, project_name: str) -> Optional[Dict[str, Any]]:
        print('called',project_name)
        try:
            instances = self.get_instances_by_name(project_name)
            print(instances)
            if instances:
                for instance in instances:
                    state = instance['State']['Name']
                    instance_id = instance['InstanceId']
                    if state in ['running', 'stopped']:
                        if state == 'stopped':
                            self.start_instance(instance_id)
                        ip_address = self.get_instance_ip(instance_id)
                        return {'instance_id': instance_id, 'ip_address': ip_address}
            else:
                ready_instance = self.get_ready_state_instance()
                print(ready_instance)
                if ready_instance:
                    instance_id = ready_instance['InstanceId']
                    self.rename_instance(instance_id, project_name)
                    state = ready_instance['State']['Name']
                    if state == 'stopped':
                        self.start_instance(instance_id)
                    ip_address = self.get_instance_ip(instance_id)
                    self.trigger_ready_state_creation()
                    return {'instance_id': instance_id, 'ip_address': ip_address}
                else:
                    self.trigger_ready_state_creation()
                    return None
        except Exception as e:
            print(f"An error occurred in get_or_create_instance: {str(e)}")
            return None

    def get_instance_ip(self, instance_id: str) -> Optional[str]:
        try:
            response = self.ec2_client.describe_instances(InstanceIds=[instance_id])
            instance = response['Reservations'][0]['Instances'][0]
            ip_address = instance.get('PublicIpAddress')
            print(f"Instance {instance_id} IP address: {ip_address}")
            return ip_address
        except ClientError as e:
            print(f"Error retrieving IP for instance {instance_id}: {str(e)}")
            return None

    def start_instance(self, instance_id: str) -> None:
        try:
            self.ec2_client.start_instances(InstanceIds=[instance_id])
            print(f"Starting instance {instance_id}")
            waiter = self.ec2_client.get_waiter('instance_running')
            waiter.wait(InstanceIds=[instance_id])
            print(f"Instance {instance_id} is now running")
        except ClientError as e:
            print(f"Error starting instance {instance_id}: {str(e)}")

    def get_ready_state_instance(self) -> Optional[Dict[str, Any]]:
        try:
            response = self.ec2_client.describe_instances(
                Filters=[
                    {'Name': 'tag:Name', 'Values': self.READY_STATES},
                    {'Name': 'instance-state-name', 'Values': ['stopped', 'running']}
                ]
            )
            instances = []
            for reservation in response['Reservations']:
                for instance in reservation['Instances']:
                    instances.append(instance)
            return instances[0] if instances else None
        except ClientError as e:
            print(f"Error retrieving ReadyState instances: {str(e)}")
            return None

    def rename_instance(self, instance_id: str, new_name: str) -> None:
        try:
            self.ec2_client.create_tags(
                Resources=[instance_id],
                Tags=[{'Key': 'Name', 'Value': new_name}]
            )
            print(f"Renamed instance {instance_id} to {new_name}")
        except ClientError as e:
            print(f"Error renaming instance {instance_id}: {str(e)}")

    def trigger_ready_state_creation(self) -> None:
        try:
            response = self.ec2_client.describe_instances(
                Filters=[
                    {'Name': 'tag:Name', 'Values': self.READY_STATES},
                    {'Name': 'instance-state-name', 'Values': ['stopped', 'running', 'pending']}
                ]
            )
            existing_instances = set()
            for reservation in response['Reservations']:
                for instance in reservation['Instances']:
                    for tag in instance.get('Tags', []):
                        if tag['Key'] == 'Name':
                            existing_instances.add(tag['Value'])

            for ready_state_name in self.READY_STATES:
                if ready_state_name not in existing_instances:
                    print(f"ReadyState instance {ready_state_name} not found, triggering creation.")
                    self.create_new_ready_state_async(ready_state_name)
                else:
                    print(f"ReadyState instance {ready_state_name} already exists.")
        except ClientError as e:
            print(f"Error checking ReadyState instances: {str(e)}")

    def create_new_ready_state_async(self, ready_state_name: str) -> None:
        try:
            result = self.create_new_ready_state(ready_state_name)
            print(f"Result of creating ReadyState instance {ready_state_name}: {result}")
        except Exception as e:
            print(f"Error creating ReadyState instance {ready_state_name}: {str(e)}")

    def create_new_ready_state(self, ready_state_name: str) -> Dict[str, Any]:
        try:
            response = self.ec2_client.run_instances(
                ImageId=self.AMI_ID,
                InstanceType=self.INSTANCE_TYPE,
                MinCount=1,
                MaxCount=1,
                SecurityGroupIds=[self.SECURITY_GROUP_ID],
                SubnetId=self.SUBNET_ID,
                TagSpecifications=[{
                    'ResourceType': 'instance',
                    'Tags': [{'Key': 'Name', 'Value': ready_state_name}]
                }]
            )
            instance_id = response['Instances'][0]['InstanceId']
            print(f"Created new ReadyState instance {ready_state_name} with ID: {instance_id}")

            waiter = self.ec2_client.get_waiter('instance_running')
            waiter.wait(InstanceIds=[instance_id])
            print(f"Instance {instance_id} is now running")

            self.create_cpu_alarm(instance_id)

            ip_address = self.get_instance_ip(instance_id)
            if not ip_address:
                raise Exception(f"Failed to get IP address for instance {instance_id}")

            return {
                "statusCode": 200,
                "body": json.dumps({
                    "message": f"Created new ReadyState instance {ready_state_name}",
                    "instance_id": instance_id,
                    "ip_address": ip_address
                })
            }
        except ClientError as e:
            error_message = f"Error creating new ReadyState instance: {str(e)}"
            print(error_message)
            return {
                "statusCode": 500,
                "body": json.dumps({"error": error_message})
            }
        except Exception as e:
            error_message = f"Unexpected error creating new ReadyState instance: {str(e)}"
            print(error_message)
            return {
                "statusCode": 500,
                "body": json.dumps({"error": error_message})
            }

    def handle_ready_instance(self, ready_instance: Dict[str, Any], project_name: str) -> Dict[str, Any]:
        try:
            instance_state = ready_instance['State']['Name']
            instance_id = ready_instance['InstanceId']
            
            # Rename the instance
            self.rename_instance(instance_id, project_name)
            
            # Start the instance if it's stopped
            if instance_state == 'stopped':
                self.start_instance(instance_id)
            
            # Get the IP address of the instance
            ip_address = self.get_instance_ip(instance_id)
            
            if not ip_address:
                raise Exception(f"Failed to get IP address for instance {instance_id}")
            
            # Trigger the creation of a new ready state instance to replace this one
            self.trigger_ready_state_creation()
            
            return {
                "statusCode": 200,
                "body": json.dumps({
                    "message": f"Ready instance {instance_id} prepared for project {project_name}",
                    "instance_id": instance_id,
                    "ip_address": ip_address
                })
            }
        except Exception as e:
            error_message = f"Error handling ready instance: {str(e)}"
            print(error_message)
            return {
                "statusCode": 500,
                "body": json.dumps({"error": error_message})
            }

    def create_cpu_alarm(self, instance_id: str, threshold: int = 1, period: int = 300, evaluation_periods: int = 6) -> None:
        try:
            alarm_name = f"checking-activity-{instance_id}"
            self.cloudwatch.put_metric_alarm(
                AlarmName=alarm_name,
                AlarmDescription='Alarm for checking inactivity based on CPUUtilization',
                ActionsEnabled=True,
                AlarmActions=[f'arn:aws:automate:{settings.AWS_REGION}:ec2:stop'],
                MetricName='CPUUtilization',
                Namespace='AWS/EC2',
                Statistic='Average',
                Dimensions=[{'Name': 'InstanceId', 'Value': instance_id}],
                Period=period,
                EvaluationPeriods=evaluation_periods,
                Threshold=threshold,
                ComparisonOperator='LessThanOrEqualToThreshold',
                TreatMissingData='missing'
            )
            print(f"Created CloudWatch alarm {alarm_name} for instance {instance_id}")
        except ClientError as e:
            print(f"Error creating CloudWatch alarm for instance {instance_id}: {str(e)}")

    def delete_instance(self, instance_id: str) -> Dict[str, Any]:
        try:
            self.ec2_client.terminate_instances(InstanceIds=[instance_id])
            print(f"Terminated instance {instance_id}")
            return {
                "statusCode": 200,
                "body": json.dumps({"message": f"Instance {instance_id} terminated successfully"})
            }
        except ClientError as e:
            error_message = f"Error terminating instance {instance_id}: {str(e)}"
            print(error_message)
            return {
                "statusCode": 500,
                "body": json.dumps({"error": error_message})
            }

    def list_all_instances(self) -> Dict[str, Any]:
        try:
            response = self.ec2_client.describe_instances()
            instances = []
            for reservation in response['Reservations']:
                for instance in reservation['Instances']:
                    instances.append({
                        'InstanceId': instance['InstanceId'],
                        'State': instance['State']['Name'],
                        'InstanceType': instance['InstanceType'],
                        'PublicIpAddress': instance.get('PublicIpAddress', 'N/A'),
                        'Tags': instance.get('Tags', [])
                    })
            return {
                "statusCode": 200,
                "body": json.dumps({"instances": instances})
            }
        except ClientError as e:
            error_message = f"Error listing instances: {str(e)}"
            print(error_message)
            return {
                "statusCode": 500,
                "body": json.dumps({"error": error_message})
            }
        
