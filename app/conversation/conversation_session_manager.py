import uuid
from datetime import datetime
from typing import Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from app.connection.establish_db_connection import get_mongo_db
from app.core.Settings import Settings

settings = Settings()

async def get_chat_session(user_id: str, project_id: int, discussion_id: int) -> Optional[dict]:
    """
    Retrieve the most recent active conversation session for a user and project.
    
    Args:
        user_id: The ID of the user
        project_id: The ID of the project
    
    Returns:
        Optional[dict]: The session discussion if found, None otherwise
    """
    try:
        # Connect to MongoDB
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='chat_discussion'
        )
        
        # Query for the most recent active session using find_one
        session = await mongo_handler.get_one(
            filter={
                "user_id": user_id,
                "project_id": project_id,
                "discussion_id": discussion_id,
                "status": "active"
            },
            db=mongo_handler.db
        )
        
        return session  # This will already be None if no session is found
        
    except Exception as e:
        # Instead of raising an HTTP exception, log the error and return None
        print(f"Error retrieving ingest session: {str(e)}")
        return None

async def create_chat_session(user_id: str, project_id: int) -> str:
    """
    Create a new chat session.
    
    Args:
        user_id: The ID of the user
        project_id: The ID of the project
    
    Returns:
        dict: session
    """
    try:
        # Create a new session id
        session_id = str(uuid.uuid4())
        
        # Connect to MongoDB
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='chat_discussion'
        )
        
        # Create session document
        session = {
            "session_id": session_id,
            "user_id": user_id,
            "project_id": project_id,
            "discussion_id": None,
            "created_at": datetime.now(),
            "status": "active"
        }
        
        # Insert into MongoDB
        await mongo_handler.insert(
            session,
            db=mongo_handler.db
        )
        
        return session
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error managing ingest session: {str(e)}"
        )

async def update_chat_session(session_id:str, session):
    try:
        # Connect to MongoDB
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='chat_discussion'
        )
        
        await mongo_handler.update(session['_id'], session, db=mongo_handler.db)

        return

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error managing ingest session: {str(e)}"
        )

async def get_or_create_chat_session(user_id: str, project_id: int, discussion_id: int) -> dict:
    """
    Get existing session or create new one, returning full session details.
    
    Args:
        user_id: The ID of the user
        project_id: The ID of the project
    
    Returns:
        dict: The complete session dictionary
    """
    try:
        # First try to get existing session
        existing_session = await get_chat_session(user_id, project_id, discussion_id)
        if existing_session:
            return existing_session
            
        # If no existing session, create new one
        new_session = await create_chat_session(user_id, project_id)
        return new_session
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error in session management: {str(e)}"
        )