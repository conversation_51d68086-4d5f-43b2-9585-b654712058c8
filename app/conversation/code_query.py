import asyncio
import os
import time
import argparse
import json
import logging
import litellm
from app.connection.establish_db_connection import get_node_db
from app.conversation.conversation_finder import ConversationFinder
from app.utils.conversation_utils import ConversationUtils
from app.utils.kg_inspect.knowledge_helper import Knowledge_Helper, Reporter
from app.utils.kg_inspect.knowledge import Knowledge, KnowledgeCodeBase
from app.utils.kg_inspect.kg_tool import KgTools
from app.utils.logs_utils import get_path
from llm_wrapper.utils.base_tools import DynamicToolFactory, ToolRegistry
from llm_wrapper.core.llm_interface import LLMInterface


class CodeQuery:
    def __init__(self, context):
        self.node_db = get_node_db()
        self.context = context
        self.utils = ConversationUtils(context)
        self.chat = ConversationFinder(context)
        self._type = 'code_query'
        
    async def _answer_question(self,):
        
        _messages = []
        if self.context.discussion_id:
            _messages = await self.utils._load_discussion(self.context.discussion_id,count=10)
            
        else:
            self.context.discussion_id = await self.chat._create_conversation(self._type, self.context.user_id)
               
        system_prompt = "You are an expert software engineer."
        
        user_prompt = 'Your task is to answer questions about a software project.  Use the provided tools to get ' \
                    'information about the project codebase and to determine what files in the project are relevant to the question. ' \
                    'Use read_file to directly read relevant files in order to ensure your answers are accurate.\n ' \
                    'For example, if you use find_relevant_files with a set of search terms and this returns a ' \
                    'large number of files you might use get_source_file_knowledge to get more information about some ' \
                    'likely files to decide which are most promising, then use read_file to read these files ' \
                    'to get the best information on which to base your answer. If the information you gather suggests there may '  \
                    'be additional sources of information then do additional find_relevant_files searches and reads ' \
                    'so that your answer can be based on comprehensive information.  If your answer contains multiline text be sure ' \
                    'to format it properly for JSON.\n' \
                    f'The current question is: {self.context.message}\n' \
                    'Please format your final response as a JSON object with the following structure:\n' \
                    '{{ ' \
                    '   "answer": "<answer to question>"\n' \
                    '}}\n' \
                    'Your final response MUST be a valid JSON object with NO OTHER non-JSON content.'

        try:

            litellm.drop_params=True
            model_name = "gpt-4o" #need to get it dynamically
            
            general_registry = ToolRegistry()
            general_registry.register_tool("KgTools", KgTools)
            
            general_factory = DynamicToolFactory(general_registry)
            general_exec_agent = general_factory.create_dynamic_tool(["KgTools"])
            kg_tool = general_exec_agent('/test', logger=None, user_id='123')
            
            llm = LLMInterface(str(get_path()), 'knowledge', self.context.user_id, self.context.project_id, self._type)

            response = await llm.llm_interaction_wrapper(
                messages=_messages,
                user_prompt=user_prompt,
                system_prompt=system_prompt,
                model=model_name,
                stream=False,
                response_format={"type": "json_object"},
                function_schemas=kg_tool.function_schemas,
                function_executor=kg_tool.function_executor
            )
            
            print('\n'+response["answer"]+'\n')
            
            yield f"data: {json.dumps({'content': response['answer'], 'discussion_id': self.context.discussion_id})}\n\n"
        except Exception as e:
            print(f"Exception {e}")

    def _setup_logger(self, name, base_path, log_level=logging.INFO):
        """
        Set up a logger with a file handler, preventing logs from going to the console.

        :param name: Name of the logger and the log file
        :param base_path: Base path for the log directory
        :param log_level: Logging level (default: logging.INFO)
        :return: Configured logger
        """
        logging.disable(logging.NOTSET)
        logger = logging.getLogger(name)
        logger.setLevel(log_level)

        # Remove any existing handlers (including the default StreamHandler)
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # Prevent the logger from propagating messages to the root logger
        logger.propagate = False

        # Create logs directory if it doesn't exist
        log_dir = os.path.join(base_path, "logs")
        os.makedirs(log_dir, exist_ok=True)

        # Create file handler
        file_handler = logging.FileHandler(os.path.join(log_dir, f"{name}.log"))
        file_handler.setLevel(log_level)

        # Create formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        # Add file handler to logger
        logger.addHandler(file_handler)
        logger.propagate = False

        return logger

    def _parse_codebase( arg ):
        if ":" not in arg:
            raise argparse.ArgumentTypeError("Codebase must be in the format 'name:path'")
        name, path = arg.split(":", 1)
        return KnowledgeCodeBase(path, name)