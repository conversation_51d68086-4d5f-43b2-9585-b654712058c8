from datetime import datetime
import json
from typing import Any, List
from typing import Any, Callable, Dict, Generator, List, AsyncGenerator
from app.connection.establish_db_connection import get_node_db
from app.core.Settings import settings
from app.models.chat_model import ChatContext
import logging
from app.utils.conversation_utils import ConversationUtils
from llm_wrapper.utils.base_tools import DynamicToolFactory, ToolRegistry
from llm_wrapper.core.llm_interface import LLMInterface
from app.connection.establish_db_connection import get_mongo_db
from kavia.inspector.prompt.schema import GRAPH_SCHEMA
from kavia.tools.inspector_tool import InspectorTools
from kavia.utils.pathutils import get_path
from importlib import resources
from jinja2 import Environment, BaseLoader
from app.connection.establish_db_connection import get_mongo_db

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]  # This ensures only one StreamHandler
)

# Create a logger instance
logger = logging.getLogger(__name__)

class CodeinspectorAgent:
    
    def __init__(self, context: ChatContext) -> None:
        self.node_db = get_node_db()
        self.utils = ConversationUtils(context)
        

        # system prompt for graph inspector
        template_content = resources.read_text('kavia.inspector.prompt', 'inspector.prompt')
        template = Environment(loader=BaseLoader()).from_string(template_content)
        self.system_prompt = template.render(graph_schema=GRAPH_SCHEMA, branch= 'develop', repo= 'graphnode-backend-api')
        self.model_name = 'gpt-4-turbo'
        
    async def _submit_completion(self, context, messages: List[Any]) -> AsyncGenerator[str, None]:
        # tools methods
        registry = ToolRegistry()
        registry.register_tool("InspectorTools", InspectorTools)

        factory = DynamicToolFactory(registry)
        exec_agent = factory.create_dynamic_tool(["InspectorTools"])
        llm = LLMInterface(str(get_path()), 'codeinspector_'+context.user_id, mongo_handler= get_mongo_db())

        tool = exec_agent('/test', logger=None)
        
        llm_response = ""
        messages.append({"role": "user","content":  context.message})
        
        async for response in await llm.llm_interaction_wrapper(
            messages=messages.copy(),
            user_prompt=None,
            system_prompt=self.system_prompt,  # Make sure this is defined earlier
            response_format={'type': 'text'},
            model=self.model_name,
            stream=True,
            function_schemas=tool.function_schemas,  # Make sure tool is defined
            function_executor=tool.function_executor  # Make sure tool is defined
        ):
            #TODO: need to handle the indication in proper way
            if "Checking the data" in response:
                yield f"data: {json.dumps({'content':  'Your code-related insights will be available shortly...'})}\n\n"

            elif "Fetching the data" in response:
                yield f"data: {json.dumps({'content': 'Please wait...'})}\n\n"
            
            else:
                llm_response += response
                yield f"data: {json.dumps({'content': llm_response})}\n\n"

        messages.append({"role": "assistant","content":  llm_response})
        
        await self.utils._ask_for_title(context.discussion_id, messages.copy())
        await self.node_db.update_node_by_id(int(context.discussion_id), {'Discussion': json.dumps(messages)})
        
        yield f"data: {json.dumps({'discussion_id': int(context.discussion_id)})}\n\n"

        yield f"event: stop\n"
        yield f"data: stopped\n\n"
        
  
    async def interface(self, context: ChatContext):
        try:
            messages = await self.utils._load_discussion(context.discussion_id,count=10)
            logger.info(f"CODEINSPECTOR AGENT: Loaded {len(messages)} messages for discussion ID {context.discussion_id}")
            
            # set the limit for user discussion as 20 iteration
            if len(messages) > 40:
                logger.warning(f"Discussion limit reached for discussion ID {context.discussion_id}")
                async for event in self.utils._yield_event('Discussion limit reached'):
                    yield event
                return
            
            async for response in self._submit_completion(context, messages):
                yield response
                
        except Exception as e:
            logger.error(f"Error in interface method: {str(e)}", exc_info=True)
            yield f"data: {json.dumps({'error': 'An error occurred'})}\n\n".encode('utf-8')