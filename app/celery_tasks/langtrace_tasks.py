from app.classes.GraphBuilder import BuildGraph
import logging
from app.core.Settings import settings
import asyncio
import os
import requests
import hashlib
import json
from dotenv import load_dotenv
from datetime import datetime
from app.utils.background_tasks import add_background_task

# Setup logging with more detailed format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('background_tasks.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

async def create_langtrace_project(node_id, properties):

        authkey = os.getenv("LANGTRACE_AUTHKEY")
        domain = os.getenv("LANGTRACE_DOMAIN")
        teamID = "cm2ss9sqi0001likxzxtj0yj5"
        if not authkey:
            logger.error("Missing LANGTRACE_AUTHKEY environment variable.")
            return {"status": "error", "message": "Missing authentication key"}

        authkey_hashed = hashlib.md5(authkey.encode()).hexdigest()

        # Extract project properties
        project_data = {
            "name": properties.get("Title", "").strip(),
            "description": properties.get("Description", "").strip(),
            "teamId": teamID,  # Static value
            "type": "kavia",  # Static value
        }

        # Validate the project data
        if not project_data["name"]:
            logger.error("Project 'name' cannot be empty.")
            return {"status": "error", "message": "Project 'name' cannot be empty."}

        if not project_data["description"]:
            logger.error("Project 'description' cannot be empty.")
            return {"status": "error", "message": "Project 'description' cannot be empty."}

        # Check if teamId and type are static and assume they're valid
        # (Alternatively, you could validate them if needed)

        headers = {
            "authkey": authkey_hashed,
            'Content-Type': 'application/json'
        }

        try:
            
            
            # First API call to create the project
            response = requests.post(
                f"{domain}/api/project", 
                data=json.dumps(project_data), 
                headers=headers
            )
            response.raise_for_status()  # Raise an exception for HTTP errors
            
            # Log the successful response for project creation
            logger.info(f"Project created successfully: {response.status_code}")
            
            # Extract the project id from the response and log it
            project_id = response.json().get('data', {}).get('id')
            if project_id:
                logger.info(f"langtrace_project id: {project_id}")  # Print the id
            else:
                logger.error("Project id not found in the response.")
                return {"status": "error", "message": "Project id not found in the response."}

            # Second API call to get the API key for the created project
            api_key_url = f"{domain}/api/api-key?project_id={project_id}"
            api_key_response = requests.post(
                api_key_url,
                headers=headers
            )
            api_key_response.raise_for_status()  # Raise an exception for HTTP errors
            
            api_key = api_key_response.json().get('data', {}).get('apiKey')
            if api_key:
                # Hash the API key
                hashed_api_key = api_key
                
                # Create response object with all required data
                current_time = datetime.utcnow()
                project_info = {
                    "id": project_id,
                    "kavia_project_id": node_id,
                    "hashapikey": hashed_api_key,
                    "langtrace_project_id": project_id,
                    "project_name": project_data["name"],
                    "project_description": project_data["description"],
                    "team_id": teamID,
                    "is_active": True,
                    "created_at": current_time,
                    "modified_at": current_time
                }
                
                # Connect to MongoDB and store the data
                try:
                    from app.connection.establish_db_connection import connect_git_mongo_db
                    mongo_handler = connect_git_mongo_db(settings.MONGO_DB_NAME, "langtrace_projects")

                    result = await mongo_handler.create_project_trace(project_info)
                    if result:
                        logger.info("Successfully stored project data in MongoDB")
                    else:
                        logger.error("Failed to store project data in MongoDB")
        
                        
                except Exception as mongo_error:
                    logger.error(f"MongoDB Error: {mongo_error}")

                # Create a JSON-serializable version of project_info for logging
                log_project_info = project_info.copy()
                log_project_info['created_at'] = current_time.isoformat()
                log_project_info['modified_at'] = current_time.isoformat()
                
                # Log the project info
                logger.info(f"Project information: {json.dumps(log_project_info, indent=2)}")
                
            # Log the response from the second API call (API key retrieval)
            logger.info(f"API Key response: {api_key_response.status_code}")
            logger.info(f"API Key response data: {api_key_response.json()}")
            
            # Optionally, you can return this data as part of the task result
            return api_key_response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"Error creating project or fetching API key: {e}")
            return {"status": "error", "message": str(e)}
        
async def fast_api_task(node_id, properties):
    background_tasks = add_background_task(create_langtrace_project, node_id, properties)
    # Execute the background tasks
    for task in background_tasks.tasks:
        await task()
        
    print("Fast_API background task :",background_tasks)
    

# data = {'Name': 'Hill Climb Racing Project', 'Description': 'Hill Climb Racing Project Description', 'Type': 'project', 'Title': 'Hill Climb Racing Project', 'created_by': '24e83488-80b1-7012-99c3-df093902e6b3', 'created_at': '2024-11-18T10:14:01.161172'}


# if __name__ == "__main__":
#     # Run the async main function
#     # asyncio.run(fast_api_task(344, data))
#     create_langtrace_project(343, data)