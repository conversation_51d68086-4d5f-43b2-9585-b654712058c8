# app/tasks/graph_tasks.py
from celery import Celery
from app.classes.GraphBuilder import BuildGraph
import logging
from app.core.Settings import settings
import asyncio

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


celery_app = Celery(
    'graph_tasks',
    broker=settings.CELERY_BROKER_URL
)

# Optional: Add Celery configuration
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
)

def run_async(coro):
    """Helper function to run async code in sync context"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()
        
@celery_app.task(name="generate_knowledge_graph", bind=True)
def generate_knowledge_graph_task(self, project_id, username, data):
    """
    Generate knowledge graph task with task ID logging
    """
    try:
        app = BuildGraph()
        logger.info(f"Starting graph generation task with ID: {self.request.id}")
        
        # Run the async function in a sync context using our helper function
        run_async(app.generate_graph(project_id, username, data))
        
        print("username - ", username)
        print("data: ", data)
        
        logger.info(f"Task {self.request.id} completed successfully")
        return {
            "status": "success",
            "message": "Graph generation completed",
            "task_id": self.request.id,
        }
        
    except Exception as e:
        logger.error(f"Task {self.request.id} failed with error: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "task_id": self.request.id
        }

def get_task_status(task_id):
    """
    Get the status of a task by its ID
    """
    task = generate_knowledge_graph_task.AsyncResult(task_id)
    return {
        "task_id": task_id,
        "status": task.status,
        "result": task.result if task.ready() else None
    }