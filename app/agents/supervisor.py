from builtins import Exception, enumerate, len, open, print, set, str, super
import asyncio
from typing import Dict
import json 
from app.connection.establish_db_connection import get_node_db, get_mongo_db
from app.discussions.discussion import Discussion
from app.agents.Workitem import WorkItem
from app.agents.agent import Agent
from app.connection.establish_db_connection import get_node_db, get_vector_db , get_mongo_db
from typing import Dict, Any
from app.telemetry.logger_config import get_logger,set_task_id
from app.telemetry.discussion_timer.timer import DiscussionTimer
from datetime import datetime, timedelta
import os
from app.core.task_framework import tasks_collection_name
from app.core.Settings import settings
from app.core.task_framework import TaskStatus            

class Supervisor(Agent):
    def __init__(self, name: str, agents: Dict[str, 'Agent'],architecture_level,user_level,start_config, config_types):
        super().__init__(name)
        self.name = name
        self.agents = agents
        self.architecture_level = architecture_level
        self.user_level = user_level
        self.work_queue: asyncio.Queue = asyncio.Queue()
        self.priority_queue: asyncio.PriorityQueue = asyncio.PriorityQueue()
        self.db = get_node_db()
        self.mongo_db = get_mongo_db()
        self.levels = 1
        self.start_config = start_config
        self.architecture_priority = 995
        self.processed_nodes = set()
        self.nodes_to_process = set()
        self.config_types = config_types
        self.update_logger_agent = get_logger(__name__)
        self.architecture_levels = 3
        self.processed_design = False
        self.project_id = None
        self.current_reconfig = None
        self.completed_configs = set()  # Track completed configurations
        self.PRIORITY_BASES = {
            "stop_tasks": 2000,
            "epic": 999,
            "userstory": 998,
            "testcases": 997,
            "architecture": 1000,
            "component": 1000,
            "subcomponent": 1000,
            "designer": 1000,
            "documentation": 1000,
            "component_testcases":999
        }
        self.ARCHITECTURE_STEPS = [
            "create_design_details",
            "autoconfig_interfaces",
            "configure"
        ]
        # Node type to autoconfig type mapping
        self.node_type_mapping = {
            "Architecture": "components_autoconfig",
            "Component": "components_autoconfig",
            "SubComponent": "subcomponents_autoconfig",
            "Interface": "interface_autoconfig",
            "Design": "design_autoconfig",
            "Project": "project_autoconfig",
            "Epic": "epic_autoconfig",
            "UserStory": "user_story_autoconfig",
            "RequirementRoot": "requirements_autoconfig"
        }
        self.discussion_timer = DiscussionTimer()
        self.timing_summaries = []
        self.mongo_handler = get_mongo_db(settings.MONGO_DB_NAME, tasks_collection_name)
        self.mongo_handler.set_collection(tasks_collection_name)
        self.is_individual_config = (
                len(config_types) == 1 and 
                config_types[0] in ['epic_autoconfig', 'user_story_autoconfig']
            )

        self.detect_partial_autoconfig()

        
    # async def start(self, node_id, node_type, project_id,specific_node_id=None):
    #     # Check if the node is a root type
    #     is_root = await self.is_root_node(node_id, node_type)
    #     node = await self.db.get_node_by_id(node_id)
    #     if is_root:
    #         initial_work_item = self.create_work_item(self.start_config, None, 1, node_id, node_type, project_id)
    #         await self.add_work_item(initial_work_item)
    #     else:
    #         # If not a root, only configure this specific node
    #         await self.configure_single_node(node_id, node_type, project_id)
    
    async def start(self, node_id, node_type, project_id, specific_node_id=None):
        try:
            self.project_id = project_id
             # Initialize reconfiguration
            reconfig_result = await self.start_reconfiguration(project_id)
            print("reconfig_result",reconfig_result)
            self.current_reconfig = reconfig_result["reconfig_num"]
            # self.is_first_config = reconfig_result["is_first_config"]
            self.auto_config_completed = False 
                
        
            # Continue with normal start process
            is_root = await self.is_root_node(node_id, node_type)
            node = await self.db.get_node_by_id(node_id)
            if is_root:
                initial_work_item = self.create_work_item(self.start_config, None, 1, node_id, node_type, project_id)
                await self.add_work_item(initial_work_item)
            else:
                await self.configure_single_node(node_id, node_type, project_id)
                
        except Exception as e:
            self.update_logger_agent.info(f"Error starting reconfiguration: {str(e)}")
            raise
        
        
    async def start_reconfiguration(self, project_id):
        """Initialize reconfiguration process with proper tracking"""
        try:
            # Check if this is the first configuration
            reconfig_doc = await self.mongo_db.get_one(
                {"project_id": project_id},
                self.mongo_db.db["reconfig_tracker"]
            )
            print("reconfig_doc",reconfig_doc)
            
           
            is_first_config = not reconfig_doc or "last_successful" not in reconfig_doc
            print("is_first_config",is_first_config)
            
            
            # Increment or initialize reconfig counter
            if is_first_config:
                current_reconfig = 1
                await self.mongo_db.insert(
                    {
                        "project_id": project_id,
                        "current_reconfig": current_reconfig,
                        "last_successful": 0,  # Initialize to 0 for first time
                        "status": "in_progress",
                        "started_at": datetime.now().isoformat(),
                        
                    },
                    self.mongo_db.db["reconfig_tracker"]
                )
            else:
                current_reconfig = reconfig_doc["current_reconfig"] + 1
                print("current_reconfig",current_reconfig)
                await self.mongo_db.update_one(
                    {"project_id": project_id},
                    {
                            "current_reconfig": current_reconfig,
                            "status": "in_progress",
                            "started_at": datetime.now().isoformat()
                    },
                    False,  # upsert parameter
                    self.mongo_db.db["reconfig_tracker"]  # db parameter
                )
            
            return {
                "reconfig_num": current_reconfig,
                "is_first_config": is_first_config
            }
            
        except Exception as e:
            self.update_logger_agent.info(f"Error starting reconfiguration: {str(e)}")
            raise
        
    # Add this function to provide reconfig info to discussions/agents
    def get_version_info(self):
        return {
            "project_id": self.project_id,
            "current_reconfig": self.current_reconfig
        }

        
    async def is_root_node(self, node_id, node_type):
        # Define root node types
        root_types = ["Project", "ArchitectureRoot", "RequirementRoot", "WorkItemRoot"]
        
        # Check if the node_type is in root_types
        if node_type in root_types:
            return True
        
        # If not, check if it has any parent nodes
        parent_node = await self.db.get_parent_node(node_id)
        return parent_node is None

    async def configure_single_node(self, node_id, node_type, project_id):
        self.update_logger_agent.info(f"Configuring single node: {node_id} of type {node_type}")
        
        # Fetch the node from the database
        node = await self.db.get_node_by_id(node_id)
        if not node:
            self.update_logger_agent.error(f"Node with ID {node_id} not found")
            return
        
        # Get the labels from the node
        labels = node.get('labels', [])
        
        # Find the first matching label in our mapping
        self.start_config = next(
            (self.node_type_mapping[label] for label in labels if label in self.node_type_mapping),
            "components_autoconfig"  # Default if no match found
        )
        
        self.update_logger_agent.info(f"Using start_config: {self.start_config} for node labels: {labels}")
        
        # Create a work item for this specific node
        work_item = self.create_work_item(self.start_config, None, 1, node_id, node_type, project_id)
        await self.add_work_item(work_item)
        
        # Set the single_config flag to True
        self.single_config = True



    async def process_single_work_item(self, work_item):
        agent = self.agents.get(work_item.agent)
        if agent:
            await agent.process_work_item(work_item)
        
        # After processing, log completion and exit
        self.update_logger_agent.info(f"Completed configuration for node: {work_item.node_id}")
        
        # Create and add termination work item
        termination_work_item = self.create_termination_work_item()
        await self.add_work_item(termination_work_item)
        
    async def notify_complete(self, complete_work_item):
        self.node_id = complete_work_item.node_id
        self.node_type = complete_work_item.node_type
        self.project_id = complete_work_item.project_id
        print("self.node_id", self.node_id)
        print("self.node_type", self.node_type)
        print("self.project_id", self.project_id)
        
        self.update_logger_agent.info(f"Completed work item: {complete_work_item.config_type} - {complete_work_item.entry}")
        self.update_logger_agent.info("   ")
        self.update_logger_agent.info(f"node_id {complete_work_item.node_id}, node_type {complete_work_item.node_type}, Step {complete_work_item.step}")
        
        if self.task_id:
            try:
                await self.mongo_handler.update_by_task_id(
                    self.task_id,
                    {'completed_configs': list(self.completed_configs)}
                )
                self.update_logger_agent.info(f"Updated completed_configs in MongoDB: {self.completed_configs}")
            except Exception as e:
                self.update_logger_agent.error(f"Error updating completed_configs in MongoDB: {str(e)}")
                
        await self.update_progress_based_on_config_types()
        
        await self.get_next_work_item(complete_work_item)

    async def discussion_timeout(self, node_id, node_type, discussion_type):
        """Handle a discussion timeout by marking the task as failed with network error"""
        self.update_logger_agent.error(f"Discussion timeout for node {node_id} of type {node_type}, discussion type {discussion_type}")
        
        # Update the task status in MongoDB
        await self.mongo_handler.update_by_task_id(self.task_id, {
            'node_id': node_id, 
            'status': 'NETWORK_ERROR', 
            'error_message': f"Discussion timed out after the maximum allowed time",
            'run_completed': True
        })
        
        # Create and add a termination work item
        termination_work_item = self.create_termination_work_item()
        await self.add_work_item(termination_work_item)
        
        # Send an update notification
        for agent_name, agent in self.agents.items():
            if hasattr(agent, 'send_update') and callable(agent.send_update):
                await agent.send_update(self.task_id)

    # async def notify_complete(self, complete_work_item):
    #     self.node_id = complete_work_item.node_id
    #     self.node_type = complete_work_item.node_type
    #     self.project_id = complete_work_item.project_id
    #     print("self.node_id", self.node_id)
    #     print("self.node_type", self.node_type)
    #     print("self.project_id", self.project_id)
        
    #     self.update_logger_agent.info(f"Completed work item: {complete_work_item.config_type} - {complete_work_item.entry}")
    #     self.update_logger_agent.info("   ")
    #     self.update_logger_agent.info(f"node_id {complete_work_item.node_id}, node_type {complete_work_item.node_type}, Step {complete_work_item.step}")
        
    #     # Add the completed configuration to the completed_configs set
    #     self.completed_configs.add(complete_work_item.config_type)
        
    #     # Update the completed_configs in the MongoDB task document
    #     # if self.task_id:
    #     #     try:
    #     #         await self.mongo_handler.update_by_task_id(
    #     #             self.task_id,
    #     #             {'completed_configs': list(self.completed_configs)}
    #     #         )
    #     #         self.update_logger_agent.info(f"Updated completed_configs in MongoDB: {self.completed_configs}")
    #     #     except Exception as e:
    #     #         self.update_logger_agent.error(f"Error updating completed_configs in MongoDB: {str(e)}")
        
    #     # Update progress based on completed configurations
    #     # await self.update_progress_based_on_config_types()
        
    #     await self.get_next_work_item(complete_work_item)

    async def add_work_item(self, work_item_dict):
        await self.work_queue.put(work_item_dict)

    def create_work_item(self, config_type, parent,levels, node_id, node_type,project_id):
        step = self.get_first_step_for(config_type)
        return WorkItem(config_type, parent, step, step['agent'],step['entry'], levels, node_id, node_type,project_id)

    def create_termination_work_item(self):
        return WorkItem("termination",None,None,None,"stop_tasks",0,0,None,None)

    def load_mapping(file_path):
            with open(file_path, 'r') as file:
                return json.load(file)
            
    def extract_steps(self,config_type,mapping):
        if config_type in mapping:
            return mapping[config_type].get('steps', [])
        return []
    
    def override_mapping(self, config_type, key, value):
        if config_type in self.mapping:
            self.mapping[config_type][key] = value
        
    def get_first_step_for(self,config_type):        

        # Load the mapping from     'mapping.json'
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        abs_file_path = os.path.join(base_dir, 'agents', 'mapping.json')
        
        with open(abs_file_path, 'r') as file:
            self.mapping = json.load(file)
    
        steps = self.extract_steps(config_type, self.mapping)
        if steps:
            
            return steps[0]
        return None
        
    def get_priority_for_work_item(self, work_item):
        priority = 1000  # Default priority

        if work_item.entry == "stop_tasks":
            priority = self.PRIORITY_BASES["stop_tasks"]

        elif work_item.agent == "epic_agent":
            priority = self.PRIORITY_BASES["epic"] 

        elif work_item.agent == "userstory_agent":
            priority = self.PRIORITY_BASES["userstory"] 

        elif work_item.agent == "TestCaseGeneratorAgent":
            priority = self.PRIORITY_BASES["testcases"] 

        elif work_item.agent == "ComponentTestCaseGeneratorAgent":
            priority = self.PRIORITY_BASES["component_testcases"] 

        elif((work_item.agent == "ArchitectureAgent") and (work_item.entry != "capture_requirements_for_architecture") and (work_item.entry != "get_architecture_root")):
            self.architecture_priority = self.architecture_priority - (work_item.levels*100)-1
            priority = self.architecture_priority

        elif work_item.agent == "documentation_agent":
            priority = self.PRIORITY_BASES["documentation"] - work_item.levels
        
        elif((work_item.agent == "DesignerAgent")):
            self.architecture_priority = self.architecture_priority - 1
            priority = self.architecture_priority
            # priority = 0

        else:
            priority = 1000

        return priority
    
    def get_step_from_mapping(self, config_type, agent, entry):
        if config_type in self.mapping:
            steps = self.mapping[config_type].get('steps', [])
            for step in steps:
                if step.get('agent') == agent and step.get('entry') == entry:
                    return step
        return None
    
    def get_next_from_mapping(self, config_type, agent, entry):
        if config_type in self.mapping:
            steps = self.mapping[config_type].get('steps', [])
            
            next_step_index = -1
            for index, step in enumerate(steps):
                if step.get('agent') == agent and step.get('entry') == entry:
                    next_step_index = index + 1
                    break  # Exit after finding the first matching step
            
            if next_step_index < 0 or next_step_index >= len(steps):
                return None
            
            return steps[next_step_index]
        
        return None
    
    def get_levels_from_mapping(self, config_type):
        if config_type in self.mapping:
            return self.mapping[config_type].get('levels', 0)
        return 0

    def get_step_from_config_type(self, config_type):
        if config_type in self.mapping:
            steps = self.mapping[config_type].get('steps', [])
            if steps:
                return steps[0]  # Return the first step for the given config_type
        return None
    
    def all_child_nodes_processed(self):
        child_nodes_set = self.nodes_to_process
        processed_nodes_set = self.processed_nodes
        print("processed_nodes_set",processed_nodes_set)
        # Check if all child nodes are in processed nodes
        all_processed = child_nodes_set.issubset(processed_nodes_set)
        
        # log or handle cases where not all nodes are processed
        if not all_processed:
            unprocessed_nodes = child_nodes_set - processed_nodes_set
            print(f"Unprocessed nodes: {unprocessed_nodes}")
        
        return all_processed

    def reset_processed_nodes(self):
        self.processed_nodes.clear()

    async def remove_child_nodes(self, node_id):
        # Get all child nodes
        child_nodes = await self.db.get_child_nodes(node_id, 'Architecture')
        
        # Remove each child node
        for child in child_nodes:
            await self.db.delete_current_node(child['id'])
        
        print(f"Removed {len(child_nodes)} child nodes from node {node_id}") # type: ignore

    async def continue_configuration_from_node(self,project_id, node_id):
        # Fetch the node details
        node = await self.db.get_node_by_id(node_id)
        if not node:
            print(f"Node with ID {node_id} not found.")
            return

        # Determine the node type
        node_type = Discussion.get_specific_node_type(node['labels'])

        # Remove child nodes
        await self.remove_child_nodes(node_id)

        # Reset the node's architectural leaf status and remove configured states
        update_dict = {
            "IsArchitecturalLeaf": "no",
            "configuration_state": "not_configured",
            "autoconfig_state": "not_configured",
            "architecture_autoconfig_state": "not_configured",
            "components_autoconfig_state": "not_configured",
            "subcomponents_autoconfig_state": "not_configured",
            "design_autoconfig_state": "not_configured"
        }
        await self.db.update_node_by_id(node_id, update_dict, node_type)

        # Determine the appropriate configuration type
        config_type = 'architecture_autoconfig'

        # Create a work item to continue configuration
        work_item = self.create_work_item(config_type, None, 1, node_id, node_type, project_id)

        # Reset the processed nodes and nodes to process
        self.reset_processed_nodes()

        # Add the current node to nodes_to_process
        self.nodes_to_process.add(node_id)

        # Add the work item to the queue
        await self.add_work_item(work_item)

        print(f"Continuing configuration from node {node_id} with config type {config_type}")

    def find_next_valid_config_for_partial_autoconfig(self, current_config):
        """
        Special logic for finding next configuration in partial autoconfig scenarios.
        
        This method handles cases where requirements_autoconfig is missing but
        child requirement configs (epic, user_story, testcase) are present.
        
        Args:
            current_config: The configuration that just completed
            
        Returns:
            The next valid config or None if no more valid configs exist
        """
        # Get all mapping keys in order
        mapping_keys = list(self.mapping.keys())
        
        # Define the requirement hierarchy for partial autoconfig handling
        requirement_configs = [
            "requirements_autoconfig",
            "epic_autoconfig", 
            "user_story_autoconfig",
            "testcase_autoconfig"
        ]
        
        try:
            current_index = mapping_keys.index(current_config)
        except ValueError:
            self.update_logger_agent.warning(f"Current config '{current_config}' not found in mapping keys")
            current_index = -1
        
        # Special handling for requirement-related configs in partial autoconfig
        if current_config in requirement_configs:
            # Look for the next requirement config in our selected list
            remaining_requirement_configs = [
                config for config in requirement_configs 
                if config in self.config_types and config not in self.completed_configs
            ]
            
            if remaining_requirement_configs:
                # Find the next requirement config in mapping order
                for config in remaining_requirement_configs:
                    if config in mapping_keys:
                        config_index = mapping_keys.index(config)
                        if config_index > current_index:
                            self.update_logger_agent.info(f"Partial autoconfig: Next requirement config found: {config}")
                            return config
        
        # Fall back to standard mapping traversal for non-requirement configs
        # or when no more requirement configs are available
        for i in range(current_index + 1, len(mapping_keys)):
            potential_config = mapping_keys[i]
            
            # Skip termination
            if potential_config == "termination":
                continue
                
            # Check if this config is in our selected config_types AND not completed
            if potential_config in self.config_types and potential_config not in self.completed_configs:
                self.update_logger_agent.info(f"Partial autoconfig: Found next valid config: {potential_config} at index {i}")
                return potential_config
        
        # If no valid config found, return None
        self.update_logger_agent.info("Partial autoconfig: No more valid configurations found in sequence")
        return None

    def get_partial_autoconfig_workflow_status(self):
        """
        Get detailed workflow status for partial autoconfig scenarios.
        Shows which requirement configs are completed vs remaining.
        """
        if not self.partial_autoconfig:
            return None
        
        requirement_configs = [
            "requirements_autoconfig",
            "epic_autoconfig", 
            "user_story_autoconfig",
            "testcase_autoconfig"
        ]
        
        workflow_status = {
            'selected_requirement_configs': [
                config for config in requirement_configs if config in self.config_types
            ],
            'completed_requirement_configs': [
                config for config in requirement_configs 
                if config in self.config_types and config in self.completed_configs
            ],
            'remaining_requirement_configs': [
                config for config in requirement_configs 
                if config in self.config_types and config not in self.completed_configs
            ],
            'missing_parent_config': 'requirements_autoconfig' not in self.config_types
        }
        
        return workflow_status

    def detect_partial_autoconfig(self):
        """
        Detect if this is a partial autoconfig scenario where requirements_autoconfig 
        is missing but child requirement configs are present.
        
        Sets self.partial_autoconfig = True if:
        - requirements_autoconfig is NOT in config_types
        - AND at least one of [epic_autoconfig, user_story_autoconfig, testcase_autoconfig] is present
        """
        # Check if requirements_autoconfig is missing
        has_requirements_autoconfig = "requirements_autoconfig" in self.config_types
        
        # Check if any child requirement configs are present
        child_requirement_configs = [
            "epic_autoconfig",
            "user_story_autoconfig", 
            "testcase_autoconfig"
        ]
        
        has_child_requirement_configs = any(
            config in self.config_types for config in child_requirement_configs
        )
        
        # Set partial autoconfig flag
        self.partial_autoconfig = not has_requirements_autoconfig and has_child_requirement_configs
        
        self.partial_autoconfig = False  # Temporarily disable partial autoconfig detection
        if self.partial_autoconfig:
            present_child_configs = [
                config for config in child_requirement_configs 
                if config in self.config_types
            ]
            self.update_logger_agent.info(
                f"Partial autoconfig detected: requirements_autoconfig missing but "
                f"child configs present: {present_child_configs}"
            )
            
        else:
            self.update_logger_agent.info("Full autoconfig detected: requirements_autoconfig present or no child configs")
            
    def get_remaining_configs(self):
        """Get list of configurations that haven't been completed yet"""
        return [config for config in self.config_types if config not in self.completed_configs]

    def find_next_valid_config(self, current_config):
        """
        Find the next valid configuration by using the mapping keys order
        to find the next uncompleted configuration in the sequence.
        
        This approach works even when intermediate configs are missing from config_types,
        as it looks at the actual mapping structure rather than 'next' references.
        
        Returns: The next valid config or None if no more valid configs exist
        """
        # Get all mapping keys in order (this represents the execution sequence)
        mapping_keys = list(self.mapping.keys())
        
        try:
            # Find current config's position in the mapping
            current_index = mapping_keys.index(current_config)
        except ValueError:
            # If current_config is not in mapping keys, start from beginning
            self.update_logger_agent.warning(f"Current config '{current_config}' not found in mapping keys")
            current_index = -1
        
        # Look for the next valid configuration starting from current position + 1
        for i in range(current_index + 1, len(mapping_keys)):
            potential_config = mapping_keys[i]
            
            # Skip termination
            if potential_config == "termination":
                continue
                
            # Check if this config is in our selected config_types AND not completed
            if potential_config in self.config_types and potential_config not in self.completed_configs:
                self.update_logger_agent.info(f"Found next valid config: {potential_config} at index {i}")
                return potential_config
        
        # If no valid config found in the remaining sequence, return None
        self.update_logger_agent.info("No more valid configurations found in sequence")
        return None

    def get_mapping_sequence_info(self):
        """
        Get detailed information about the mapping sequence and current progress.
        Useful for debugging and monitoring.
        """
        mapping_keys = list(self.mapping.keys())
        
        sequence_info = {
            'total_mapping_keys': len(mapping_keys),
            'selected_configs': self.config_types,
            'completed_configs': list(self.completed_configs),
            'mapping_sequence': []
        }
        
        for i, key in enumerate(mapping_keys):
            status = 'not_selected'
            if key in self.config_types:
                if key in self.completed_configs:
                    status = 'completed'
                else:
                    status = 'pending'
            
            sequence_info['mapping_sequence'].append({
                'index': i,
                'config': key,
                'status': status,
                'next_in_mapping': self.mapping.get(key, {}).get('next', 'none')
            })
        
        return sequence_info

    async def get_next_work_item(self, complete_work_item):
        current_config = complete_work_item.config_type
        agent = complete_work_item.agent
        entry = complete_work_item.entry
        levels = complete_work_item.levels
        step = self.get_step_from_mapping(current_config, agent, entry)

        # Add the completed node to processed nodes
        self.processed_nodes.add(complete_work_item.node_id)
        
        self.update_logger_agent.info(f"""
        === Processing Work Item Completion ===
        Config: {current_config}
        Agent: {agent}
        Entry: {entry}
        Parent: {complete_work_item.parent.config_type if complete_work_item.parent else 'None'}
        Node ID: {complete_work_item.node_id}
        Node Type: {complete_work_item.node_type}
        """)

        # 3. Get Next Step in Current Config
        if step and 'children' in step:
            
            child_config = step['children']
            levels = levels + 1
            if child_config in self.config_types:
                child_nodes = await self.db.get_child_nodes(self.node_id, self.node_type)
                child_step = self.get_step_from_config_type(child_config)
                for child in child_nodes:
                    specific_node_type = Discussion.get_specific_node_type(child['labels'])
                    work_item = self.create_work_item(child_config, complete_work_item, levels, child['id'], specific_node_type, self.project_id)
                    work_item.agent = child_step['agent']
                    work_item.entry = child_step['entry']
                    await self.add_work_item(work_item)

        next_step = self.get_next_from_mapping(current_config, agent, entry)
        if next_step:
            work_item = self.create_work_item(
                current_config,
                None,
                1,
                complete_work_item.node_id,
                complete_work_item.node_type,
                self.project_id
            )
            work_item.agent = next_step['agent']
            work_item.entry = next_step['entry']
            await self.add_work_item(work_item)
            return

        # 4. Move to Next Configuration
        next_config = self.mapping[current_config].get('next')

        # Special case handling for component_testcase_autoconfig
        if next_config == "component_testcase_autoconfig" and next_config not in self.config_types:
        # If component_testcase_autoconfig is the next but not selected, skip to interface_autoconfig
            next_config = self.mapping.get("component_testcase_autoconfig", {}).get('next')
            self.update_logger_agent.info(f"Skipping component_testcase_autoconfig as it's not selected, moving to {next_config}")
        
        if next_config and next_config != "termination" and next_config in self.config_types:
            self.update_logger_agent.info(f"Moving to next config: {next_config}")
            next_work_item = self.create_work_item(
                next_config,
                None,
                1,
                complete_work_item.node_id,
                complete_work_item.node_type,
                self.project_id
            )
            await self.add_work_item(next_work_item)
        else:
            await self.mongo_db.update_by_task_id(
                    self.task_id, 
                    {
                        'task_status': TaskStatus.COMPLETE, 
                        'run_completed': True, 
                        'progress': 100
                    }
                )
            
            await self.send_update(self.task_id)
            
            termination_work_item = self.create_termination_work_item()
            await self.add_work_item(termination_work_item)

            
    async def is_node_configured(self, node_id):
        """Check if a node has been fully configured"""
        node = await self.db.get_node_by_id(node_id)
        return node['properties'].get('configuration_state') == 'configured'

    async def add_work_item(self, w_i):
        # Don't add if config already completed
        if w_i.config_type in self.completed_configs:
            self.update_logger_agent.info(f"Skipping already completed configuration: {w_i.config_type}")
            return

        priority = self.get_priority_for_work_item(w_i)
        self.update_logger_agent.info(f"""
        === Adding Work Item ===
        Config Type: {w_i.config_type}
        Agent: {w_i.agent}
        Entry: {w_i.entry}
        Priority: {priority}
        Current Queue: {self.priority_queue.qsize()}
        """)
        await self.priority_queue.put((priority, w_i))

    async def record_discussion_timing(self, timing_summary):
        """Record timing summary from a discussion"""
        self.timing_summaries.append(timing_summary)
        
    # async def find_next_valid_config(self, current_config):
    #     """
    #     Find the next valid configuration by following the chain of 'next' references
    #     until finding one that's in the selected config_types.
        
    #     Returns: The next valid config or None if no more valid configs exist
    #     """
    #     visited = set()  # Track visited configs to avoid infinite loops
    #     visited.add(current_config)
    #     next_config = self.mapping[current_config].get('next')
        
    #     # Follow the chain of 'next' references
    #     while next_config and next_config != "termination" and next_config not in visited:
    #         if next_config in self.config_types:
    #             return next_config
            
    #         visited.add(next_config)
    #         next_config = self.mapping.get(next_config, {}).get('next')
        
    #     return None

    async def print_final_timing_report(self):
        """Generate and print the final timing report"""
        combined_summary = {
            'total_discussions': 0,
            'total_duration': timedelta(),
            'by_node_type': {},
            'by_discussion_type': {}
        }
        
        # Combine all summaries
        for summary in self.timing_summaries:
            # Handle both dict and TimingSummary objects
            if isinstance(summary, dict):
                combined_summary['total_discussions'] += summary.get('total_discussions', 0)
                combined_summary['total_duration'] += summary.get('total_duration', timedelta())
                
                # Combine node type stats
                for node_type, data in summary.get('by_node_type', {}).items():
                    if node_type not in combined_summary['by_node_type']:
                        combined_summary['by_node_type'][node_type] = {
                            'count': 0,
                            'total_duration': timedelta()
                        }
                    combined_summary['by_node_type'][node_type]['count'] += data.get('count', 0)
                    combined_summary['by_node_type'][node_type]['total_duration'] += data.get('total_duration', timedelta())
                
                # Combine discussion type stats
                for disc_type, data in summary.get('by_discussion_type', {}).items():
                    if disc_type not in combined_summary['by_discussion_type']:
                        combined_summary['by_discussion_type'][disc_type] = {
                            'count': 0,
                            'total_duration': timedelta()
                        }
                    combined_summary['by_discussion_type'][disc_type]['count'] += data.get('count', 0)
                    combined_summary['by_discussion_type'][disc_type]['total_duration'] += data.get('total_duration', timedelta())
            else:
                # Handle TimingSummary object
                combined_summary['total_discussions'] += summary.total_discussions
                combined_summary['total_duration'] += summary.total_duration
                
                for node_type, data in summary.by_node_type.items():
                    if node_type not in combined_summary['by_node_type']:
                        combined_summary['by_node_type'][node_type] = {
                            'count': 0,
                            'total_duration': timedelta()
                        }
                    combined_summary['by_node_type'][node_type]['count'] += data['count']
                    combined_summary['by_node_type'][node_type]['total_duration'] += data['total_duration']
                
                for disc_type, data in summary.by_discussion_type.items():
                    if disc_type not in combined_summary['by_discussion_type']:
                        combined_summary['by_discussion_type'][disc_type] = {
                            'count': 0,
                            'total_duration': timedelta()
                        }
                    combined_summary['by_discussion_type'][disc_type]['count'] += data['count']
                    combined_summary['by_discussion_type'][disc_type]['total_duration'] += data['total_duration']
        
        # Format and print the report using the new Datadog format
        formatted_report = self.discussion_timer.format_datadog_summary(combined_summary, self.task_id)
        self.update_logger_agent.info("Final Discussion Timing Report:")
        self.update_logger_agent.info(json.dumps(formatted_report, indent=2))
        print("\nFinal Discussion Timing Report:\n" + json.dumps(formatted_report, indent=2))

            
    async def run(self):
        while True:
            priority, work_item_dict = await self.priority_queue.get()
            
            # Debug queue state
            self.update_logger_agent.info(f"""
            === Processing Queue Item ===
            Priority: {priority}
            Config: {work_item_dict.config_type}
            Agent: {work_item_dict.agent}
            Entry: {work_item_dict.entry}
            Completed Configs: {self.completed_configs}
            Remaining Queue: {self.priority_queue.qsize()}
            """)

            try:
                if work_item_dict.entry == "stop_tasks":
                    # This is the termination signal
                    self.priority_queue.task_done()
                    
                    # Mark auto-config as completed
                    self.auto_config_completed = True
                    
                    # Check if is_first_config is set, otherwise get it from database
                    if not hasattr(self, 'is_first_config') or self.is_first_config is None:
                        # Get from database or set a default
                        reconfig_doc = await self.mongo_db.get_one(
                            {"project_id": self.project_id},
                            self.mongo_db.db["reconfig_tracker"]
                        )
                        print("reconfig_doc",reconfig_doc)
                        
                        
                        if reconfig_doc:
                            last_successful = reconfig_doc["last_successful"]
                            self.is_first_config = not reconfig_doc or not last_successful
                        else:
                            self.is_first_config = True
                    
                    # Now process based on first config status
                    if last_successful == 0:
                        print("last_successful",last_successful)
                        success = len(self.completed_configs) == len(self.config_types)
                        print("success",success)
                        print("self.completed_configs",self.completed_configs)
                        await self.complete_reconfiguration(self.project_id)
                        
                        if success:
                            self.update_logger_agent.info(f"First-time auto-configuration completed successfully for project {self.project_id}")
                        else:
                            self.update_logger_agent.warning(f"First-time auto-configuration completed with issues for project {self.project_id}")
                    else:
                        # For reconfigs, we wait for manual approval
                        # await self.approve_reconfiguration()
                        self.update_logger_agent.info(f"Reconfiguration completed, awaiting manual approval for project {self.project_id}")
                        
                    return
                    
                # Normal work item processing
                agent = self.agents.get(work_item_dict.agent)
                if agent:
                    await agent.process_work_item(work_item_dict)
                
                self.priority_queue.task_done()
                
            except Exception as e:
                self.update_logger_agent.info(f"Error processing work item: {str(e)}")
                
    async def configure(self, node_id, node_type):
        self.node_id = node_id
        self.node_type = node_type
        
        # Configure the architecture
        await self.configure_node(self.node_id, self.node_type, self.root_node_type, "configuration")

        # After configuring the node, update its interfaces
        await self.update_interfaces(self.node_id)

        await self.supervisor.notify_complete(self.current_work_item)
        return
        
    async def approve_reconfiguration(self):
        """Handle user approval of reconfiguration"""
        if self.is_first_config or self.auto_config_completed:
            # For first-time configs, already handled in run()
            # For normal reconfigs, update the tracker to mark as successful
            await self.complete_reconfiguration(self.project_id, True)
            self.update_logger_agent.info(f"Reconfiguration approved for project {self.project_id}")
        else:
            self.update_logger_agent.warning(f"Cannot approve incomplete reconfiguration for project {self.project_id}")
            
    async def complete_reconfiguration(self, project_id, success=True):
        """Complete reconfiguration and update reconfig tracker"""
        try:
            status = "completed" if success else "interrupted"
            
            # Get current reconfig info
            reconfig_doc = await self.mongo_db.get_one(
                {"project_id": project_id},
                self.mongo_db.db["reconfig_tracker"]
            )
            
            update_data = {
                "status": status,
                "completed_at": datetime.now().isoformat()
            }
            
            # Update last_successful only if successful
            if success:
                update_data["last_successful"] = reconfig_doc["current_reconfig"]
            print("update_data",update_data)
            # Update tracker
            await self.mongo_db.update_one(
                    {"project_id": project_id},
                    update_data,
                    False,  # upsert parameter
                    self.mongo_db.db["reconfig_tracker"]  # db parameter
                )
            
            return {
                "status": status,
                "reconfig_num": reconfig_doc["current_reconfig"],
                "last_successful": update_data.get("last_successful", reconfig_doc.get("last_successful", 0))
            }
            
        except Exception as e:
            self.update_logger_agent.error(f"Error completing reconfiguration: {str(e)}")
            raise

    async def update_interfaces(self, node_id):
        # Get all relationships involving this node
        relationships = await self.db.get_relationships_involving_node(node_id, "INTERFACES_WITH")

        for relationship in relationships:
            if relationship['target'] == node_id:
                # This is an incoming interface, update the interface node
                interface_nodes = await self.db.get_child_nodes(node_id, "Interface")
                if interface_nodes:
                    interface_node = interface_nodes[0]
                    incoming_interfaces = json.loads(interface_node['properties'].get('incoming_interfaces', '[]'))
                    
                    # Update or add this interface
                    updated = False
                    for interface in incoming_interfaces:
                        if interface['source_node_id'] == relationship['source']:
                            interface['interface_details'] = relationship['properties']
                            updated = True
                            break
                    
                    if not updated:
                        source_node = await self.db.get_node_by_id(relationship['source'])
                        incoming_interfaces.append({
                            "source_node_id": relationship['source'],
                            "source_node_title": source_node['properties']['Title'],
                            "interface_details": relationship['properties']
                        })

                    # Update the Interface node
                    await self.db.update_node_by_id(interface_node['id'], {"incoming_interfaces": json.dumps(incoming_interfaces)})

                    # Reconfigure the interface node
                    await self.configure_node(interface_node['id'], "Interface", self.root_node_type, "design_details")
    
    async def create_documentation(self, node_id, node_type):
        """Creates SAD documentation for a given node"""
        self.node_id = node_id
        self.node_type = node_type
        
        # Create a work item for documentation creation
        work_item = self.create_work_item("documentation_autoconfig", None, 1, node_id, node_type, self.project_id)
        await self.add_work_item(work_item)
        return

    async def create_prd_documentation(self, node_id, node_type):
        """Creates PRD documentation for a given node"""
        self.node_id = node_id
        self.node_type = node_type
        
        # Create a work item for documentation creation
        work_item = self.create_work_item("prd_documentation_autoconfig", None, 1, node_id, node_type, self.project_id)
        await self.add_work_item(work_item)
        return

    async def update_progress_based_on_config_types(self):
        """
        Update task progress based on completed configuration types.
        Each config type contributes to overall progress.
        Progress is output as a whole number.
        """
        try:
            if not hasattr(self, 'mongo_handler') or not self.task_id:
                return
                
            # Define progress weights for each configuration type
            # Adjust these weights based on importance and effort required
            progress_weights = {
                "project_autoconfig": 5,
                "work_item_autoconfig": 5,
                "requirements_autoconfig": 10,
                "epic_autoconfig": 5,
                "user_story_autoconfig": 5,
                "capture_requirements": 10,
                "system_context_config": 15,
                "container_config": 15,
                "components_autoconfig": 10,
                "interface_autoconfig": 5,
                "design_autoconfig": 10,
                "documentation_autoconfig": 5,
                "container_ops_autoconfig": 5,
                "termination": 0  # No progress contribution
            }
            
            # Calculate total weight of all config types in the current configuration
            total_weight = 0
            for config_type in self.config_types:
                total_weight += progress_weights.get(config_type, 5)  # Default weight of 5
                
            if total_weight == 0:
                total_weight = 100  # Prevent division by zero
                
            # Calculate progress based on completed configs
            current_progress = 0
            for completed_config in self.completed_configs:
                current_progress += progress_weights.get(completed_config, 5)
                
            # Convert to percentage, max 99% until final completion
            progress_percentage = min((current_progress / total_weight) * 100, 99)
            
            # Set minimum progress to 5% once started
            if progress_percentage > 0:
                progress_percentage = max(progress_percentage, 5)
                
            # Special case: if we're at the last config type, bump to 90%
            if len(self.completed_configs) == len(self.config_types) - 1:
                progress_percentage = max(progress_percentage, 90)
            
            # Round to a whole number
            progress_percentage = int(round(progress_percentage))
            
            # Update the database
            await self.mongo_handler.update_by_task_id(
                self.task_id,
                {'progress': progress_percentage}
            )
            
            # Log progress update
            self.update_logger_agent.info(
                f"Updated progress to {progress_percentage}% - " +
                f"Completed {len(self.completed_configs)}/{len(self.config_types)} configs"
            )
            
            # Send update notification
            await self.send_progress_update()
            
        except Exception as e:
            self.update_logger_agent.error(f"Error updating progress: {str(e)}")

    async def send_progress_update(self):
        """Send progress update via websocket"""
        try:
            for agent_name, agent in self.agents.items():
                if hasattr(agent, 'send_update') and callable(agent.send_update):
                    await agent.send_update(self.task_id)
        except Exception as e:
            self.update_logger_agent.error(f"Error sending progress update: {str(e)}")

    