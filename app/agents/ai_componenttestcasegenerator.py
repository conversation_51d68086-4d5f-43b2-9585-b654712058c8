import asyncio
from app.agents.agent import Agent
from app.discussions.discussion import Discussion
from app.telemetry.logger_config import get_logger, set_task_id
from app.discussions.discussion_util import conduct_discussion
from app.connection.establish_db_connection import get_node_db
from app.core.task_framework import TaskStatus


class ComponentTestCaseGeneratorAgent(Agent):
    def __init__(self, name, node_id, node_type, root_node_type, discussion_type, logging_context, levels, semaphore, supervisor=None):
        super().__init__(name)
        self.current_step = 0
        self.supervisor = supervisor
        self.node_id = node_id
        self.node_type = node_type
        self.root_node_type = root_node_type
        self.discussion_type = discussion_type
        self.db = get_node_db()
        self.semaphore = semaphore
        self.levels = levels
        self.update_logger_agent = get_logger(__name__)

        if logging_context:
            self.logging_context = logging_context
            self.mongo_handler = logging_context.get('mongo_handler')
            self.task_id = logging_context.get('task_id')
            set_task_id(self.task_id)
        else:
            self.logging_context = {}
            self.task_id = None
            self.mongo_handler = None
        self.queue: asyncio.Queue = asyncio.Queue()

    async def process_work_item(self, work_item_dict):
        try:
            self.current_work_item = work_item_dict
            self.update_logger_agent.info(f"Processing WorkItem: {work_item_dict.entry} for node {work_item_dict.node_id}")
            if work_item_dict.entry == "generate_component_test_cases":
                await self.generate_component_test_cases(work_item_dict.node_id, work_item_dict.node_type)
            else:
                self.update_logger_agent.warning(f"Task {work_item_dict.entry} not found for agent {self.name}")
        except Exception as e:
            self.update_logger_agent.error(f"Error processing work item: {str(e)}", exc_info=True)

    async def generate_component_test_cases(self, node_id, node_type):
        try:
            self.node_id = node_id
            self.node_type = node_type
            self.update_logger_agent.info(f"Generating test cases for component: {node_id}")
            await self.configure_node(node_id, node_type="Architecture", root_node_type="Project", configuration_type='component_testcase_generation')
            await self.supervisor.notify_complete(self.current_work_item)
        except Exception as e:
            self.update_logger_agent.error(f"Error generating test cases for component {node_id}: {str(e)}", exc_info=True)
            # Optionally, you could notify the supervisor of the failure
            await self.supervisor.notify_failure(self.current_work_item, str(e))

    async def create_components_config(self, node_id, node_type, project_id):
        """Helper method to configure all components in C4 model"""
        try:
            self.update_logger_agent.info(f"Starting component configuration for project {project_id}")
            
            # Get all components across all containers
            query = """
            MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)
            -[:HAS_CHILD]->(container:Container)
            -[:HAS_CHILD]->(component:Component)
            WHERE ID(p) = $project_id
            RETURN DISTINCT ID(component) as id, component.Title as title
            """
            components = await self.db.async_run(query, project_id=project_id)
            
            for component in components.data():
                component_id = component['id']
                self.update_logger_agent.info(f"Configuring component {component['title']} ({component_id})")
                
                await self.generate_component_test_cases(component_id, "Component")
                
               
                
            self.update_logger_agent.info("Completed configuring all components")

            await self.supervisor.notify_complete(self.current_work_item)

        except Exception as e:
            self.update_logger_agent.error(f"Error in create_components_config: {str(e)}")
            raise

    async def configure_node(self, node_id, node_type, root_node_type, configuration_type):
        """
        Configure or reconfigure a component node with test cases.
        
        Args:
            node_id: The ID of the component to configure
            node_type: The type of the node (Component in this case)
            root_node_type: The type of the root node (typically Project)
            configuration_type: The type of configuration to perform
        """
        try:
            # Log the start of configuration process
            self.update_logger_agent.info(f"Starting test case generation process for component node: {node_id}")

            # Get or create task configuration from MongoDB
            # This maintains the state and progress of the configuration task
            task_configuration = await self.mongo_handler.get_by_task_id(self.task_id, self.mongo_handler.db)
            
            if not task_configuration:
                # Initialize new task configuration if none exists
                task_configuration = {
                    'task_id': self.task_id, 
                    'queue_messages': [], 
                    'task_status': 'In Progress',
                    'configuration_status': {},
                    'progress': 0
                }
                await self.mongo_handler.insert(task_configuration, self.mongo_handler.db)
                await self.send_update(self.task_id)
                self.update_logger_agent.info(f"Created new task configuration for task {self.task_id}")

            # Extract task tracking information
            self.queue_messages = task_configuration['queue_messages']
            self.configuration_status = task_configuration['configuration_status']

            # Retrieve the node to be configured
            node = await self.db.get_node_by_id(node_id)
            if not node:
                self.update_logger_agent.error(f"Node {node_id} not found")
                self.configuration_status[str(node_id)] = {
                    'node_type': node_type,
                    'title': 'Node not found',
                    configuration_type: 'not_found'
                }
                await self.mongo_handler.update_by_task_id(self.task_id, 
                                                        {'configuration_status': self.configuration_status})
                await self.send_update(self.task_id)
                return None

            # Update configuration status tracking
            # This helps in monitoring the progress of configuration
            if not self.configuration_status.get(str(node_id)):
                self.configuration_status[str(node_id)] = {
                    'node_type': node_type,
                    'title': node['properties']['Title'],
                    configuration_type: 'configuring'
                }
            else:
                self.configuration_status[str(node_id)]['title'] = node['properties']['Title']
                self.configuration_status[str(node_id)][configuration_type] = 'configuring'

            await self.mongo_handler.update_by_task_id(self.task_id, 
                                                    {'configuration_status': self.configuration_status})
            
            await self.send_update(self.task_id)

            # Get the specific node type from the node's labels
            # This ensures we're using the correct type for the discussion
            node_type = Discussion.get_specific_node_type(node['labels'])
            
            # Initiate the configuration discussion
            # This will handle both initial configuration and reconfiguration
            # The discussion class will manage version comparison and change detection
            await conduct_discussion(
                node_id=node_id,
                node_type=node_type,
                root_node_type=root_node_type,
                discussion_type=configuration_type,
                logging_context=self.logging_context,
                supervisor=self.supervisor
            )

            # Update the configuration status to reflect completion
            self.configuration_status[str(node_id)][configuration_type] = 'configured'
            await self.mongo_handler.update_by_task_id(self.task_id, 
                                                    {'configuration_status': self.configuration_status})
            
            await self.send_update(self.task_id)
            # Log successful completion
            self.update_logger_agent.info(f"Successfully completed test case generation for component {node_id}")
            
            # Retrieve and return the updated node
            node = await self.db.get_node_by_id(node_id)
            return node

        except Exception as e:
            await self.mongo_handler.update_by_task_id(
                    self.task_id, 
                    {
                        'task_status': TaskStatus.FAILED, 
                        'run_completed': True, 
                        'progress': 100
                    }
                )
            # Log any errors that occur during configuration
            self.update_logger_agent.error(f"Error in configure_node for component {node_id}: {str(e)}", 
                                        exc_info=True)
            raise
    
    async def run(self):
        while True:
            try:
                self.current_work_item = await self.queue.get()
                await self.process_work_item(self.current_work_item)
            except Exception as e:
                self.update_logger_agent.error(f"Error in run method: {str(e)}", exc_info=True)
            finally:
                self.queue.task_done() 