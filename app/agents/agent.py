import asyncio
from typing import Dict,Any
from app.celery_app import celery_task_id, get_websocket_session
from app.utils.task_utils import configuration_update
import json

class Agent:
    def __init__(self, name):
        self.name = name
        self.work_queue = asyncio.Queue()
        self.task_id = celery_task_id.get()
        self.status = 1
        self.ws_client = get_websocket_session(self.task_id)

        
    async def run(self):
        while True:
            work_item = await self.work_queue.get()
            await self.process_work_item(work_item)
            self.work_queue.task_done()

    async def handle_work_item(self, work_item_dict: dict):
        task_method = getattr(self, work_item_dict['task'], None)
        if task_method:
            await task_method(work_item_dict['node_id'], **work_item_dict.get('kwargs', {}))
        else:
            print(f"Task {work_item_dict['task']} not found for agent {self.name}")
    
    async def send_update(self, task_id:str):
        configuration_status = await configuration_update(task_id)
        self.ws_client.send_message("auto_configuration_update", json.dumps(configuration_status))
    
        