import asyncio
from app.agents.agent import Agent
from app.discussions.discussion import Discussion
from app.telemetry.logger_config import get_logger, set_task_id
from app.discussions.discussion_util import conduct_discussion
from app.connection.establish_db_connection import get_node_db
from app.core.task_framework import TaskStatus


class TestCaseGeneratorAgent(Agent):
    def __init__(self, name, node_id, node_type, root_node_type, discussion_type, logging_context, levels, semaphore, supervisor=None):
        super().__init__(name)
        self.current_step = 0
        self.supervisor = supervisor
        self.node_id = node_id
        self.node_type = node_type
        self.root_node_type = root_node_type
        self.discussion_type = discussion_type
        self.db = get_node_db()
        self.semaphore = semaphore
        self.levels = levels
        self.update_logger_agent = get_logger(__name__)

        if logging_context:
            self.logging_context = logging_context
            self.mongo_handler = logging_context.get('mongo_handler')
            self.task_id = logging_context.get('task_id')
            set_task_id(self.task_id)
        else:
            self.logging_context = {}
            self.task_id = None
            self.mongo_handler = None
        self.queue: asyncio.Queue = asyncio.Queue()

    async def process_work_item(self, work_item_dict):
        try:
            self.current_work_item = work_item_dict
            self.update_logger_agent.info(f"Processing WorkItem: {work_item_dict.entry} for node {work_item_dict.node_id}")
            if work_item_dict.entry == "generate_test_cases":
                await self.generate_test_cases(work_item_dict.node_id, work_item_dict.node_type)
            else:
                self.update_logger_agent.warning(f"Task {work_item_dict.entry} not found for agent {self.name}")
        except Exception as e:
            self.update_logger_agent.error(f"Error processing work item: {str(e)}", exc_info=True)

    async def get_epics_for_project(self, project_id):
        """Configure containers in C4 model"""
        try:
            # Get system context
            requirementroot = await self.db.get_child_nodes(project_id, "RequirementRoot")
            if not requirementroot:
                raise ValueError("RequirementRoot not found")
            
            requirementroot = requirementroot[0]
            
            # Get existing containers
            epics = await self.db.get_child_nodes(requirementroot['id'], "Epic")

            return epics

        except Exception as e:
            self.update_logger_agent.error(
                f"Error getting epics for project {project_id}: {str(e)}", 
                exc_info=True
            )
            return []
        
    async def generate_test_cases(self, node_id, node_type):
        try:
            self.node_id = node_id
            self.node_type = node_type
            self.update_logger_agent.info(f"Generating test cases for node: {node_id}")
            # Get node first to validate it's a UserStory
            node = await self.db.get_node_by_id(node_id)
            if not node:
                self.update_logger_agent.error(f"Node {node_id} not found")
                raise ValueError(f"Node {node_id} not found")
                
            # Validate that the node is a UserStory
            if 'UserStory' not in node.get('labels', []):
                # First get all epics
                epics = await self.get_epics_for_project(node_id)
                
                self.update_logger_agent.info(
                    f"Found {len(epics)} epics in project {node_id}"
                )
                
                total_stories_configured = 0
                
                # Process each epic
                for epic in epics:
                    try:
                        epic_id = epic['id']
                        epic_title = epic['properties'].get('Title', 'Untitled Epic')
                        
                        self.update_logger_agent.info(
                            f"Processing user stories for epic: {epic_title} ({epic_id})"
                        )
                        
                        # Get user stories for this epic
                        user_stories = await self.db.get_child_nodes(epic_id, "UserStory")

                        # Add parent configuration check
                        # parents_configured, message = await self.check_parent_configurations(user_stories[0]['id'])
                        # if not parents_configured:
                        #     self.update_logger_agent.warning(f"Cannot process work item: {message}")
                        #     # Notify supervisor that this task cannot proceed
                        #     await self.supervisor.notify_complete(self.current_work_item, success=False, error_message=message)
                        #     return
                        
                        self.update_logger_agent.info(
                            f"Found {len(user_stories)} user stories in epic {epic_title}"
                        )
                        
                        # Configure each user story in this epic
                        for story in user_stories:
                            try:
                                story_id = story['id']
                                story_title = story['properties'].get('title', 'Untitled Story')
                                
                                self.update_logger_agent.info(
                                    f"Configuring user story: {story_title} ({story_id}) in epic {epic_title}"
                                )
                                
                                await self.configure_node(
                                    node_id=story_id,
                                    node_type="UserStory",
                                    root_node_type="Project",
                                    configuration_type='testcase_generation'
                                )
                            
                                
                            except Exception as story_error:
                                self.update_logger_agent.error(
                                    f"Error configuring user story {story_id}: {str(story_error)}", 
                                    exc_info=True
                                )
                                await self.update_story_status(
                                    story_id, 
                                    "ConfigurationFailed",
                                    epic_id=epic_id
                                )
                                continue
                                    
                    except Exception as epic_error:
                        self.update_logger_agent.error(
                            f"Error processing epic {epic_id}: {str(epic_error)}", 
                            exc_info=True
                        )
                        continue
            else:
                # Always use UserStory as the node type for test case generation
                await self.configure_node(node_id, node_type="UserStory", root_node_type="Project", configuration_type='testcase_generation')
                await self.supervisor.notify_complete(self.current_work_item)

                # self.update_logger_agent.error(f"Node {node_id} is not a UserStory. Labels: {node.get('labels', [])}")
                # raise ValueError(f"Test case generation can only be performed on UserStory nodes")
            
            
        except Exception as e:
            self.update_logger_agent.error(f"Error generating test cases for node {node_id}: {str(e)}", exc_info=True)
            # Optionally, you could notify the supervisor of the failure
            await self.supervisor.notify_failure(self.current_work_item, str(e))

    async def configure_node(self, node_id, node_type, root_node_type, configuration_type):
        """
        Configure or reconfigure a requirement root node. This method handles both initial configuration 
        and subsequent reconfigurations based on system changes.
        
        Args:
            node_id: The ID of the node to configure
            node_type: The type of the node (RequirementRoot in this case)
            root_node_type: The type of the root node (typically Project)
            configuration_type: The type of configuration to perform
        """
        try:
            # Log the start of configuration process
            self.update_logger_agent.info(f"Starting configuration process for requirement root node: {node_id}")

            # Get or create task configuration from MongoDB
            # This maintains the state and progress of the configuration task
            task_configuration = await self.mongo_handler.get_by_task_id(self.task_id, self.mongo_handler.db)
            
            if not task_configuration:
                # Initialize new task configuration if none exists
                task_configuration = {
                    'task_id': self.task_id, 
                    'queue_messages': [], 
                    'task_status': 'In Progress',
                    'configuration_status': {},
                    'progress': 0
                }
                await self.mongo_handler.insert(task_configuration, self.mongo_handler.db)
                await self.send_update(self.task_id)
                self.update_logger_agent.info(f"Created new task configuration for task {self.task_id}")

            # Extract task tracking information
            self.queue_messages = task_configuration['queue_messages']
            self.configuration_status = task_configuration['configuration_status']

            # Retrieve the node to be configured
            node = await self.db.get_node_by_id(node_id)
            if not node:
                self.update_logger_agent.error(f"Node {node_id} not found")
                self.configuration_status[str(node_id)] = {
                    'node_type': node_type,
                    'title': 'Node not found',
                    configuration_type: 'not_found'
                }
                await self.mongo_handler.update_by_task_id(self.task_id, 
                                                        {'configuration_status': self.configuration_status})
                await self.send_update(self.task_id)
                return None

            # Update configuration status tracking
            # This helps in monitoring the progress of configuration
            if not self.configuration_status.get(str(node_id)):
                self.configuration_status[str(node_id)] = {
                    'node_type': node_type,
                    'title': node['properties']['Title'],
                    configuration_type: 'configuring'
                }
            else:
                self.configuration_status[str(node_id)]['title'] = node['properties']['Title']
                self.configuration_status[str(node_id)][configuration_type] = 'configuring'

            await self.mongo_handler.update_by_task_id(self.task_id, 
                                                    {'configuration_status': self.configuration_status})
            
            await self.send_update(self.task_id)

            # Ensure we're using the correct node type - always UserStory for test case generation
            # Do NOT get node type from labels as that might return Epic incorrectly
            self.update_logger_agent.info(f"Using node_type: {node_type} for test case generation")
            
            # Initiate the configuration discussion
            # This will handle both initial configuration and reconfiguration
            # The discussion class will manage version comparison and change detection
            await conduct_discussion(
                node_id=node_id,
                node_type=node_type,  # Always use the explicitly passed node_type (UserStory)
                root_node_type=root_node_type,
                discussion_type=configuration_type,
                logging_context=self.logging_context,
                supervisor=self.supervisor
            )

            # Update the configuration status to reflect completion
            self.configuration_status[str(node_id)][configuration_type] = 'configured'
            await self.mongo_handler.update_by_task_id(self.task_id, 
                                                    {'configuration_status': self.configuration_status})
            
            await self.send_update(self.task_id)
            # Log successful completion
            self.update_logger_agent.info(f"Successfully completed configuration for node {node_id}")
            
            # Retrieve and return the updated node
            node = await self.db.get_node_by_id(node_id)
            return node

        except Exception as e:
            await self.mongo_handler.update_by_task_id(
                    self.task_id, 
                    {
                        'task_status': TaskStatus.FAILED, 
                        'run_completed': True, 
                        'progress': 100
                    }
                )
            # Log any errors that occur during configuration
            self.update_logger_agent.error(f"Error in configure_node for node {node_id}: {str(e)}", 
                                        exc_info=True)
            raise
    
    async def run(self):
        while True:
            try:
                self.current_work_item = await self.queue.get()
                await self.process_work_item(self.current_work_item)
            except Exception as e:
                self.update_logger_agent.error(f"Error in run method: {str(e)}", exc_info=True)
            finally:
                self.queue.task_done()