
class WorkItem:
   def __init__(self, config_type, parent,step,agent,entry,levels,node_id,node_type,project_id):
       self.parent = parent
       self.step = step
       self.agent = agent
       self.entry = entry
       self.config_type = config_type
       self.levels = levels
       self.node_id = node_id
       self.node_type = node_type
       self.project_id = project_id
   def __le__(self, other):
       return True
   def __ge__(self, other):
       return True
   def __gt__(self, other):
       return False
   def __lt__(self, other):
       return False
   def __eq__(self, other):
       return True
   def __ne__(self, other):
       return False