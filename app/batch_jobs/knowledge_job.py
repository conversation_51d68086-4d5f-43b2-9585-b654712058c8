import asyncio
import traceback

from pydantic import BaseModel
from typing import  Dict, List, Optional
from fastapi import  APIRouter, Depends, Request, HTTPException,  BackgroundTasks
from fastapi.responses import StreamingResponse
from app.connection.establish_db_connection import get_mongo_db
from app.core.Settings import Settings
from app.utils.auth_utils import get_current_user
import uuid
from app.utils.kg_inspect.knowledge import Knowledge, KnowledgeCodeBase
from app.utils.kg_inspect.knowledge_helper import Knowledge_Helper
from app.utils.kg_inspect.knowledge_reporter import Reporter
from app.telemetry.logger_config import set_task_id, setup_logging
import logging
import os
from datetime import datetime, timedelta
import time
from datetime import datetime
from app.core.websocket.client import WebSocketClient
from app.core.Settings import settings
from app.connection.tenant_middleware import tenant_context
import sys

setup_logging()


# Get all arguments from command line
if len(sys.argv) < 6:
    print("Not enough arguments provided")
    sys.exit(1)
    
session_id = sys.argv[1]
user_id = sys.argv[2]
tenant_id = sys.argv[3]
project_id = int(sys.argv[4])
build_ids = sys.argv[5].split(',')
session_name = sys.argv[6] if len(sys.argv) > 6 else "untitled"
description = sys.argv[7] if len(sys.argv) > 7 else ""

set_task_id(session_id)

ws_client: WebSocketClient = WebSocketClient(session_id, settings.WEBSOCKET_URI)

input_data = {
    "session_id": session_id,
    "user_id": user_id,
    "tenant_id": tenant_id,
    "project_id": project_id,
    "build_ids": build_ids,
    "session_name": session_name,
    "description": description
}

print(input_data)
    
user_id = input_data.get("user_id")
session_id = input_data.get("session_id")
session_name=input_data.get("session_name")
description=input_data.get("description")
tenant_id = input_data.get("tenant_id")
project_id = input_data.get("project_id")
build_ids = input_data.get("build_ids")
tenant_context.set(tenant_id)
print(session_name,description)
# Connect to MongoDB
mongo_handler = get_mongo_db().db

print("initialize:",mongo_handler)
# Get the document for the project
project_data = mongo_handler["project_repositories"].find_one({"project_id": project_id})

print("initialize:",project_data)
if not project_data:
    raise HTTPException(
        status_code=404,
        detail=f"Project with ID {project_id} not found"
    )

# Initialize lists to store build paths and incomplete builds
build_paths = []
incomplete_builds = []

# Iterate through repositories and their branches to find matching build IDs
for repo in project_data.get('repositories', []):
    for branch in repo.get('branches', []):
        branch_name = branch['name']
        build_info = branch.get('builds', {})
        if build_info.get('build_id') in build_ids:

            # Track incomplete builds
            if build_info.get('kg_creation_status') != 2:
                incomplete_builds.append({
                    'build_id': build_info.get('build_id'),
                    'status': build_info.get('kg_creation_status')
                })
                continue

            # First check build_path in builds object
            build_path = build_info.get('path')

            if build_path:
                os.chdir(build_path)
                os.system(f'git stash')
                os.system(f'git pull --rebase')
                os.system(f'git switch {branch_name}')
                os.system(f'git stash pop')
                os.chdir(os.path.dirname(os.path.dirname(build_path)))
                
                lock_file = os.path.join(build_path, '.knowledge', '.vector_db', '.milvus.db.lock')

                # Check if lock file exists and remove it
                if os.path.exists(lock_file):
                    try:
                        os.remove(lock_file)
                        print(f"Successfully removed {lock_file}")
                    except Exception as e:
                        print(f"Error removing lock file: {e}")
                    
                build_paths.append({
                    'build_id': build_info['build_id'],
                    'path': build_path,
                    'repo_url': repo['git_url'],
                    'branch': branch['name']
                })
                
                # TODO: Need to switch to the branch

# Check if there are any incomplete builds
if incomplete_builds:
    raise HTTPException(
        status_code=404,
        detail={
            "message": "Knowledge generation is not complete for some builds",
            "incomplete_builds": incomplete_builds
        }
    )

# Check if we found any valid build paths
if not build_paths:
    raise HTTPException(
        status_code=404,
        detail="No valid build paths found for the specified build IDs"
    )

codebase_paths = [
    KnowledgeCodeBase(
        build_path["path"], f"{build_path['repo_url']}:{build_path['branch']}",
    )
    for build_path in build_paths
]




reporter = Reporter(ws_client)
reporter.initialize()

reporter.ws_client.send_message("code_query", {"message": 'NOT STARTED', "session_id": session_id})

knowledge_helper: Knowledge_Helper = Knowledge_Helper(session_id, reporter, os.getcwd() , codebase_paths)

reporter.ws_client.send_message("code_query", {"message": 'NOT STARTED - Initializing Knowledge', "session_id": session_id})

total_files = 0
for base in codebase_paths:
    count = len(knowledge_helper.knowledge._list_important_files(base.base_path))
    total_files += count 

reporter.ws_client.send_message("code_query", {"message": 'NOT STARTED - STARTING KNOWLEDGE', "session_id": session_id})
knowledge_helper.knowledge.start()

reporter.ws_client.send_message("code_query", {"message": 'NOT STARTED - KNOWLEDGE CREATION COMPLETE', "session_id": session_id})
    

# Get the document for the project
project_data = mongo_handler["code_query_session"].insert_one(
    {
        "session_id": session_id,
        "build_ids": build_ids,
        "start_time": datetime.now(),
        "session_name":session_name,
        "description":description
    }
)


# Before the while loop
start_time = time.time()
start_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
print(f"Start time: {start_datetime}")
print("Total Files: ", total_files)
reporter.ws_client.send_message("code_query", {"message": f'NOT STARTED - KNOWLEDGE CREATION COMPLETE - {start_datetime} - {total_files} ', "session_id": session_id})
init_message_sent = False


while( True ):

    if reporter.is_ready():
        if not init_message_sent:
            
            reporter.send_message("code_query", {"message": 'STARTED', "session_id": session_id})
            init_message_sent = True
            continue
        continue





