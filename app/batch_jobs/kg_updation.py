import subprocess
import os
import sys
import time
import json
import asyncio
import argparse
from datetime import datetime, timedelta
from pathlib import Path

# Add your project path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import with retry logic and exception handling
def import_with_retry(import_func, max_retries=3, delay=2):
    """
    Import modules with retry logic to handle transient import failures

    Args:
        import_func: Function that performs the import
        max_retries: Maximum number of retry attempts
        delay: Delay between retries in seconds

    Returns:
        Result of import_func or None if all retries failed
    """
    for attempt in range(max_retries):
        try:
            return import_func()
        except Exception as e:
            print(f"Import attempt {attempt + 1}/{max_retries} failed: {str(e)}")
            if attempt < max_retries - 1:
                print(f"Retrying in {delay} seconds...")
                time.sleep(delay)
                delay *= 2  # Exponential backoff
            else:
                print(f"All {max_retries} import attempts failed. Last error: {str(e)}")
                raise e

# Import basic dependencies with better error handling
def import_basic_dependencies():
    """Import basic dependencies with error handling"""
    try:
        from app.connection.establish_db_connection import get_mongo_db
        from app.knowledge.redis_kg import add_redis_support_to_knowledge
        from app.utils.kg_build.knowledge import Knowledge, KnowledgeCodeBase
        from app.utils.kg_build.knowledge_helper import Knowledge_Helper
        from app.core.websocket.client import WebSocketClient
        from app.core.Settings import settings
        from app.knowledge.code_query import KnowledegeBuild
        import logging
        from app.celery_app import user_context

        return {
            'get_mongo_db': get_mongo_db,
            'add_redis_support_to_knowledge': add_redis_support_to_knowledge,
            'Knowledge': Knowledge,
            'KnowledgeCodeBase': KnowledgeCodeBase,
            'Knowledge_Helper': Knowledge_Helper,
            'WebSocketClient': WebSocketClient,
            'settings': settings,
            'KnowledegeBuild': KnowledegeBuild,
            'logging': logging,
            'user_context': user_context
        }
    except Exception as e:
        print(f"❌ Failed to import basic dependencies: {e}")
        raise

# Import dependencies when needed
_dependencies = None

def get_dependencies():
    """Get dependencies, importing them if needed"""
    global _dependencies
    if _dependencies is None:
        _dependencies = import_basic_dependencies()
        print("✅ Basic imports successful")
    return _dependencies

# Import Reporter with retry logic due to complex dependency chain
def import_reporter():
    from app.utils.kg_build.knowledge_reporter import Reporter
    return Reporter

def get_reporter():
    """Get Reporter class, importing with retry if needed"""
    try:
        return import_with_retry(import_reporter, max_retries=3, delay=2)
    except Exception as e:
        print(f"❌ Failed to import Reporter after retries: {e}")
        raise

# Enhanced GitHub API rate limit handling
def check_github_rate_limit(github_token=None):
    """
    Check GitHub API rate limit status

    Args:
        github_token: GitHub token for authentication

    Returns:
        tuple: (can_proceed: bool, remaining_requests: int, reset_time: int)
    """
    import requests

    try:
        headers = {}
        if github_token:
            headers["Authorization"] = f"token {github_token}"

        response = requests.get("https://api.github.com/rate_limit", headers=headers, timeout=10)

        if response.status_code == 200:
            data = response.json()
            core_limit = data.get("resources", {}).get("core", {})
            remaining = core_limit.get("remaining", 0)
            reset_time = core_limit.get("reset", 0)

            # Allow operation if we have at least 10 requests remaining
            can_proceed = remaining >= 10

            print(f"GitHub API Rate Limit - Remaining: {remaining}, Reset: {reset_time}")
            return can_proceed, remaining, reset_time
        else:
            print(f"Failed to check rate limit: HTTP {response.status_code}")
            return True, 0, 0  # Assume we can proceed if check fails

    except Exception as e:
        print(f"Error checking GitHub rate limit: {e}")
        return True, 0, 0  # Assume we can proceed if check fails

def wait_for_rate_limit_reset(reset_time, max_wait=300):
    """
    Wait for GitHub rate limit to reset

    Args:
        reset_time: Unix timestamp when rate limit resets
        max_wait: Maximum time to wait in seconds (default: 5 minutes)
    """
    import time

    current_time = int(time.time())
    wait_time = min(reset_time - current_time + 60, max_wait)  # Add 60s buffer

    if wait_time > 0:
        print(f"Rate limit exceeded. Waiting {wait_time} seconds for reset...")
        time.sleep(wait_time)
    else:
        print("Rate limit should be reset now, proceeding...")

def set_tenant_context(tenant_id):
    """Set tenant ID in environment variables for get_tenant_id() function"""
    try:
        # Get existing input_arguments or create new ones
        existing_args = json.loads(os.environ.get("input_arguments", "{}"))
        
        # Update with tenant_id
        existing_args["tenant_id"] = tenant_id
        
        # Set back to environment
        os.environ["input_arguments"] = json.dumps(existing_args)
        
        print(f"✅ Tenant ID set in environment: {tenant_id}")
        print(f"📄 Updated input_arguments: {os.environ.get('input_arguments')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting tenant context: {e}")
        return False

def set_user_context(user_id):
    """Set user ID in the context variable"""
    from app.connection.tenant_middleware import user_id_context
    user_id_context.set(user_id)
    return True

async def run_knowledge_update_with_retry(data, max_retries=3):
    """
    Run the knowledge update process with GitHub rate limit retry logic

    Args:
        data: Knowledge update data
        max_retries: Maximum number of retries for rate limit issues
    
    Returns:
        tuple: (success: bool, elapsed_time: float in seconds)
    """
    start_time = time.time()  # Record the start time
    
    print(f"Started knowledge update at: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
    
    for attempt in range(max_retries):
        try:
            print(f"Knowledge update attempt {attempt + 1}/{max_retries}")
            attempt_start_time = time.time()  # Record start time of this attempt

            # Check GitHub rate limit before starting
            try:
                deps = get_dependencies()
                # Use token rotation for better rate limit handling
                from app.knowledge.code_query import get_best_github_token
                github_token = data.get('repo', {}).get('github_token') or get_best_github_token()
            except Exception as e:
                print(f"Warning: Could not get settings, using token from data: {e}")
                github_token = data.get('repo', {}).get('github_token')
            can_proceed, remaining, reset_time = check_github_rate_limit(github_token)

            if not can_proceed:
                if attempt < max_retries - 1:
                    print(f"Rate limit exceeded on attempt {attempt + 1}. Waiting for reset...")
                    wait_for_rate_limit_reset(reset_time)
                    continue
                else:
                    raise Exception("GitHub API rate limit exceeded and max retries reached")

            # Execute the actual knowledge update
            await run_knowledge_update(data)
            
            # Calculate time for this successful attempt
            attempt_elapsed_time = time.time() - attempt_start_time
            print(f"✅ Knowledge update completed successfully on attempt {attempt + 1}")
            print(f"   Time taken for successful attempt: {attempt_elapsed_time:.2f} seconds")
            
            # Calculate total elapsed time
            end_time = time.time()
            total_elapsed_time = end_time - start_time
            
            # Format elapsed time in a human-readable format
            hours, remainder = divmod(total_elapsed_time, 3600)
            minutes, seconds = divmod(remainder, 60)
            
            print(f"✅ Total knowledge update process completed at: {datetime.fromtimestamp(end_time).strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⏱️ Total elapsed time: {int(hours)}h {int(minutes)}m {seconds:.2f}s ({total_elapsed_time:.2f} seconds)")
            
            # Add timing data to the data structure for possible logging
            data['timing'] = {
                'start_time': start_time,
                'end_time': end_time,
                'total_seconds': total_elapsed_time,
                'formatted': f"{int(hours)}h {int(minutes)}m {seconds:.2f}s"
            }
            
            return True, total_elapsed_time

        except Exception as e:
            error_msg = str(e).lower()
            attempt_elapsed_time = time.time() - attempt_start_time
            print(f"   Time taken for failed attempt: {attempt_elapsed_time:.2f} seconds")

            # Check if it's a rate limit error
            if any(keyword in error_msg for keyword in ['rate limit', 'forbidden', '403', 'abuse']):
                print(f"Rate limit error on attempt {attempt + 1}/{max_retries}: {e}")

                if attempt < max_retries - 1:
                    # Wait with exponential backoff
                    wait_time = min(2 ** attempt * 60, 300)  # 1min, 2min, 4min (max 5min)
                    print(f"Waiting {wait_time} seconds before retry...")
                    time.sleep(wait_time)
                    continue
                else:
                    print(f"All {max_retries} attempts failed due to rate limiting")
                    raise e
            else:
                # Non-rate-limit error, re-raise immediately
                print(f"Non-rate-limit error on attempt {attempt + 1}: {e}")
                raise e

    # If we get here, all attempts have failed
    end_time = time.time()
    total_elapsed_time = end_time - start_time
    
    # Format elapsed time in a human-readable format
    hours, remainder = divmod(total_elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print(f"❌ All attempts failed. Process ended at: {datetime.fromtimestamp(end_time).strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⏱️ Total time spent: {int(hours)}h {int(minutes)}m {seconds:.2f}s ({total_elapsed_time:.2f} seconds)")
    
    # Add timing data to the data structure for possible logging
    data['timing'] = {
        'start_time': start_time,
        'end_time': end_time,
        'total_seconds': total_elapsed_time,
        'formatted': f"{int(hours)}h {int(minutes)}m {seconds:.2f}s",
        'status': 'failed'
    }
    
    raise Exception(f"Knowledge update failed after {max_retries} attempts. Total time: {data['timing']['formatted']}")


def detect_modified_files(repo_path, verbose=True):
    """
    Detect files that have been modified and need knowledge update,
    focusing on actual changes in the latest commit or pull
    
    Args:
        repo_path: Path to the git repository
        verbose: Whether to print detailed information
        
    Returns:
        list: List of modified file paths
    """
    os.chdir(repo_path)
    modified_files = []
    
    # Save the output of git status to inspect what's happening
    if verbose:
        git_status = os.popen('git status').read()
        print("\n--- Git Status ---\n" + git_status + "\n-------------------\n")
    
    # Strategy 1: Check for changes from the most recent pull/rebase
    if verbose:
        print("Checking for changes from the most recent pull/rebase...")
    
    # Use git diff to show files changed in the most recent commit
    last_commit_files = os.popen('git show --name-only --pretty=format: HEAD').read().strip().split('\n')
    last_commit_files = [f for f in last_commit_files if f.strip()]
    
    if last_commit_files:
        modified_files.extend(last_commit_files)
        if verbose:
            print(f"Found {len(last_commit_files)} files changed in the most recent commit")
    
    # Strategy 2: Check for uncommitted changes
    if verbose:
        print("Checking for uncommitted changes...")
    
    uncommitted_files = os.popen('git diff --name-only HEAD').read().strip().split('\n')
    uncommitted_files = [f for f in uncommitted_files if f.strip()]
    
    if uncommitted_files and uncommitted_files != ['']:
        modified_files.extend(uncommitted_files)
        if verbose:
            print(f"Found {len(uncommitted_files)} uncommitted changed files")
    
    # Strategy 3: Check what was actually modified in the last pull operation
    if verbose:
        print("Checking for changes from the last pull operation...")
    
    try:
        # This finds files that changed between the commit before the last merge and HEAD
        merge_base = os.popen('git merge-base ORIG_HEAD HEAD 2>/dev/null').read().strip()
        if merge_base:
            pull_changes = os.popen(f'git diff --name-only {merge_base} HEAD').read().strip().split('\n')
            pull_changes = [f for f in pull_changes if f.strip()]
            
            if pull_changes and pull_changes != ['']:
                modified_files.extend(pull_changes)
                if verbose:
                    print(f"Found {len(pull_changes)} files changed in the last pull operation")
    except Exception as e:
        if verbose:
            print(f"Error checking merge base: {str(e)}")
    
    # Strategy 4: Use git log to see what files were changed in recent commits
    if not modified_files or modified_files == ['']:
        if verbose:
            print("No changes detected yet. Checking recent commits...")
        
        # Look at files changed in the last 3 commits
        recent_commits = os.popen('git log -n 3 --name-only --pretty=format:').read().strip().split('\n')
        recent_commits = [f for f in recent_commits if f.strip()]
        
        if recent_commits:
            modified_files.extend(recent_commits)
            if verbose:
                print(f"Found {len(recent_commits)} files changed in the last 3 commits")
    
    # Remove duplicates and empty entries
    modified_files = list(set([f for f in modified_files if f.strip()]))
    
    # Filter out files that shouldn't be included in knowledge graph
    excluded_patterns = [
        '.git/', '.github/', 
        '*.md', '*.MD', '*.json', '*.lock', 
        '*.gitignore', 'LICENSE', '.DS_Store'
    ]
    
    # Check if we should exclude .knowledge directory files
    exclude_knowledge_dir = False
    
    # Count how many .knowledge files we have
    knowledge_files = [f for f in modified_files if '.knowledge/' in f]
    if len(knowledge_files) > 0:
        if verbose:
            print(f"\nFound {len(knowledge_files)} files in .knowledge directory")
            print("Note: These files are typically generated during the knowledge build process")
            print("and might not need to be included in the update")
    
    # Start with all files that aren't in the excluded patterns
    filtered_files = []
    for file in modified_files:
        exclude = False
        for pattern in excluded_patterns:
            if pattern.startswith('*.'):
                # Handle file extension patterns
                ext = pattern[1:]  # Remove the *
                if file.endswith(ext):
                    exclude = True
                    break
            elif pattern in file:
                # Handle directory/file patterns
                exclude = True
                break
        
        # Special handling for .knowledge directory
        if '.knowledge/' in file and exclude_knowledge_dir:
            exclude = True
        
        if not exclude:
            filtered_files.append(file)
    
    # Check if there are actual changes to regular code files
    code_files = [f for f in filtered_files if not f.startswith('.knowledge/')]
    knowledge_files = [f for f in filtered_files if f.startswith('.knowledge/')]
    
    if verbose:
        print(f"\nFound {len(modified_files)} total modified files")
        print(f"After filtering: {len(filtered_files)} files will be processed")
        print(f"  - {len(code_files)} regular code files")
        print(f"  - {len(knowledge_files)} .knowledge directory files")
        
        if len(code_files) > 0:
            print("\nCode files to process:")
            for file in code_files[:10]:
                print(f"  - {file}")
            if len(code_files) > 10:
                print(f"  ... and {len(code_files) - 10} more files")
        
        if len(knowledge_files) > 0 and not exclude_knowledge_dir:
            print("\nKnowledge files to process:")
            for file in knowledge_files[:10]:
                print(f"  - {file}")
            if len(knowledge_files) > 10:
                print(f"  ... and {len(knowledge_files) - 10} more files")
    
    # If all we have is .knowledge files and they were newly created,
    # they're likely just outputs of the knowledge generation process
    # Only return actual source code files that need to be processed
    if code_files:
        result_files = code_files
    else:
        # If we only have knowledge files and they're newly created in a commit,
        # don't process them - they're likely just outputs
        newly_created = os.popen('git diff --diff-filter=A --name-only HEAD~1 HEAD').read().strip().split('\n')
        newly_created = [f for f in newly_created if f.strip()]
        
        knowledge_outputs = [f for f in knowledge_files if f in newly_created]
        knowledge_modified = [f for f in knowledge_files if f not in newly_created]
        
        if verbose and knowledge_outputs:
            print(f"\nFound {len(knowledge_outputs)} newly created knowledge files that are likely outputs")
            print("These will be excluded from processing")
        
        # Only include knowledge files that were modified but not newly created
        result_files = knowledge_modified
    
    if verbose:
        print(f"\nFinal list: {len(result_files)} files will be processed")
        if len(result_files) > 0:
            for file in result_files[:10]:
                print(f"  - {file}")
            if len(result_files) > 10:
                print(f"  ... and {len(result_files) - 10} more files")
        else:
            print("No files need to be processed")
    
    return result_files

async def run_knowledge_update(data):
    """Run the knowledge update process with provided data"""
    function_start_time = time.time()
    print(f"Starting knowledge update function at: {datetime.fromtimestamp(function_start_time).strftime('%Y-%m-%d %H:%M:%S')}")

    # Get dependencies
    deps = get_dependencies()
    get_mongo_db = deps['get_mongo_db']
    add_redis_support_to_knowledge = deps['add_redis_support_to_knowledge']
    Knowledge = deps['Knowledge']
    KnowledgeCodeBase = deps['KnowledgeCodeBase']
    Knowledge_Helper = deps['Knowledge_Helper']
    WebSocketClient = deps['WebSocketClient']
    settings = deps['settings']
    KnowledegeBuild = deps['KnowledegeBuild']
    logging = deps['logging']
    user_context = deps['user_context']

    session_id = data['session_id']
    user_id = data['user_id']
    project_id = int(data['project_id'])
    repo = data['repo']
    build_id = data['build_id']
    build_path = data['build_path']
    branch_name = data['branch_name']
    git_url = data['git_url']
    code_base = f"{git_url}:{branch_name}"

    print(f"Starting knowledge update for session: {session_id}")
    print(f"Project ID: {project_id}")
    print(f"Build ID: {build_id}")
    print(f"Build path: {build_path}")
    print(f"Branch name: {branch_name}")
    print(f"run_knowledge_update -> user_id {user_id}")

    # Track timing for major operations
    operation_timings = {}

    try:
        # Initialize session handler
        session_start = time.time()
        session_handler = get_mongo_db(
            db_name=data['settings']['mongo_db_name'],
            collection_name='kg_sessions',
            user_id=user_id
        )
        operation_timings['session_init'] = time.time() - session_start

        # Initialize WebSocket client and reporter
        ws_start = time.time()
        ws_client = WebSocketClient(session_id, data['settings']['websocket_uri'])
        Reporter = get_reporter()
        reporter = Reporter(ws_client)
        operation_timings['websocket_init'] = time.time() - ws_start

        # Initialize KnowledgeBuild for status updates
        kg = KnowledegeBuild()

        reporter.send_message("code_ingestion", {
            'info': "Build_Status_Update",
            'message': 'Starting Update Process',
            'status': 'Success',
            'buildId': build_id
        })
        
        # Update session status to progress
        mongo_start = time.time()
        await session_handler.update_one(
            filter={"session_id": session_id},
            element={
                "session_status": "Progress",
                "updated_at": datetime.utcnow()
            },
            db=session_handler.db
        )
        operation_timings['mongo_update_1'] = time.time() - mongo_start
        
        # Setup the repository
        reporter.send_message("code_ingestion", {
            'info': "Build_Status_Update",
            'message': 'Setting up the repository',
            'status': 'Processing',
            'buildId': build_id
        })

        # Change directory to the build path
        git_start = time.time()
        cwd = os.getcwd()
        os.chdir(build_path)

        try:
            print("Trying to switch to Build Path")
            print("Running OS commands")
            
            # Configure git to allow safe directory
            os.system(f'git config --global --add safe.directory {build_path}')
            
            # Switch to the specified branch
            os.system(f'git switch {branch_name}')
            
            # Get list of modified files before pulling
            modified_files_before = os.popen('git diff --name-only HEAD').read().strip().split('\n')
            
            # Stash any local changes
            os.system('git stash')
            
            # Pull latest changes
            os.system('git pull --rebase')
            
            # Get list of modified files compared to our previous commit
            modified_files_after_pull = os.popen('git diff --name-only HEAD@{1} HEAD').read().strip().split('\n')
            
            # Combine both lists to get all modified files
            all_modified_files = list(set(modified_files_before + modified_files_after_pull))
            
            # Filter out empty entries
            modified_files = [f for f in all_modified_files if f]
            
            # Count of modified files
            modified_files = detect_modified_files(build_path)
            modified_files_count = len(modified_files)

            print(f"Detected {modified_files_count} modified files that need updating:")
            for file in modified_files[:10]:  # Print first 10 files
                print(f"  - {file}")
            if modified_files_count > 10:
                print(f"  ... and {modified_files_count - 10} more files")
            
            # Pop stashed changes
            os.system('git stash pop')
            
            # Remove lock file if exists
            lock_file = os.path.join(build_path, '.knowledge', '.vector_db', '.milvus.db.lock')
            if os.path.exists(lock_file):
                try:
                    os.remove(lock_file)
                    print(f"Successfully removed {lock_file}")
                except Exception as e:
                    print(f"Error removing lock file: {e}")
            
            # Return to original directory
            os.chdir(cwd)
            operation_timings['git_operations'] = time.time() - git_start
            
            reporter.send_message("code_ingestion", {
                'info': "Build_Status_Update",
                'message': 'Setting up the repository',
                'status': 'Success',
                'buildId': build_id
            })
        except Exception as e:
            os.chdir(cwd)  # Ensure we return to original directory even on error
            print(f"Error during git operations: {str(e)}")
            reporter.send_message("code_ingestion", {
                'info': "Build_Status_Update",
                'message': 'Setting up the repository',
                'status': 'Failed',
                'buildId': build_id
            })
            raise Exception(f"Failed to update repository: {str(e)}")
        
        # Create codebase for update
        codebase_start = time.time()
        codebases = [KnowledgeCodeBase(build_path, code_base)]
        operation_timings['codebase_creation'] = time.time() - codebase_start
        
        reporter.send_message("code_ingestion", {
            'info': "Build_Status_Update",
            'message': 'Starting Re-build Process',
            'status': 'Processing',
            'buildId': build_id
        })
        
        # Initialize Knowledge_Helper
        helper_start = time.time()
        knowledge_helper = Knowledge_Helper(
            session_id, 
            reporter, 
            os.getcwd(), 
            codebases, 
            user_id, 
            project_id
        )
        operation_timings['helper_init'] = time.time() - helper_start

        # Get knowledge instance
        knowledge_start = time.time()
        knowledge = Knowledge.getKnowledge(id=session_id)
        add_redis_support_to_knowledge(knowledge)
        operation_timings['knowledge_init'] = time.time() - knowledge_start
        
        # Store modified files count in knowledge for progress tracking
        if modified_files_count > 0:
            knowledge.modified_files_count = modified_files_count
        else:
            # If no modified files detected, use total files as fallback
            knowledge.modified_files_count = knowledge.get_file_count(build_path)
            
        total_files = knowledge.get_file_count(build_path)
        print(f"Total files in repository: {total_files}")
        print(f"Files to be updated: {knowledge.modified_files_count}")
        
        reporter.send_message("code_ingestion", {
            'info': "Build_Status_Update",
            'message': 'Starting Re-build Process',
            'status': 'Success',
            'buildId': build_id
        })
        
        # Start knowledge processing
        processing_start = time.time()
        knowledge.start()
        
        # Update status
        await kg.update_kg_status_by_id(
            1, project_id, build_id, session_id, False, 
            repo.get('service', 'github'), user_id
        )
        
        reporter.send_message("code_ingestion", {
            'info': "Build_Status_Update",
            'message': 'Fetching Progress Data',
            'status': 'Success',
            'buildId': build_id
        })
        
        # Main processing loop
        loop_start = time.time()
        while True:
            await kg.update_build_times(
                project_id, [build_id], "last_updated", 
                False, repo.get('service', 'github'), user_id
            )
            
            if knowledge._state == 2:  # Completed
                # Save to Redis
                redis_start = time.time()
                knowledge.save_to_redis()
                operation_timings['redis_save'] = time.time() - redis_start
                
                # Update end time
                await kg.update_build_times(
                    project_id, [build_id], "end_time", 
                    False, repo.get('service', 'github'), user_id
                )
                
                # Try to commit changes
                commit_start = time.time()
                can_push = await kg.try_to_commit(build_path, branch_name)
                if can_push:
                    # Update commit hash
                    os.chdir(build_path)
                    _hash = os.popen('git rev-parse HEAD').read().strip()
                    await kg.update_commit_hash(_hash, project_id, build_id, user_id)
                    logging.info(f"Successfully pushed changes to {repo.get('repository_name', 'repo')}")
                    await kg.update_kg_status_by_id(2, project_id, build_id, None, False, repo.get('service', 'github'), user_id)
                    
        
                    reporter.send_message("code_ingestion", modified_files_before)
                    print("Knowlege files modified : ", modified_files_before)
                else:
                    logging.info("Error while pushing the code")
                    await kg.update_kg_status_by_id(-1, project_id, build_id, None, False, repo.get('service', 'github'), user_id)
                operation_timings['git_commit'] = time.time() - commit_start
                
                # Update session status to completed
                session_update_start = time.time()
                await session_handler.update_one(
                    filter={"session_id": session_id},
                    element={
                        "session_status": "Completed",
                        "updated_at": datetime.utcnow()
                    },
                    db=session_handler.db
                )
                operation_timings['session_update'] = time.time() - session_update_start

                reporter.send_message("code_ingestion", {'info': 'Repo_Data_Update'})
                print("Knowledge update completed. Exiting...")
                break
            
            await asyncio.sleep(1)
        
        operation_timings['processing_loop'] = time.time() - loop_start
        operation_timings['total_processing'] = time.time() - processing_start
        
        # Cleanup after completion
        cleanup_start = time.time()
        Knowledge_Helper.cleanup(str(session_id))
        operation_timings['cleanup'] = time.time() - cleanup_start
        
        # Calculate and log total time
        function_end_time = time.time()
        total_function_time = function_end_time - function_start_time
        hours, remainder = divmod(total_function_time, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        print("\n--- Knowledge Update Timing Summary ---")
        print(f"Total time: {int(hours)}h {int(minutes)}m {seconds:.2f}s ({total_function_time:.2f} seconds)")
        print("Breakdown by operation:")
        for op_name, op_time in operation_timings.items():
            op_percent = (op_time / total_function_time) * 100
            print(f"  - {op_name}: {op_time:.2f}s ({op_percent:.1f}%)")
        
        # Store timing info in data for possible logging
        data['operation_timings'] = operation_timings
        data['total_update_time'] = total_function_time
        data['formatted_time'] = f"{int(hours)}h {int(minutes)}m {seconds:.2f}s"
        
        print("Knowledge graph update completed successfully")
        return True
        
    except Exception as e:
        # Update session status to failed
        try:
            await session_handler.update_one(
                filter={"session_id": session_id},
                element={
                    "session_status": "Failed",
                    "error_message": str(e),
                    "updated_at": datetime.utcnow()
                },
                db=session_handler.db
            )
        except Exception as update_error:
            print(f"Failed to update session status: {update_error}")
        
        # Calculate time even for failed operations
        function_end_time = time.time()
        total_function_time = function_end_time - function_start_time
        hours, remainder = divmod(total_function_time, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        print(f"\n❌ Knowledge update failed after {total_function_time:.2f} seconds ({int(hours)}h {int(minutes)}m {seconds:.2f}s)")
        print(f"Error in knowledge update: {str(e)}")
        
        # Store timing info even for failed operations
        data['operation_timings'] = operation_timings
        data['total_update_time'] = total_function_time
        data['formatted_time'] = f"{int(hours)}h {int(minutes)}m {seconds:.2f}s"
        data['error'] = str(e)
        
        raise

def main():
    parser = argparse.ArgumentParser(description='Knowledge Graph Update Background Job')
    parser.add_argument('--input_args', required=True, help='JSON string containing input arguments')
    parser.add_argument('--stage', required=True, help='Processing stage')
    
    args = parser.parse_args()
    
    print(f"Starting knowledge update job")
    print(f"Stage: {args.stage}")
    print(f"Input args: {args.input_args}")
    
    try:
        # Parse input arguments
        input_data = json.loads(args.input_args)
        
        build_path = input_data['build_path']
        json_data_file = input_data['json_data_file']
        session_id = input_data['session_id']
        project_id = input_data['project_id']
        user_id = input_data['user_id']
        tenant_id = input_data['tenant_id']
        set_tenant_context(tenant_id)
        set_user_context(user_id)

        # Set user context
        try:
            deps = get_dependencies()
            user_context = deps['user_context']
            user_context.set(user_id)
        except Exception as e:
            print(f"Warning: Could not set user context: {e}")
        
        print(f"Parsed parameters:")
        print(f"  Build path: {build_path}")
        print(f"  JSON data file: {json_data_file}")
        print(f"  Session ID: {session_id}")
        print(f"  Project ID: {project_id}")
        print(f"  User ID: {user_id}")
        print(f"  Tenant ID: {tenant_id}")
        
        # Load JSON data
        with open(json_data_file, 'r') as f:
            data = json.load(f)
        
        print(f"Loaded data from {json_data_file}")
        print(f"Data keys: {list(data.keys())}")
        
        # Run the knowledge update process based on stage
        if args.stage == "knowledge_update":
            try:
                # Use retry wrapper to handle GitHub rate limit issues
                asyncio.run(run_knowledge_update_with_retry(data, max_retries=3))
                print("✅ Knowledge update completed successfully")
                sys.exit(0)
            except Exception as e:
                error_msg = str(e).lower()
                if any(keyword in error_msg for keyword in ['rate limit', 'forbidden', '403', 'abuse']):
                    print(f"❌ Knowledge update failed due to GitHub rate limiting: {e}")
                    print("💡 Consider waiting for rate limit reset or using a different GitHub token")
                else:
                    print(f"❌ Knowledge update failed: {e}")
                sys.exit(1)
        else:
            print(f"Unknown stage: {args.stage}")
            sys.exit(1)
            
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in input arguments: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print(f"Error: JSON data file not found: {json_data_file}")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)
    finally:
        # Clean up the JSON data file
        try:
            if 'json_data_file' in locals():
                if os.path.exists(json_data_file):
                    os.unlink(json_data_file)
                    print(f"Cleaned up JSON data file: {json_data_file}")
        except Exception as cleanup_error:
            print(f"Warning: Could not clean up JSON data file: {cleanup_error}")

if __name__ == "__main__":
    main()