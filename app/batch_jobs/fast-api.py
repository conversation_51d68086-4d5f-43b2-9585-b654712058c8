from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
import os
import sys
import time
import json
import hashlib
import threading
from pathlib import Path

app = FastAPI(
    title="Knowledge File Creator API",
    description="Create knowledge JSON files from source code",
    version="1.0.0"
)

class KnowledgeRequest(BaseModel):
    base_path: str = Field(..., description="Base directory path to process")
    num_threads: Optional[int] = Field(default=7, ge=1, le=20, description="Number of processing threads")
    output_dir: Optional[str] = Field(default=None, description="Custom output directory")

class KnowledgeResponse(BaseModel):
    success: bool
    message: str
    files_discovered: int
    files_created: int
    processing_time: float
    output_directory: str
    errors: List[str] = []

def create_file_info_template(file_id: int, filename: str, worker_name: str) -> Dict[str, Any]:
    """
    Create a standardized file_info template for source file processing.
    
    Args:
        file_id: Unique identifier for the file
        filename: The relative path to the source file
        worker_name: Name of the worker processing the file
        
    Returns:
        dict: A standardized file_info dictionary with initial values
    """
    file_extension = Path(filename).suffix[1:] if Path(filename).suffix else "unknown"
    
    return {
        "is_source_file": True,
        "file_id": file_id,
        "git_revision": f"api_creation_{worker_name}",
        "state": 1,  # _STATE_INITIAL
        "description": f"Source file processed by API worker {worker_name}",
        "external_files": [],
        "external_methods": [],
        "published": [],
        "classes": [],
        "methods": [],
        "calls": [],
        "search-terms": [
            Path(filename).name,
            file_extension,
            f"api_worker_{worker_name}",
            "fastapi_created"
        ]
    }

class SimpleKnowledgeFileCreator:
    def __init__(self, base_path: str, num_threads: int = 7, output_dir: Optional[str] = None):
        self.base_path = Path(base_path)
        self.num_threads = num_threads
        self.output_dir = Path(output_dir) if output_dir else self.base_path / '.knowledge'
        self.discovered_files = []
        self.created_files = []
        self._lock = threading.Lock()
        self._file_queue = []
        self.thread_workers = []
        self.errors = []
        
        # Same ingestible file types as your Knowledge class
        self.ingestible_filetypes = [
            '.js', '.jsx', '.ts', '.tsx', '.html', '.htm', '.xml', '.xhtml', 
            '.css', '.scss', '.less', '.json', '.yaml', '.yml', '.mjs', '.cjs',
            '.py', '.pyi', '.ipynb', '.c', '.h', '.cpp', '.cc', '.cxx', '.c++', 
            '.hpp', '.hxx', '.hh', '.rs', '.java', '.kt', '.kts', '.scala', '.sc',
            '.swift', '.m', '.mm', '.cs', '.go', '.php', '.php3', '.php4', '.php5', 
            '.phps', '.phtml', '.rb', '.erb', '.rhtml', '.pl', '.pm', '.t',
            '.sh', '.bash', '.zsh', '.fish', '.csh', '.tcsh', '.ksh',
            '.Makefile', '.makefile', '.mk', '.cmake', '.ninja', '.gradle', '.sbt',
            '.lisp', '.cl', '.el', '.scm', '.rkt', '.asm', '.s', '.a51', '.inc',
            '.hs', '.lhs', '.erl', '.hrl', '.ex', '.exs', '.fs', '.fsi', '.fsx',
            '.dart', '.lua', '.r', '.R', '.m', '.mat', '.fig', '.jl', '.pl',
            '.pro', '.cob', '.cbl', '.cpy', '.f', '.for', '.f90', '.f95', 
            '.f03', '.f08', '.pas', '.pp', '.inc', '.dpr', '.dfm', '.sql',
            '.j2', '.jinja', '.jinja2', '.toml', '.ini', '.md', '.rst', '.adoc', 
            '.asciidoc', '.gitignore', '.prompt', '.ebnf', '.bnf', '.peg'
        ]

    def _is_ingestible(self, item_path: Path) -> bool:
        """Same filtering logic as your Knowledge class"""
        item = item_path.name
        
        if item.startswith('.'):
            return False
        if str(item_path).endswith('.log'):
            return False
        if str(item_path).endswith('files.yaml'):
            return False
        if str(item_path).endswith('package-lock.json'):
            return False
            
        return item_path.suffix.lower() in self.ingestible_filetypes

    def _list_important_files(self, base_folder: Path) -> List[Path]:
        """Same file discovery logic as your Knowledge class"""
        filelist = []

        def list_files_in_folder(folder_path: Path):
            files = []
            try:
                items = list(folder_path.iterdir())
            except (FileNotFoundError, PermissionError, NotADirectoryError):
                return files
                
            for item_path in items:
                try:
                    if item_path.is_dir():
                        skip_folder = False
                        if item_path.name.startswith('.'):
                            skip_folder = True
                        for suffix in ['coverage', 'node_modules', 'venv', 'data', 'pyenv', 'env', 'pyvenv']:
                            if item_path.name == suffix:
                                skip_folder = True
                                break
                        if not skip_folder:
                            files.extend(list_files_in_folder(item_path))
                    else:
                        if self._is_ingestible(item_path):
                            relative_path = item_path.relative_to(self.base_path)
                            files.append(relative_path)
                except (FileNotFoundError, PermissionError, NotADirectoryError):
                    if self._is_ingestible(item_path):
                        relative_path = item_path.relative_to(self.base_path)
                        files.append(relative_path)
            return files

        filelist.extend(list_files_in_folder(base_folder))
        return filelist

    def _convert_path_to_knowledge_filename(self, file_path: Path) -> str:
        """Convert file path to .knowledge filename format"""
        filename = str(file_path).replace('/', '_')
        filename = filename.replace('\\', '_')
        return filename

    class _Worker:
        """Same Worker pattern as your Knowledge class"""
        def __init__(self, creator, name):
            self.creator = creator
            self.name = name
            self.thread_worker = None
            self.stop_worker = False
            self.worker_stopped = False

        def start(self):
            self.thread_worker = threading.Thread(target=self._worker_loop, daemon=True)
            self.thread_worker.start()

        def _service_file_queue(self):
            """Same queue servicing logic as your Knowledge class"""
            filename = None
            creator = self.creator
            
            with creator._lock:
                if creator._file_queue:
                    filename = creator._file_queue.pop(0)
                    
            if filename:
                print(f"Worker {self.name}: Processing {filename}")
                self._create_knowledge_file(filename)

        def _create_knowledge_file(self, file_path: Path):
            """Create .knowledge file without LLM processing"""
            try:
                # Convert to .knowledge filename format
                knowledge_filename = self.creator._convert_path_to_knowledge_filename(file_path)
                output_path = self.creator.output_dir / knowledge_filename
                
                # Get file info
                full_file_path = self.creator.base_path / file_path
                file_size = 0
                file_hash = ""
                try:
                    file_size = full_file_path.stat().st_size
                    if file_size < 1024 * 1024:  # Only hash files < 1MB
                        content = full_file_path.read_text(encoding='utf-8', errors='ignore')
                        file_hash = hashlib.md5(content.encode("utf-8")).hexdigest()
                except Exception as e:
                    self.creator.errors.append(f"Error reading {file_path}: {str(e)}")
                    return
                
                # Get file extension
                file_extension = file_path.suffix[1:] if file_path.suffix else "unknown"
                
                # Use the template function for file info
                file_info = create_file_info_template(
                    len(self.creator.created_files) + 1,
                    str(file_path).replace('\\', '/'),
                    self.name
                )
                
                # Create complete file data
                file_data = {
                    "filename": str(file_path).replace('\\', '/'),
                    "format-version": 4,
                    "format": file_extension,
                    "knowledge_revision": 1,
                    "revision_history": [{"1": f"api_creation_{self.name}"}],
                    "hash": file_hash,
                    "code-base-name": "default",
                    "ctags": [],
                    "size": file_size,
                    **file_info  # Merge the template data
                }
                
                # Write file
                json_content = json.dumps(file_data, indent=2)
                output_path.write_text(json_content, encoding='utf-8')
                
                # Thread-safe addition to created files list
                with self.creator._lock:
                    self.creator.created_files.append(str(output_path))
                
            except Exception as e:
                self.creator.errors.append(f"Worker {self.name}: Error creating knowledge file for {file_path}: {e}")

        def _worker_loop(self):
            """Same worker loop pattern as your Knowledge class"""
            print(f"Knowledge worker {self.name} start")
            
            while True:
                try:
                    self._service_file_queue()
                except Exception as e:
                    self.creator.errors.append(f"Exception in worker {self.name}: {str(e)}")

                with self.creator._lock:
                    if not self.creator._file_queue:
                        break

                if self.stop_worker:
                    self.worker_stopped = True
                    break
                    
                time.sleep(0.001)
                
            print(f"Knowledge worker {self.name} done")

    def create_knowledge_files(self) -> Dict[str, Any]:
        """Main method to create knowledge files"""
        print(f"Starting knowledge file creation with {self.num_threads} threads")
        print(f"Base path: {self.base_path}")
        print(f"Output directory: {self.output_dir}")
        
        start_time = time.time()
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Step 1: Discover files
        print("Step 1: Discovering files...")
        discovery_start = time.time()
        
        self.discovered_files = self._list_important_files(self.base_path)
        
        discovery_end = time.time()
        discovery_time = discovery_end - discovery_start
        
        print(f"Files discovered: {len(self.discovered_files)}")
        print(f"Discovery time: {discovery_time:.4f} seconds")
        
        if not self.discovered_files:
            return {
                'success': False,
                'error': 'No ingestible files found',
                'files_discovered': 0,
                'files_created': 0,
                'processing_time': time.time() - start_time,
                'output_directory': str(self.output_dir),
                'errors': []
            }
        
        # Step 2: Populate file queue
        with self._lock:
            self._file_queue.extend(self.discovered_files)
        
        # Step 3: Create workers (same as your Knowledge class)
        print(f"Step 2: Creating .knowledge files with {self.num_threads} threads...")
        creation_start = time.time()
        
        num_workers = min(self.num_threads, len(self._file_queue))
        
        worker_index = 0
        while worker_index < num_workers:
            worker_index += 1
            thread_worker = self._Worker(self, f"kw{worker_index}")
            thread_worker.start()
            self.thread_workers.append(thread_worker)
        
        # Wait for all workers to complete
        for worker in self.thread_workers:
            worker.thread_worker.join()
        
        creation_end = time.time()
        creation_time = creation_end - creation_start
        total_time = time.time() - start_time
        
        # Results
        print(f"Files created: {len(self.created_files)}")
        print(f"Creation time: {creation_time:.4f} seconds")
        print(f"Total time: {total_time:.4f} seconds")
        
        return {
            'success': True,
            'files_discovered': len(self.discovered_files),
            'files_created': len(self.created_files),
            'processing_time': total_time,
            'output_directory': str(self.output_dir),
            'errors': self.errors
        }

@app.post("/create-knowledge-files", response_model=KnowledgeResponse)
def create_knowledge_files(request: KnowledgeRequest):
    """
    Create knowledge files from source code directory (JSON metadata only, no LLM)
    """
    # Validate base path
    if not os.path.exists(request.base_path):
        raise HTTPException(status_code=400, detail=f"Base path does not exist: {request.base_path}")
    
    if not os.path.isdir(request.base_path):
        raise HTTPException(status_code=400, detail=f"Base path is not a directory: {request.base_path}")
    
    try:
        # Create processor
        processor = SimpleKnowledgeFileCreator(
            request.base_path, 
            request.num_threads, 
            request.output_dir
        )
        
        # Process files
        result = processor.create_knowledge_files()
        
        if result['success']:
            return KnowledgeResponse(
                success=True,
                message="Knowledge files created successfully",
                files_discovered=result['files_discovered'],
                files_created=result['files_created'],
                processing_time=result['processing_time'],
                output_directory=result['output_directory'],
                errors=result['errors']
            )
        else:
            raise HTTPException(status_code=500, detail=result.get('error', 'Unknown error'))
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating knowledge files: {str(e)}")

@app.get("/")
def root():
    """API health check"""
    return {
        "message": "Knowledge File Creator API",
        "status": "running",
        "version": "1.0.0"
    }

@app.get("/health")
def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": time.time()}

def run_directly(root_path: str, num_threads: int = 7, output_dir: Optional[str] = None):
    """
    Run the knowledge file creator directly without FastAPI
    """
    print(f"Running knowledge file creator directly on: {root_path}")
    
    if not os.path.exists(root_path):
        print(f"Error: Root path does not exist: {root_path}")
        return False
    
    if not os.path.isdir(root_path):
        print(f"Error: Root path is not a directory: {root_path}")
        return False
    
    try:
        processor = SimpleKnowledgeFileCreator(root_path, num_threads, output_dir)
        result = processor.create_knowledge_files()
        
        if result['success']:
            print(f"Success! Created {result['files_created']} knowledge files")
            print(f"Output directory: {result['output_directory']}")
            print(f"Processing time: {result['processing_time']:.2f} seconds")
            if result['errors']:
                print(f"Errors encountered: {len(result['errors'])}")
                for error in result['errors']:
                    print(f"  - {error}")
            return True
        else:
            print(f"Failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python script.py <root_path> [num_threads] [output_dir]")
        print("  root_path: Path to the source code directory")
        print("  num_threads: Number of processing threads (default: 7)")
        print("  output_dir: Custom output directory (optional)")
        print("\nOr run without arguments to start FastAPI server")
        
        # Start FastAPI server if no arguments provided
        import uvicorn
        uvicorn.run(app, host="0.0.0.0", port=8002)
    else:
        # Run directly with command line arguments
        root_path = sys.argv[1]
        num_threads = int(sys.argv[2]) if len(sys.argv) > 2 else 7
        output_dir = sys.argv[3] if len(sys.argv) > 3 else None
        
        success = run_directly(root_path, num_threads, output_dir)
        sys.exit(0 if success else 1)