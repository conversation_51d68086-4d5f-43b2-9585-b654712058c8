# app/connection/mongo_client.py
from typing import Any, Optional
import logging
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError, InvalidURI
import certifi
from app.core.Settings import settings
from app.connection.tenant_middleware import get_tenant_id, get_user_id, KAVIA_ROOT_DB_NAME

Database = None
db_client: Optional[MongoClient] = None
ca = certifi.where()
logger = logging.getLogger(__name__)

class TenantBasedDatabase:
    def __init__(self, db):
        self._db = db
        
    def __getitem__(self, collection_name):
        # Check if the tenant is a B2C client
        if get_tenant_id() == settings.KAVIA_B2C_CLIENT_ID and self._db.name != KAVIA_ROOT_DB_NAME and settings.KAVIA_ROOT_TENANT_ID not in self._db.name:
            user_id = get_user_id()
            # Only prefix the collection name if it doesn't already start with the user ID
            if not collection_name.startswith(f"{user_id}_"):
                modified_collection_name = f"{user_id}_{collection_name}"
                return self._db[modified_collection_name]
        # For regular tenants or collections that already have the prefix, use the collection name as is
        return self._db[collection_name]
    
    # Forward all other attributes/methods to the underlying database object
    def __getattr__(self, name):
        return getattr(self._db, name)

class TenantAwareMongoClient(MongoClient):
    def __getitem__(self, db_name):
        # Get the database from the original client
        db = super().__getitem__(db_name)
        # Wrap it with our custom TenantBasedDatabase class
        return TenantBasedDatabase(db)

def get_optimal_connection_options():
    """Get optimized connection options based on MongoDB URI type."""
    base_options = {
        "connectTimeoutMS": 3000,  # Fast connection
        "serverSelectionTimeoutMS": 2000,  # Quick server selection
        "socketTimeoutMS": 5000,  # Socket timeout
        "maxPoolSize": 50,
        "minPoolSize": 5,
        "maxIdleTimeMS": 30000,
        "waitQueueTimeoutMS": 2000,
        "retryWrites": True,
        "w": "majority",
        "readPreference": "primaryPreferred"
    }
    
    # For internal Kubernetes services, avoid TLS complications
    uri = settings.MONGO_CONNECTION_URI
    if ".svc.cluster.local" in uri:
        # Internal k8s service - no TLS needed
        return base_options
    elif "mongodb+srv://" in uri or "ssl=true" in uri or "tls=true" in uri:
        # Atlas or explicit TLS
        base_options.update({
            "tls": True,
            "tlsCAFile": ca,
            "tlsAllowInvalidHostnames": False,
            "tlsAllowInvalidCertificates": False
        })
    elif "localhost" not in uri and "127.0.0.1" not in uri:
        # Remote connection, try TLS with lenient settings
        base_options.update({
            "tls": True,
            "tlsAllowInvalidHostnames": True,
            "tlsAllowInvalidCertificates": True
        })
    
    return base_options

def connect_db():
    """Create database connection with tenant awareness."""
    global db_client
    
    try:
        options = get_optimal_connection_options()
        db_client = TenantAwareMongoClient(settings.MONGO_CONNECTION_URI, **options)
        
        # Quick connection test
        db_client.admin.command('ping', maxTimeMS=1000)
        logger.info("MongoDB connection successful")
        
        return db_client
        
    except Exception as e:
        logger.warning(f"Primary connection failed: {str(e)}")
        
        # Fallback with minimal options (no TLS)
        try:
            fallback_options = {
                "connectTimeoutMS": 2000,
                "serverSelectionTimeoutMS": 1500,
                "socketTimeoutMS": 3000
            }
            
            db_client = TenantAwareMongoClient(settings.MONGO_CONNECTION_URI, **fallback_options)
            db_client.admin.command('ping', maxTimeMS=1000)
            logger.info("MongoDB fallback connection successful")
            
            return db_client
            
        except Exception as fallback_error:
            logger.error(f"All MongoDB connections failed: {str(fallback_error)}")
            raise fallback_error

def get_db_client():
    """Return database client instance."""
    global db_client
    if db_client is None: 
        return connect_db()
    
    # Quick connection test with timeout
    try:
        db_client.admin.command('ping', maxTimeMS=1000)
        return db_client
    except Exception as e:
        logger.warning(f"MongoDB connection test failed, reconnecting: {str(e)}")
        db_client = None  # Reset client
        return connect_db()

def close_db():
    """Close database connection."""
    global db_client
    if db_client:
        db_client.close()
        db_client = None
        logger.info("MongoDB connection closed")

def get_db(db_name: str = settings.MONGO_DB_NAME):
    """Return database instance with tenant awareness."""
    client = get_db_client()
    # This will return a TenantBasedDatabase instance
    return client[db_name]

# Connection pool warming function (optional)
def warm_connection_pool():
    """Pre-warm the connection pool for better performance."""
    try:
        client = get_db_client()
        # Perform lightweight operations to establish connections
        client.admin.command('ping')
        logger.info("Connection pool warmed")
    except Exception as e:
        logger.warning(f"Failed to warm connection pool: {str(e)}")