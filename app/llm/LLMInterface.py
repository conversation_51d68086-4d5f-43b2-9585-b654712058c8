# --------------------------------------------------------------------------------
# Company Name: Kavia AI
# Author: <PERSON><PERSON><PERSON>
# Creation Date: Year (2024)
#
# Confidential Information of Kavia AI
# NOTICE: All information contained herein is, and remains the property of Kavia AI.
# The intellectual and technical concepts contained herein are proprietary to Kavia AI
# and may be covered by U.S. and Foreign Patents, patents in process, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material is strictly forbidden unless prior written permission is obtained
# from Kavia AI. Access to this file is granted on the condition that it will not be used for any purpose other than as specifically authorized
# in writing by Kavia AI, and subject to the terms of any agreement governing such access and use. Access to this file may also require
# a signed Non-Disclosure Agreement.
#
# --------------------------------------------------------------------------------

from openai import OpenAIError, OpenAI
import os
import json
import time
import logging
import asyncio
from app.utils.datetime_utils import generate_timestamp
from jinja2 import Environment, FileSystemLoader, TemplateNotFound

# setting up logger
def setup_logger(name, log_file, level=logging.INFO):
    """Function to set up a logger for logging to a file."""
    logger = logging.getLogger(name)

    # Check if the logger already has handlers to prevent adding duplicate handlers
    if not logger.handlers:
        handler = logging.FileHandler(log_file)
        formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)

    logger.setLevel(level)
    logger.propagate = False
    return logger

def format_messages_for_logging(messages):
    """Convert messages to a more readable format by processing escape sequences."""
    # Using json.dumps with indent for pretty printing, then convert it back to dict to replace escape sequences
    formatted_messages = json.dumps(messages, indent=2)
    return formatted_messages


def exponential_backoff(retry):
    # Basic exponential backoff formula: 2^retry * 100 milliseconds
    return 2 ** retry * 0.1

async def llm_interaction_wrapper(messages, user_prompt, system_prompt, model, response_format, stream=False, user_id='admin' , max_retries=5, functions=[]):
    if user_prompt:
        messages.append({
            "role": "user",
            "content": user_prompt
        })
    if system_prompt:
        messages.append({
            "role": "system",
            "content": system_prompt
        })

    llm_logger.info("Request parameters: Model - {}, Response Format - {}, Stream - {}".format(model, response_format, stream))
    llm_logger.info("Messages:")
    for message in messages:
        llm_logger.info(f"Role: {message['role']}, Content: {message['content']}")

    # Define a wrapper function that calls your target function with keyword arguments
    def blocking_call_wrapper():
        if functions:
            return client.chat.completions.create(
            messages=messages,
            model=model,
            response_format=response_format,
            temperature=0.2,
            stream=stream,
            functions=functions,
            function_call="auto"
        )
            
        return client.chat.completions.create(
            messages=messages,
            model=model,
            response_format=response_format,
            temperature=0.2,
            stream=stream,
            
        )
    loop = asyncio.get_running_loop()

    attempt = 0
    while attempt < max_retries:
        try:
            # Make the call to the LLM
            # Use the wrapper function with run_in_executor
            completion = await loop.run_in_executor(None, blocking_call_wrapper)

            # Log the response
            if not stream:
                llm_logger.info(f"LLM Response: {completion.choices[0].message.content}")
            else:
                llm_logger.info("LLM Response: Streaming response")
                return completion  # Return the stream object if streaming

            # Log other info from the llm response like token count
            llm_logger.info(f"Usage: Prompt Tokens: {completion.usage.prompt_tokens}, Completion Tokens: {completion.usage.completion_tokens}, Total Tokens: {completion.usage.total_tokens}")

            return completion
        except Exception as e:
            if "rate limit" in str(e).lower():  # Check if error is due to rate limiting
                wait = exponential_backoff(attempt)
                llm_logger.warning(f"Rate limit hit, waiting {wait} seconds before retrying...")
                time.sleep(wait)
                attempt += 1
            else:
                llm_logger.error(f"Error in LLM interaction: {e}")
                raise
        else:
            break  # Exit loop if call succeeds

    if attempt == max_retries:
        llm_logger.error("Max retries hit, failing with rate limit error.")
        raise Exception("Rate limit error: Max retries reached.")

llm_logger= None
client = None


def init_interface(llm_api_key):
    # Set up the logger
    log_dir = 'logs/'
    log_path = os.path.join(log_dir, 'llm.log')
    
    # Create logs directory if it doesn't exist
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    global llm_logger
    llm_logger = setup_logger('llm_logger', log_path)
    
    # Set up the OpenAI API client
    global client
    client = OpenAI(api_key=llm_api_key)
    
    return True

def generate_embedding(properties):
        # Convert properties to a string representation for embedding generation
        properties_text = properties_to_text(properties)
        # Generate embedding using OpenAI's API
        response = client.embeddings.create(
            input=properties_text,
            model="text-embedding-ada-002"  # Choose an appropriate model for your use case
        )
        embedding = response.data[0].embedding
        return embedding

def properties_to_text(properties):
        # Convert node properties to a text representation
        # This could be as simple as concatenating key-value pairs or more complex depending on your needs
        return ", ".join([f"{key}: {value}" for key, value in properties.items()])
    
