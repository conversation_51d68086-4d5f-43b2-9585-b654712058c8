import psutil
import threading
import time
import logging
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass, asdict
import json


@dataclass
class MetricEntry:
    """Data class for a single metric entry"""
    timestamp: str
    system: Dict
    process: Dict
    project_id: str
    tenant_id: str
    task_id: str


class SystemMetricsTracker:
    """Efficient system metrics tracker for monitoring CPU and memory usage"""
    
    def __init__(self, project_id: str, tenant_id: str, task_id: str, 
             log_file_path: Optional[Path] = None, interval: int = 30):
        """
        Initialize the metrics tracker
        
        Args:
            project_id: Project identifier (will be converted to string)
            tenant_id: Tenant identifier  
            task_id: Task identifier
            log_file_path: Optional custom log file path
            interval: Collection interval in seconds
        """
        # Convert project_id to string to handle both int and str inputs
        self.project_id = str(project_id)
        self.tenant_id = str(tenant_id)
        self.task_id = str(task_id)
        self.interval = max(interval, 5)  # Minimum 5 seconds
        self.is_running = False
        self.metrics_data: List[MetricEntry] = []
        self.stop_event = threading.Event()
        self.collector_thread: Optional[threading.Thread] = None
        self.batch_size = 5  # Smaller batch size for more frequent writes
        self.alert_callbacks: List[Callable] = []
        
        # Setup log file path with better error handling
        if log_file_path:
            self.log_file_path = log_file_path
        else:
            # Use absolute path to avoid directory issues
            import os
            base_dir = os.getcwd()  # Get current working directory
            log_dir = Path(base_dir) / "data" / self.tenant_id / self.project_id / "ingestion"
            log_dir.mkdir(parents=True, exist_ok=True)
            self.log_file_path = log_dir / "test.log"
            
            # Ensure the file exists
            self.log_file_path.touch(exist_ok=True)
        
        # Initialize logger with proper configuration
        self.logger = logging.getLogger(f"metrics_tracker_{self.project_id}")
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
        
        # Thresholds for alerts
        self.cpu_threshold = 80.0
        self.memory_threshold = 85.0
        self.alert_count = 0
        
        # Track collection stats
        self.collection_count = 0
        self.last_collection_time = None
        self.write_count = 0
    
    def set_alert_thresholds(self, cpu_threshold: float = 80.0, memory_threshold: float = 85.0):
        """Set alert thresholds for CPU and memory usage"""
        self.cpu_threshold = cpu_threshold
        self.memory_threshold = memory_threshold
        self.logger.info(f"Alert thresholds set - CPU: {cpu_threshold}%, Memory: {memory_threshold}%")
    
    def add_alert_callback(self, callback: Callable[[Dict], None]):
        """Add callback function to be called when alerts are triggered"""
        self.alert_callbacks.append(callback)
        self.logger.info(f"Alert callback added - Total callbacks: {len(self.alert_callbacks)}")
    

    def collect_metrics(self) -> Optional[MetricEntry]:
        """Collect current CPU and memory metrics with better process tracking"""
        try:
            collection_start = time.time()
            
            # Get system metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            try:
                disk = psutil.disk_usage('/')
            except Exception:
                disk = psutil.disk_usage(os.getcwd())
            
            # Get current process and its children
            try:
                current_process = psutil.Process()
                process_memory = current_process.memory_info()
                process_cpu = current_process.cpu_percent()
                
                # Track children processes (git clone, npm install, etc.)
                children_cpu = 0.0
                children_memory = 0
                children_count = 0
                
                try:
                    children = current_process.children(recursive=True)
                    for child in children:
                        try:
                            child_cpu = child.cpu_percent()
                            child_memory = child.memory_info().rss
                            children_cpu += child_cpu
                            children_memory += child_memory
                            children_count += 1
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            continue
                except Exception as e:
                    self.logger.debug(f"Could not get child processes: {e}")
                
                # Get process command line for identification
                try:
                    cmdline = ' '.join(current_process.cmdline()[:3])  # First 3 args
                except Exception:
                    cmdline = "unknown"
                    
            except Exception as e:
                self.logger.warning(f"Could not get process metrics: {e}")
                process_memory = type('obj', (object,), {'rss': 0, 'vms': 0})()
                process_cpu = 0.0
                children_cpu = 0.0
                children_memory = 0
                children_count = 0
                cmdline = "unknown"
            
            # Get load average
            try:
                load_avg = psutil.getloadavg() if hasattr(psutil, 'getloadavg') else (0, 0, 0)
            except Exception:
                load_avg = (0, 0, 0)
            
            timestamp = datetime.utcnow().isoformat()
            
            metric_entry = MetricEntry(
                timestamp=timestamp,
                system={
                    'cpu_percent': round(cpu_percent, 2),
                    'memory_percent': round(memory.percent, 2),
                    'memory_used_gb': round(memory.used / (1024**3), 3),
                    'memory_available_gb': round(memory.available / (1024**3), 3),
                    'memory_total_gb': round(memory.total / (1024**3), 3),
                    'disk_usage_percent': round(disk.percent, 2),
                    'disk_free_gb': round(disk.free / (1024**3), 3),
                    'disk_total_gb': round(disk.total / (1024**3), 3),
                    'load_avg_1min': round(load_avg[0], 2),
                    'load_avg_5min': round(load_avg[1], 2),
                    'load_avg_15min': round(load_avg[2], 2)
                },
                process={
                    'cpu_percent': round(process_cpu, 2),
                    'memory_mb': round(process_memory.rss / (1024**2), 2),
                    'memory_vms_mb': round(process_memory.vms / (1024**2), 2),
                    'children_cpu_percent': round(children_cpu, 2),
                    'children_memory_mb': round(children_memory / (1024**2), 2),
                    'children_count': children_count,
                    'total_process_cpu': round(process_cpu + children_cpu, 2),
                    'total_process_memory_mb': round((process_memory.rss + children_memory) / (1024**2), 2),
                    'pid': os.getpid(),
                    'cmdline': cmdline[:50]  # Truncate long command lines
                },
                project_id=self.project_id,
                tenant_id=self.tenant_id,
                task_id=self.task_id
            )
            
            self.collection_count += 1
            self.last_collection_time = datetime.utcnow()
            
            # Check for alerts
            self._check_alerts(metric_entry)
            
            return metric_entry
            
        except Exception as e:
            self.logger.error(f"Error collecting metrics: {str(e)}", exc_info=True)
            return None

    def write_metrics_batch(self, metrics: List[MetricEntry]) -> bool:
        """Write a batch of metrics to log file with enhanced process info"""
        if not metrics:
            return True
            
        try:
            with open(self.log_file_path, 'a', encoding='utf-8') as f:
                for metric in metrics:
                    if metric:
                        # Enhanced log entry with process and children info
                        log_entry = (
                            f"[{metric.timestamp}] METRICS - "
                            f"System CPU: {metric.system['cpu_percent']}% | "
                            f"System Memory: {metric.system['memory_percent']}% "
                            f"({metric.system['memory_used_gb']}GB/{metric.system['memory_total_gb']}GB) | "
                            f"Disk: {metric.system['disk_usage_percent']}% | "
                            f"Process CPU: {metric.process['cpu_percent']}% | "
                            f"Process Memory: {metric.process['memory_mb']}MB | "
                            f"Children CPU: {metric.process['children_cpu_percent']}% | "
                            f"Children Memory: {metric.process['children_memory_mb']}MB | "
                            f"Children Count: {metric.process['children_count']} | "
                            f"Total Process CPU: {metric.process['total_process_cpu']}% | "
                            f"Total Process Memory: {metric.process['total_process_memory_mb']}MB | "
                            f"Load: [{metric.system['load_avg_1min']},{metric.system['load_avg_5min']},{metric.system['load_avg_15min']}] | "
                            f"Project: {self.project_id} | Tenant: {self.tenant_id}\n"
                        )
                        f.write(log_entry)
                        
                # Ensure data is written to disk
                f.flush()
                os.fsync(f.fileno())
                
            self.write_count += 1
            self.logger.info(f"Wrote batch of {len(metrics)} metrics to log (batch #{self.write_count})")
            return True
            
        except Exception as e:
            self.logger.error(f"Error writing metrics to log: {str(e)}", exc_info=True)
            return False
    
    def _check_alerts(self, metric: MetricEntry):
        """Check if metrics exceed thresholds and trigger alerts"""
        try:
            cpu_percent = metric.system['cpu_percent']
            memory_percent = metric.system['memory_percent']
            
            alert_triggered = False
            alert_data = {
                'timestamp': metric.timestamp,
                'project_id': self.project_id,
                'tenant_id': self.tenant_id,
                'task_id': self.task_id,
                'alerts': []
            }
            
            if cpu_percent > self.cpu_threshold:
                alert_triggered = True
                alert_data['alerts'].append({
                    'type': 'cpu_high',
                    'current': cpu_percent,
                    'threshold': self.cpu_threshold,
                    'severity': 'critical' if cpu_percent > self.cpu_threshold + 10 else 'warning'
                })
            
            if memory_percent > self.memory_threshold:
                alert_triggered = True
                alert_data['alerts'].append({
                    'type': 'memory_high',
                    'current': memory_percent,
                    'threshold': self.memory_threshold,
                    'severity': 'critical' if memory_percent > self.memory_threshold + 10 else 'warning'
                })
            
            if alert_triggered:
                self.alert_count += 1
                self.logger.warning(f"System alert triggered: {alert_data}")
                
                # Write alert to log file immediately
                try:
                    with open(self.log_file_path, 'a', encoding='utf-8') as f:
                        alert_msg = f"[{metric.timestamp}] ALERT - " + ", ".join([
                            f"{alert['type'].upper()}: {alert['current']}% (threshold: {alert['threshold']}%)"
                            for alert in alert_data['alerts']
                        ])
                        f.write(alert_msg + "\n")
                except Exception as e:
                    self.logger.error(f"Error writing alert to file: {e}")
                
                # Call registered callbacks
                for callback in self.alert_callbacks:
                    try:
                        callback(alert_data)
                    except Exception as e:
                        self.logger.error(f"Error in alert callback: {str(e)}")
                        
        except Exception as e:
            self.logger.error(f"Error checking alerts: {str(e)}")
    
    
    def _collector_thread_function(self):
        """Background thread function for continuous metrics collection"""
        batch_metrics = []
        last_write_time = time.time()
        write_interval = 30  # Write to file every 30 seconds minimum
        
        self.logger.info(f"Metrics collection thread started - PID: {os.getpid()}, interval: {self.interval}s")
        
        # Write initial status
        try:
            with open(self.log_file_path, 'a', encoding='utf-8') as f:
                f.write(f"[{datetime.utcnow().isoformat()}] METRICS_COLLECTION_THREAD_STARTED - PID: {os.getpid()}, Interval: {self.interval}s\n")
                f.flush()
        except Exception as e:
            self.logger.error(f"Error writing thread start status: {e}")
        
        while not self.stop_event.is_set():
            try:
                collection_start = time.time()
                
                # Collect metrics
                metric = self.collect_metrics()
                if metric:
                    batch_metrics.append(metric)
                    self.metrics_data.append(metric)
                    
                    self.logger.debug(f"Collected metric #{self.collection_count} - "
                                    f"CPU: {metric.system['cpu_percent']}%, "
                                    f"Memory: {metric.system['memory_percent']}%")
                else:
                    self.logger.warning("Failed to collect metrics")
                
                # Determine if we should write
                current_time = time.time()
                should_write = (
                    len(batch_metrics) >= self.batch_size or 
                    (current_time - last_write_time) >= write_interval or
                    len(batch_metrics) > 0 and (current_time - last_write_time) >= 10  # Force write every 10s if we have data
                )
                
                if should_write and batch_metrics:
                    if self.write_metrics_batch(batch_metrics):
                        batch_metrics.clear()
                        last_write_time = current_time
                    else:
                        self.logger.error("Failed to write metrics batch")
                
                # Calculate sleep time accounting for collection time
                collection_time = time.time() - collection_start
                sleep_time = max(0, self.interval - collection_time)
                
                if sleep_time > 0:
                    self.stop_event.wait(sleep_time)
                else:
                    self.logger.warning(f"Metrics collection took longer than interval: {collection_time:.2f}s > {self.interval}s")
                
            except Exception as e:
                self.logger.error(f"Error in metrics collector thread: {str(e)}", exc_info=True)
                # Sleep on error to prevent rapid error loops
                self.stop_event.wait(min(self.interval, 30))
        
        # Write remaining metrics before stopping
        if batch_metrics:
            self.logger.info(f"Writing final batch of {len(batch_metrics)} metrics")
            self.write_metrics_batch(batch_metrics)
        
        # Write thread end status
        try:
            with open(self.log_file_path, 'a', encoding='utf-8') as f:
                f.write(f"[{datetime.utcnow().isoformat()}] METRICS_COLLECTION_THREAD_STOPPED - Total collections: {self.collection_count}\n")
                f.flush()
        except Exception as e:
            self.logger.error(f"Error writing thread stop status: {e}")
            
        self.logger.info(f"Metrics collection thread stopped - Total collections: {self.collection_count}")
    
    def start(self):
        """Start metrics collection in background thread"""
        if self.is_running:
            self.logger.warning("Metrics tracker is already running")
            return False
        
        try:
            self.is_running = True
            self.stop_event.clear()
            self.collection_count = 0
            self.write_count = 0
            
            # Log start with more details
            start_time = datetime.utcnow()
            start_msg = (f"METRICS_TRACKING_START - Project: {self.project_id}, "
                        f"Task: {self.task_id}, Interval: {self.interval}s, "
                        f"PID: {os.getpid()}, Log: {self.log_file_path}")
            
            with open(self.log_file_path, 'a', encoding='utf-8') as f:
                f.write(f"\n[{start_time.isoformat()}] {start_msg}\n")
                f.flush()
                os.fsync(f.fileno())
            
            # Start collector thread
            self.collector_thread = threading.Thread(
                target=self._collector_thread_function, 
                daemon=False,  # Don't make it daemon so it completes properly
                name=f"MetricsCollector-{self.project_id}-{self.task_id[:8]}"
            )
            self.collector_thread.start()
            
            # Give thread a moment to start and verify it's running
            time.sleep(0.5)
            if not self.collector_thread.is_alive():
                self.logger.error("Metrics collector thread failed to start")
                self.is_running = False
                return False
            
            self.logger.info(f"Metrics tracking started successfully for project {self.project_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting metrics tracker: {str(e)}", exc_info=True)
            self.is_running = False
            return False
    
    def stop(self, timeout: int = 15) -> Dict:
        """Stop metrics collection and return summary"""
        if not self.is_running:
            self.logger.warning("Metrics tracker is not running")
            return {'status': 'not_running'}
        
        self.logger.info("Stopping metrics collection...")
        
        try:
            # Signal stop
            self.stop_event.set()
            self.log_file_path.parent.mkdir(parents=True, exist_ok=True)

            # Wait for thread to finish
            if self.collector_thread and self.collector_thread.is_alive():
                self.logger.info(f"Waiting for collector thread to finish (timeout: {timeout}s)")
                self.collector_thread.join(timeout=timeout)
                
                if self.collector_thread.is_alive():
                    self.logger.warning("Collector thread did not finish within timeout")
            
            self.is_running = False
            
            # Generate summary
            end_time = datetime.utcnow()
            total_metrics = len(self.metrics_data)
            
            summary = {
                'project_id': self.project_id,
                'tenant_id': self.tenant_id,
                'task_id': self.task_id,
                'end_time': end_time.isoformat(),
                'total_metrics_collected': total_metrics,
                'collection_count': self.collection_count,
                'write_count': self.write_count,
                'alert_count': self.alert_count,
                'log_file_path': str(self.log_file_path),
                'collection_interval': self.interval,
                'status': 'completed'
            }
            
            # Log completion with summary
            completion_msg = (f"METRICS_TRACKING_END - Collected {total_metrics} entries, "
                            f"Collections: {self.collection_count}, Writes: {self.write_count}, "
                            f"Alerts: {self.alert_count}")
            
            with open(self.log_file_path, 'a', encoding='utf-8') as f:
                f.write(f"[{end_time.isoformat()}] {completion_msg}\n")
                f.flush()
                os.fsync(f.fileno())
            
            self.logger.info(f"Metrics tracking completed: {completion_msg}")
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error stopping metrics tracker: {str(e)}", exc_info=True)
            return {'status': 'error', 'error': str(e)}
    
    def get_current_stats(self) -> Dict:
        """Get current statistics without stopping collection"""
        return {
            'is_running': self.is_running,
            'total_metrics': len(self.metrics_data),
            'collection_count': self.collection_count,
            'write_count': self.write_count,
            'alert_count': self.alert_count,
            'last_collection_time': self.last_collection_time.isoformat() if self.last_collection_time else None,
            'thread_alive': self.collector_thread.is_alive() if self.collector_thread else False
        }
    
    def get_metrics_summary(self) -> Dict:
        """Get summary statistics of collected metrics"""
        if not self.metrics_data:
            return {'status': 'no_data'}
        
        try:
            cpu_values = [m.system['cpu_percent'] for m in self.metrics_data if m.system['cpu_percent'] > 0]
            memory_values = [m.system['memory_percent'] for m in self.metrics_data if m.system['memory_percent'] > 0]
            
            if not cpu_values or not memory_values:
                return {'status': 'insufficient_data', 'total_samples': len(self.metrics_data)}
            
            return {
                'total_samples': len(self.metrics_data),
                'cpu_stats': {
                    'avg': round(sum(cpu_values) / len(cpu_values), 2),
                    'max': max(cpu_values),
                    'min': min(cpu_values)
                },
                'memory_stats': {
                    'avg': round(sum(memory_values) / len(memory_values), 2),
                    'max': max(memory_values),
                    'min': min(memory_values)
                },
                'alert_count': self.alert_count,
                'collection_count': self.collection_count,
                'write_count': self.write_count,
                'status': 'completed'
            }
        except Exception as e:
            return {'status': 'error', 'error': str(e)}


class MetricsManager:
    """Manager class for handling multiple metrics trackers"""
    
    def __init__(self):
        self.trackers: Dict[str, SystemMetricsTracker] = {}
        self.logger = logging.getLogger('MetricsManager')
    
    def create_tracker(self, project_id, tenant_id: str, task_id: str, **kwargs) -> SystemMetricsTracker:
        """Create and register a new metrics tracker"""
        # Convert project_id to string to handle both int and str inputs
        project_id_str = str(project_id)
        tenant_id_str = str(tenant_id)
        task_id_str = str(task_id)
        
        tracker_key = f"{tenant_id_str}-{project_id_str}-{task_id_str}"
        
        # Stop existing tracker if present
        if tracker_key in self.trackers:
            self.logger.info(f"Stopping existing tracker for {tracker_key}")
            self.trackers[tracker_key].stop()
        
        tracker = SystemMetricsTracker(project_id_str, tenant_id_str, task_id_str, **kwargs)
        self.trackers[tracker_key] = tracker
        self.logger.info(f"Created new tracker for {tracker_key}")
        return tracker
    
    def get_tracker(self, project_id: str, tenant_id: str, task_id: str) -> Optional[SystemMetricsTracker]:
        """Get existing tracker"""
        tracker_key = f"{tenant_id}-{project_id}-{task_id}"
        return self.trackers.get(tracker_key)
    
    def stop_tracker(self, project_id: str, tenant_id: str, task_id: str) -> Optional[Dict]:
        """Stop and remove tracker"""
        tracker_key = f"{tenant_id}-{project_id}-{task_id}"
        if tracker_key in self.trackers:
            summary = self.trackers[tracker_key].stop()
            del self.trackers[tracker_key]
            self.logger.info(f"Stopped and removed tracker for {tracker_key}")
            return summary
        return None
    
    def stop_all(self):
        """Stop all active trackers"""
        for key, tracker in list(self.trackers.items()):
            tracker.stop()
            self.logger.info(f"Stopped tracker {key}")
        self.trackers.clear()


