from typing import List, Dict, Optional
from datetime import datetime
from app.repository.mongodb.base_repository import BaseMongoRepository
from app.repository.mongodb.client import Database

class CodeGenTasksRepository(BaseMongoRepository):
    def __init__(self):
        super().__init__('code_gen_tasks')
        self.collection: str = "code_gen_tasks"

    async def find_code_gen_tasks_credits(self, project_id: int, user_id: str, db: Database) -> float:
        """Find code generation tasks and calculate total credits."""
        try:
            query = {
                "project_id": project_id,
                "user_id": user_id
            }
            
            collection = await self.get_collection(db)
            tasks = await collection.find(query).to_list(length=None)
            
            if not tasks:
                return 0
            
            total_credits = 0
            for task in tasks:
                if 'total_cost' in task:
                    try:
                        total_credits += float(task['total_cost']) * 10
                    except (ValueError, TypeError):
                        print(f"Invalid total_cost format in task: {task['total_cost']}")
            
            return total_credits
            
        except Exception as e:
            print(f"Error in find_code_gen_tasks: {str(e)}")
            return 0