# app/repository/mongodb/tenant/tenant_credentials_repository.py
from app.repository.mongodb.base_repository import BaseMongoRepository
from app.repository.mongodb.client import Database
from typing import Dict, Optional

class TenantCredentialsRepository(BaseMongoRepository):
    def __init__(self):
        super().__init__('tenant_credentials')
        self.collection: str = "tenant_credentials"

    async def get_tenant_credentials(self, tenant_id: str, db: Database) -> Optional[Dict]:
        """Get tenant credentials from MongoDB."""
        try:
            collection = await self.get_collection(db)
            return await collection.find_one(
                {"tenant_id": tenant_id},
                projection={"_id": 0}
            )
        except Exception as e:
            print(f"Error getting tenant credentials: {e}")
            return None

    async def store_tenant_credentials(
        self, 
        tenant_id: str, 
        user_pool_id: str, 
        client_id: str, 
        db: Database
    ) -> bool:
        """Store tenant credentials in MongoDB."""
        try:
            collection = await self.get_collection(db)
            await collection.update_one(
                {"tenant_id": tenant_id},
                {"$set": {
                    "tenant_id": tenant_id,
                    "user_pool_id": user_pool_id,
                    "client_id": client_id
                }},
                upsert=True
            )
            return True
        except Exception as e:
            print(f"Error storing tenant credentials: {e}")
            return False

class TenantPermissionRepository(BaseMongoRepository):
    def __init__(self):
        super().__init__('tenant_permissions')
        self.collection: str = "tenant_permissions"

    async def get_permission(self, permission_id: str, db: Database) -> Optional[Dict]:
        """Get permission by ID."""
        try:
            collection = await self.get_collection(db)
            return await collection.find_one(
                {"permission_id": permission_id},
                projection={"_id": 0}
            )
        except Exception as e:
            print(f"Error getting permission: {e}")
            return None

    async def get_permissions(self, permission_ids: list, db: Database) -> Dict:
        """Get multiple permissions by IDs."""
        try:
            collection = await self.get_collection(db)
            cursor = collection.find(
                {"permission_id": {"$in": permission_ids}},
                projection={"_id": 0}
            )
            permissions = await cursor.to_list(length=None)
            return {p["permission_id"]: p["permissions"] for p in permissions}
        except Exception as e:
            print(f"Error getting permissions: {e}")
            return {}

    async def create_permission(self, permission_id: str, permissions: Dict, db: Database) -> bool:
        """Create a new permission."""
        try:
            collection = await self.get_collection(db)
            await collection.update_one(
                {"permission_id": permission_id},
                {"$set": {
                    "permission_id": permission_id,
                    "permissions": permissions
                }},
                upsert=True
            )
            return True
        except Exception as e:
            print(f"Error creating permission: {e}")
            return False

    async def update_permissions(self, permission_id: str, new_permissions: Dict, db: Database) -> bool:
        """Update existing permissions."""
        try:
            collection = await self.get_collection(db)
            result = await collection.update_one(
                {"permission_id": permission_id},
                {"$set": {"permissions": new_permissions}}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error updating permissions: {e}")
            return False

    async def delete_permission(self, permission_id: str, db: Database) -> bool:
        """Delete a permission."""
        try:
            collection = await self.get_collection(db)
            result = await collection.delete_one({"permission_id": permission_id})
            return result.deleted_count > 0
        except Exception as e:
            print(f"Error deleting permission: {e}")
            return False