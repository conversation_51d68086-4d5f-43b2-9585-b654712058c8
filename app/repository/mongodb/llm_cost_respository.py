from typing import List, Dict, Optional
from datetime import datetime
from app.repository.mongodb.base_repository import BaseMongoRepository
from app.repository.mongodb.client import Database


class LLMCostsRepository(BaseMongoRepository):
    def __init__(self):
        super().__init__('llm_costs')
        self.collection: str = "llm_costs"

    async def get_llm_costs(self, user_id: Optional[str], db: Database):
        """Retrieve LLM costs for a given user_id."""
        try:
            collection = await self.get_collection(db)
            if user_id:
                document = await collection.find_one({"user_id": user_id})
                if document:
                    document["_id"] = str(document["_id"])
                    return document
                return None
            else:
                cursor = collection.find({})
                result = await cursor.to_list(length=None)
                if result:
                    for doc in result:
                        doc["_id"] = str(doc["_id"])
                    return result
                return None
        except Exception as e:
            print(f"Error in get_llm_costs: {str(e)}")
            return None
