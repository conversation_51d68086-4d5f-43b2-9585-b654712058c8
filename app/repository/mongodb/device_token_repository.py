# app/repository/mongodb/device_token_repository.py

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from app.repository.mongodb.base_repository import BaseMongoRepository
from app.repository.mongodb.client import Database
from app.utils.datetime_utils import generate_timestamp

class DeviceToken(BaseModel):
    """Schema for device token document"""
    user_id: str
    token: str
    platform: str  # 'android', 'ios', 'web'
    tenant_id: str
    created_at: str = Field(default_factory=generate_timestamp)
    last_used: str = Field(default_factory=generate_timestamp)
    is_active: bool = True
    app_version: Optional[str] = None
    device_info: Optional[Dict[str, Any]] = None

    class Config:
        arbitrary_types_allowed = True

class DeviceTokenRepository(BaseMongoRepository):
    def __init__(self):
        super().__init__('device_tokens')
        self.collection: str = "device_tokens"

    async def add_device_token(
        self, 
        user_id: str, 
        token: str, 
        platform: str,
        tenant_id: str,
        db: Database,
        app_version: Optional[str] = None,
        device_info: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """Add or update a device token"""
        try:
            device_token = DeviceToken(
                user_id=user_id,
                token=token,
                platform=platform,
                tenant_id=tenant_id,
                app_version=app_version,
                device_info=device_info
            )
            token_dict = device_token.model_dump(exclude_none=True, exclude={'last_used'})
            collection = await self.get_collection(db)
            result = await collection.update_one(
                {
                    "user_id": user_id,
                    "token": token,
                    "tenant_id": tenant_id
                },
                {
                    "$set": token_dict,
                    "$currentDate": {"last_used": True}
                },
                upsert=True
            )
            return True
        except Exception as e:
            print(f"Error adding device token: {e}")
            return False

    async def get_user_tokens(
        self, 
        user_id: str, 
        tenant_id: str,
        db: Database,
        platform: Optional[str] = None,
    ) -> List[str]:
        """Get active device tokens for a user, sorted by last used (most recent first), limited to 10"""
        try:
            query = {
                "user_id": user_id,
                "tenant_id": tenant_id,
                "is_active": True
            }
            
            if platform:
                query["platform"] = platform
                
            collection = await self.get_collection(db)
            cursor = collection.find(
                query,
                {"token": 1}
            ).sort("last_used", -1).limit(10)  # Sort by last_used desc, limit to 10
            tokens = await cursor.to_list(length=None)
            return [doc["token"] for doc in tokens]
        except Exception as e:
            print(f"Error getting user tokens: {e}")
            return []

    async def deactivate_token(
        self, 
        token: str, 
        tenant_id: str,
        db: Database
    ) -> bool:
        """Deactivate a device token"""
        try:
            collection = await self.get_collection(db)
            result = await collection.delete_one(
                {
                    "token": token,
                    "tenant_id": tenant_id
                }
            )
            return result.deleted_count > 0
        except Exception as e:
            print(f"Error deactivating token: {e}")
            return False

    async def cleanup_inactive_tokens(
        self, 
        days_threshold: int,
        tenant_id: str,
        db: Database
    ) -> int:
        """Remove old inactive tokens"""
        try:
            threshold_date = generate_timestamp() - timedelta(days=days_threshold)
            collection = await self.get_collection(db)
            result = await collection.delete_many({
                "tenant_id": tenant_id,
                "last_used": {
                    "$lt": threshold_date
                }
            })
            return result.deleted_count
        except Exception as e:
            print(f"Error cleaning up tokens: {e}")
            return 0