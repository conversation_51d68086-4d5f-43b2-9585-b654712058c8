from typing import Dict, List, Optional, Any
from datetime import datetime
from bson import ObjectId
import logging
from motor.motor_asyncio import AsyncIOMotorDatabase as Database
from app.connection.tenant_middleware import get_tenant_id, get_user_id, KAVIA_ROOT_DB_NAME
from app.core.Settings import settings

logger = logging.getLogger(__name__)

class BaseMongoRepository:
    def __init__(self, collection_name: str):
        self.collection_name = collection_name
        
    def _get_b2c_collection_name(self, collection_name: str, db_name: str) -> str:
        """
        Get the proper collection name for B2C clients.
        Similar to the logic in TenantBasedDatabase.__getitem__ in mongo_client.py
        """
        try:
            tenant_id = get_tenant_id()
            
            # Check if the tenant is a B2C client
            if tenant_id == settings.KAVIA_B2C_CLIENT_ID and db_name != KAVIA_ROOT_DB_NAME:
                user_id = get_user_id()
                if user_id:
                    # Only prefix the collection name if it doesn't already start with the user ID
                    if not collection_name.startswith(f"{user_id}_"):
                        modified_collection_name = f"{user_id}_{collection_name}"
                        logger.debug(f"B2C collection name modified: {collection_name} -> {modified_collection_name}")
                        return modified_collection_name
                else:
                    logger.warning(f"B2C tenant detected but no user_id found for collection: {collection_name}")
            
            # For regular tenants or collections that already have the prefix, use the collection name as is
            return collection_name
            
        except Exception as e:
            logger.error(f"Error in B2C collection name resolution: {e}")
            # Fallback to original collection name
            return collection_name
    
    def get_effective_collection_name(self, db_name: str) -> str:
        """
        Get the effective collection name that will be used (useful for debugging/logging)
        """
        return self._get_b2c_collection_name(self.collection_name, db_name)
        
    async def get_collection(self, db: Database):
        # Apply B2C collection naming middleware
        collection_name = self._get_b2c_collection_name(self.collection_name, db.name)
        return db[collection_name]

    async def set_collection(self, collection_name: str):
        self.collection_name = collection_name

    async def insert_one(self, document: Dict, db: Database) -> str:
        collection = await self.get_collection(db)
        result = await collection.insert_one(document)
        return str(result.inserted_id)
    
    async def insert_many(self, documents: List[Dict], db: Database) -> List[str]:
        collection = await self.get_collection(db)
        result = await collection.insert_many(documents)
        return [str(id) for id in result.inserted_ids]

    async def find_one(self, filter_dict: Dict, db: Database, projection: Dict = None) -> Optional[Dict]:
        collection = await self.get_collection(db)
        return await collection.find_one(filter_dict, projection)

    async def find_many(self, filter_dict: Dict, db: Database, projection: Dict = None, 
                       sort: List = None, skip: int = 0, limit: int = 0) -> List[Dict]:
        collection = await self.get_collection(db)
        cursor = collection.find(filter_dict, projection, sort=sort)
        
        if skip:
            cursor = cursor.skip(skip)
        if limit:
            cursor = cursor.limit(limit)
            
        return await cursor.to_list(length=limit)

    async def update_one(self, filter_dict: Dict, update_dict: Dict, db: Database, 
                        upsert: bool = False) -> bool:
        collection = await self.get_collection(db)
        result = await collection.update_one(
            filter_dict,
            {"$set": update_dict},
            upsert=upsert
        )
        return result.modified_count > 0 or result.upserted_id is not None

    async def update_many(self, filter_dict: Dict, update_dict: Dict, db: Database) -> int:
        collection = await self.get_collection(db)
        result = await collection.update_many(filter_dict, {"$set": update_dict})
        return result.modified_count

    async def delete_one(self, filter_dict: Dict, db: Database) -> bool:
        collection = await self.get_collection(db)
        result = await collection.delete_one(filter_dict)
        return result.deleted_count > 0

    async def delete_many(self, filter_dict: Dict, db: Database) -> int:
        collection = await self.get_collection(db)
        result = await collection.delete_many(filter_dict)
        return result.deleted_count

    async def count_documents(self, filter_dict: Dict, db: Database) -> int:
        collection = await self.get_collection(db)
        return await collection.count_documents(filter_dict)

    async def aggregate(self, pipeline: List[Dict], db: Database) -> List[Dict]:
        collection = await self.get_collection(db)
        return await collection.aggregate(pipeline).to_list(None)

    async def create_index(self, keys: List, db: Database, **kwargs) -> str:
        collection = await self.get_collection(db)
        return await collection.create_index(keys, **kwargs)

    async def push_to_array(self, filter_dict: Dict, array_field: str, new_element: Any, 
                           db: Database) -> bool:
        try:
            collection = await self.get_collection(db)
            result = await collection.update_one(
                filter_dict,
                {"$push": {array_field: new_element}}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error pushing to array: {e}")
            return False

    def _serialize_object_id(self, doc: Optional[Dict]) -> Optional[Dict]:
        if doc and '_id' in doc:
            doc['_id'] = str(doc['_id'])
        return doc