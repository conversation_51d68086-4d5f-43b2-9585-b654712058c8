from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>
from fastapi import Depends
import jwt
import uvicorn
from app.routes import code_query
from app.connection.establish_db_connection import connect_node_db, connect_vector_db, get_mongo_db
from app.connection.tenant_middleware import TenantMiddleware, tenant_context
from app.repository.mongodb.client import connect_db
from starlette.middleware.sessions import SessionMiddleware
from app.core.Settings import settings
from app.utils.auth_utils import verify_token_and_get_user_details
from app.utils.aws.cognito_main import tenant_service
from app.utils.auth_userpool_utils import cognito_manager as cognito_auth_manager
from app.utils.cost_utils import check_free_credit_limits_crossed
import logging

security = HTTPBearer()
dependencies = [Depends(security)]

class CORSJSONResponse(JSONResponse): #to bypass the CORS issues while blocking POST requests after credits limit reached
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.headers.update({
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Allow-Methods": "*",
            "Access-Control-Allow-Headers": "*"
        })

def create_code_query_app():
    _app = FastAPI(title="Code Query API")
    
    origins = ['*']
    _app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    _app.add_middleware(SessionMiddleware, secret_key="your-secret-key")  # Add your secure key here

    _app.add_middleware(TenantMiddleware)
    
    @_app.get("/", status_code=200)
    def health(__: Request):
        return {"message": "Hey!, Welcome To Kavia AI Code Query Session..."}
    
    @_app.middleware("http")
    async def auth_middleware(request: Request, call_next):
        
        if request.method == "OPTIONS":
            return await call_next(request)
        
        if request.url.path.startswith("/api/ws"):
            return await call_next(request)
        
        if request.url.path.startswith("/ws"):
            return await call_next(request)
        
        if request.url.path.startswith("/api/auth"):
            return await call_next(request)
        
        if request.url.path.startswith("/api/oauth/github/callback"):
            return await call_next(request)

        if request.url.path.startswith("/api"):
            request.state.user = {"cognito:username": "admin", "sub": "admin", "email": "<EMAIL>"}  # default user
            authorization_header = request.headers.get("Authorization")

            # First try to get tenant_id from JWT token if authorization header exists
            tenant_id = None
            if authorization_header:
                try:
                    # Remove 'Bearer ' prefix if present
                    token = authorization_header.replace('Bearer ', '') if authorization_header.startswith('Bearer ') else authorization_header
                    # Decode JWT without verification to extract custom:tenant_id
                    decoded = jwt.decode(token, options={"verify_signature": False})
                    tenant_id = decoded.get('custom:tenant_id')
                except:
                    pass
            
            # Fallback to other methods if tenant_id not found in JWT
            tenant_id = tenant_id or request.headers.get("X-Tenant-Id") or request.cookies.get('X-Tenant-Id') or settings.KAVIA_ROOT_TENANT_ID
            
            print("main.py- tenant_id: ", tenant_id)
            if authorization_header:
                try:
                    if tenant_id == settings.KAVIA_ROOT_TENANT_ID:
                        claims = verify_token_and_get_user_details(authorization_header)
                    else:
                        tenant_cred = await tenant_service.get_tenant_cred(tenant_id)
                        claims = cognito_auth_manager.verify_token_and_get_user_details(authorization_header, tenant_cred['user_pool_id'])
                except Exception as e:
                    return JSONResponse(status_code=401, content={"message": str(e)})
                claims['tenant_id'] = tenant_id  # tenant_id is already set above with default "T0000"
                tenant_context.set(tenant_id)
                request.state.user = claims  

                if any(request.url.path.startswith(path) for path in [ #unblocking any super route with credits check. Put any route that doesn't need credits check here
                    "/api/manage"
                ]):
                    response = await call_next(request)
                    return response
                else:
                    if request.method == "GET":
                        return await call_next(request)
                    if await check_free_credit_limits_crossed(tenant_id, claims):
                        return CORSJSONResponse(
                            status_code=402,
                            content = {"detail": "Free Credits Used Up."}
                        )

                    response = await call_next(request)
                    return response
            else:
                return JSONResponse(status_code=401, content={"message": "Authorization header is missing."})
        else:
            response = await call_next(request)
            return response
    
    # _app.include_router(websocket_router)
    
    # Move code query routes to separate app
    _app.include_router(
        code_query.router,
        prefix="/api",
        dependencies=dependencies
    )
    
    
    return _app

def get_complete_application():
    """In production need to start and close db connection and some middleware."""
    _app = create_code_query_app()
    # _app.add_event_handler("startup", connect_llm)
    _app.add_event_handler("startup", connect_db)
    _app.add_event_handler("startup", connect_node_db)
    _app.add_event_handler("startup", connect_vector_db)
    _app.add_event_handler("startup", lambda: get_mongo_db(collection_name='tasks'))
    return _app

app = get_complete_application()

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001, )
    logging.info("Uvicorn server started") 